<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width initial-scale=1" />

    <!--<link
      href="https://fonts.loli.net/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext"
      rel="stylesheet"
      type="text/css"
    />-->
    <style type="text/css">
      html {
        overflow-x: initial !important;
      }
      :root {
        --bg-color: #ffffff;
        --text-color: #333333;
        --select-text-bg-color: #b5d6fc;
        --select-text-font-color: auto;
        --monospace: "<PERSON><PERSON> Console", Consolas, "Courier", monospace;
        --title-bar-height: 20px;
      }
      .mac-os-11 {
        --title-bar-height: 28px;
      }
      html {
        font-size: 14px;
        background-color: var(--bg-color);
        color: var(--text-color);
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
      }
      body {
        margin: 0px;
        padding: 0px;
        height: auto;
        bottom: 0px;
        top: 0px;
        left: 0px;
        right: 0px;
        font-size: 1rem;
        line-height: 1.42857143;
        overflow-x: hidden;
        background-image: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        background-clip: inherit;
        background-color: inherit;
        tab-size: 4;
        background-position: inherit inherit;
        background-repeat: inherit inherit;
      }
      iframe {
        margin: auto;
      }
      a.url {
        word-break: break-all;
      }
      a:active,
      a:hover {
        outline: 0px;
      }
      .in-text-selection,
      ::selection {
        text-shadow: none;
        background: var(--select-text-bg-color);
        color: var(--select-text-font-color);
      }
      #write {
        margin: 0px auto;
        height: auto;
        width: inherit;
        word-break: normal;
        word-wrap: break-word;
        position: relative;
        white-space: normal;
        overflow-x: visible;
        padding-top: 36px;
      }
      #write.first-line-indent p {
        text-indent: 2em;
      }
      #write.first-line-indent li p,
      #write.first-line-indent p * {
        text-indent: 0px;
      }
      #write.first-line-indent li {
        margin-left: 2em;
      }
      .for-image #write {
        padding-left: 8px;
        padding-right: 8px;
      }
      body.typora-export {
        padding-left: 30px;
        padding-right: 30px;
      }
      .typora-export .footnote-line,
      .typora-export li,
      .typora-export p {
        white-space: pre-wrap;
      }
      .typora-export .task-list-item input {
        pointer-events: none;
      }
      @media screen and (max-width: 500px) {
        body.typora-export {
          padding-left: 0px;
          padding-right: 0px;
        }
        #write {
          padding-left: 20px;
          padding-right: 20px;
        }
        .CodeMirror-sizer {
          margin-left: 0px !important;
        }
        .CodeMirror-gutters {
          display: none !important;
        }
      }
      #write li > figure:last-child {
        margin-bottom: 0.5rem;
      }
      #write ol,
      #write ul {
        position: relative;
      }
      img {
        max-width: 100%;
        vertical-align: middle;
        image-orientation: from-image;
      }
      button,
      input,
      select,
      textarea {
        color: inherit;
        font-family: inherit;
        font-size: inherit;
        font-style: inherit;
        font-variant-caps: inherit;
        font-weight: inherit;
        font-stretch: inherit;
        line-height: inherit;
      }
      input[type="checkbox"],
      input[type="radio"] {
        line-height: normal;
        padding: 0px;
      }
      *,
      ::after,
      ::before {
        box-sizing: border-box;
      }
      #write h1,
      #write h2,
      #write h3,
      #write h4,
      #write h5,
      #write h6,
      #write p,
      #write pre {
        width: inherit;
      }
      #write h1,
      #write h2,
      #write h3,
      #write h4,
      #write h5,
      #write h6,
      #write p {
        position: relative;
      }
      p {
        line-height: inherit;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        break-after: avoid-page;
        break-inside: avoid;
        orphans: 4;
      }
      p {
        orphans: 4;
      }
      h1 {
        font-size: 2rem;
      }
      h2 {
        font-size: 1.8rem;
      }
      h3 {
        font-size: 1.6rem;
      }
      h4 {
        font-size: 1.4rem;
      }
      h5 {
        font-size: 1.2rem;
      }
      h6 {
        font-size: 1rem;
      }
      .md-math-block,
      .md-rawblock,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p {
        margin-top: 1rem;
        margin-bottom: 1rem;
      }
      .hidden {
        display: none;
      }
      .md-blockmeta {
        color: rgb(204, 204, 204);
        font-weight: 700;
        font-style: italic;
      }
      a {
        cursor: pointer;
      }
      sup.md-footnote {
        padding: 2px 4px;
        background-color: rgba(238, 238, 238, 0.7);
        color: rgb(85, 85, 85);
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
        cursor: pointer;
      }
      sup.md-footnote a,
      sup.md-footnote a:hover {
        color: inherit;
        text-transform: inherit;
        text-decoration: inherit;
      }
      #write input[type="checkbox"] {
        cursor: pointer;
        width: inherit;
        height: inherit;
      }
      figure {
        overflow-x: auto;
        margin: 1.2em 0px;
        max-width: calc(100% + 16px);
        padding: 0px;
      }
      figure > table {
        margin: 0px;
      }
      tr {
        break-inside: avoid;
        break-after: auto;
      }
      thead {
        display: table-header-group;
      }
      table {
        border-collapse: collapse;
        border-spacing: 0px;
        width: 100%;
        overflow: auto;
        break-inside: auto;
        text-align: left;
      }
      table.md-table td {
        min-width: 32px;
      }
      .CodeMirror-gutters {
        border-right-width: 0px;
        background-color: inherit;
      }
      .CodeMirror-linenumber {
      }
      .CodeMirror {
        text-align: left;
      }
      .CodeMirror-placeholder {
        opacity: 0.3;
      }
      .CodeMirror pre {
        padding: 0px 4px;
      }
      .CodeMirror-lines {
        padding: 0px;
      }
      div.hr:focus {
        cursor: none;
      }
      #write pre {
        white-space: pre-wrap;
      }
      #write.fences-no-line-wrapping pre {
        white-space: pre;
      }
      #write pre.ty-contain-cm {
        white-space: normal;
      }
      .CodeMirror-gutters {
        margin-right: 4px;
      }
      .md-fences {
        font-size: 0.9rem;
        display: block;
        break-inside: avoid;
        text-align: left;
        overflow: visible;
        white-space: pre;
        background-image: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        background-clip: inherit;
        background-color: inherit;
        position: relative !important;
        background-position: inherit inherit;
        background-repeat: inherit inherit;
      }
      .md-fences-adv-panel {
        width: 100%;
        margin-top: 10px;
        text-align: center;
        padding-top: 0px;
        padding-bottom: 8px;
        overflow-x: auto;
      }
      #write .md-fences.mock-cm {
        white-space: pre-wrap;
      }
      .md-fences.md-fences-with-lineno {
        padding-left: 0px;
      }
      #write.fences-no-line-wrapping .md-fences.mock-cm {
        white-space: pre;
        overflow-x: auto;
      }
      .md-fences.mock-cm.md-fences-with-lineno {
        padding-left: 8px;
      }
      .CodeMirror-line,
      twitterwidget {
        break-inside: avoid;
      }
      .footnotes {
        opacity: 0.8;
        font-size: 0.9rem;
        margin-top: 1em;
        margin-bottom: 1em;
      }
      .footnotes + .footnotes {
        margin-top: 0px;
      }
      .md-reset {
        margin: 0px;
        padding: 0px;
        border: 0px;
        outline: 0px;
        vertical-align: top;
        text-decoration: none;
        text-shadow: none;
        float: none;
        position: static;
        width: auto;
        height: auto;
        white-space: nowrap;
        cursor: inherit;
        line-height: normal;
        font-weight: 400;
        text-align: left;
        box-sizing: content-box;
        direction: ltr;
        background-position: 0px 0px;
        background-repeat: initial initial;
      }
      li div {
        padding-top: 0px;
      }
      blockquote {
        margin: 1rem 0px;
      }
      li .mathjax-block,
      li p {
        margin: 0.5rem 0px;
      }
      li blockquote {
        margin: 1rem 0px;
      }
      li {
        margin: 0px;
        position: relative;
      }
      blockquote > :last-child {
        margin-bottom: 0px;
      }
      blockquote > :first-child,
      li > :first-child {
        margin-top: 0px;
      }
      .footnotes-area {
        color: rgb(136, 136, 136);
        margin-top: 0.714rem;
        padding-bottom: 0.143rem;
        white-space: normal;
      }
      #write .footnote-line {
        white-space: pre-wrap;
      }
      @media print {
        body,
        html {
          border: 1px solid transparent;
          height: 99%;
          break-after: avoid;
          break-before: avoid;
          font-variant-ligatures: no-common-ligatures;
        }
        #write {
          margin-top: 0px;
          padding-top: 0px;
          border-color: transparent !important;
        }
        .typora-export * {
          -webkit-print-color-adjust: exact;
        }
        .typora-export #write {
          break-after: avoid;
        }
        .typora-export #write::after {
          height: 0px;
        }
        .is-mac table {
          break-inside: avoid;
        }
        .typora-export-show-outline .typora-export-sidebar {
          display: none;
        }
      }
      .footnote-line {
        margin-top: 0.714em;
        font-size: 0.7em;
      }
      a img,
      img a {
        cursor: pointer;
      }
      pre.md-meta-block {
        font-size: 0.8rem;
        min-height: 0.8rem;
        white-space: pre-wrap;
        background-color: rgb(204, 204, 204);
        display: block;
        overflow-x: hidden;
        background-position: initial initial;
        background-repeat: initial initial;
      }
      p > .md-image:only-child:not(.md-img-error) img,
      p > img:only-child {
        display: block;
        margin: auto;
      }
      #write.first-line-indent p > .md-image:only-child:not(.md-img-error) img {
        left: -2em;
        position: relative;
      }
      p > .md-image:only-child {
        display: inline-block;
        width: 100%;
      }
      #write .MathJax_Display {
        margin: 0.8em 0px 0px;
      }
      .md-math-block {
        width: 100%;
      }
      .md-math-block:not(:empty)::after {
        display: none;
      }
      .MathJax_ref {
        fill: currentcolor;
      }
      [contenteditable="true"]:active,
      [contenteditable="true"]:focus,
      [contenteditable="false"]:active,
      [contenteditable="false"]:focus {
        outline: 0px;
        box-shadow: none;
      }
      .md-task-list-item {
        position: relative;
        list-style-type: none;
      }
      .task-list-item.md-task-list-item {
        padding-left: 0px;
      }
      .md-task-list-item > input {
        position: absolute;
        top: 0px;
        left: 0px;
        margin-left: -1.2em;
        margin-top: calc(1em - 10px);
        border: none;
      }
      .math {
        font-size: 1rem;
      }
      .md-toc {
        min-height: 3.58rem;
        position: relative;
        font-size: 0.9rem;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
      }
      .md-toc-content {
        position: relative;
        margin-left: 0px;
      }
      .md-toc-content::after,
      .md-toc::after {
        display: none;
      }
      .md-toc-item {
        display: block;
        color: rgb(65, 131, 196);
      }
      .md-toc-item a {
        text-decoration: none;
      }
      .md-toc-inner:hover {
        text-decoration: underline;
      }
      .md-toc-inner {
        display: inline-block;
        cursor: pointer;
      }
      .md-toc-h1 .md-toc-inner {
        margin-left: 0px;
        font-weight: 700;
      }
      .md-toc-h2 .md-toc-inner {
        margin-left: 2em;
      }
      .md-toc-h3 .md-toc-inner {
        margin-left: 4em;
      }
      .md-toc-h4 .md-toc-inner {
        margin-left: 6em;
      }
      .md-toc-h5 .md-toc-inner {
        margin-left: 8em;
      }
      .md-toc-h6 .md-toc-inner {
        margin-left: 10em;
      }
      @media screen and (max-width: 48em) {
        .md-toc-h3 .md-toc-inner {
          margin-left: 3.5em;
        }
        .md-toc-h4 .md-toc-inner {
          margin-left: 5em;
        }
        .md-toc-h5 .md-toc-inner {
          margin-left: 6.5em;
        }
        .md-toc-h6 .md-toc-inner {
          margin-left: 8em;
        }
      }
      a.md-toc-inner {
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
        line-height: inherit;
      }
      .footnote-line a:not(.reversefootnote) {
        color: inherit;
      }
      .md-attr {
        display: none;
      }
      .md-fn-count::after {
        content: ".";
      }
      code,
      pre,
      samp,
      tt {
        font-family: var(--monospace);
      }
      kbd {
        margin: 0px 0.1em;
        padding: 0.1em 0.6em;
        font-size: 0.8em;
        color: rgb(36, 39, 41);
        background-color: rgb(255, 255, 255);
        border: 1px solid rgb(173, 179, 185);
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border-bottom-left-radius: 3px;
        box-shadow: rgba(12, 13, 14, 0.2) 0px 1px 0px,
          rgb(255, 255, 255) 0px 0px 0px 2px inset;
        white-space: nowrap;
        vertical-align: middle;
        background-position: initial initial;
        background-repeat: initial initial;
      }
      .md-comment {
        color: rgb(162, 127, 3);
        opacity: 0.6;
        font-family: var(--monospace);
      }
      code {
        text-align: left;
      }
      a.md-print-anchor {
        white-space: pre !important;
        border: none !important;
        display: inline-block !important;
        position: absolute !important;
        width: 1px !important;
        right: 0px !important;
        outline: 0px !important;
        text-shadow: initial !important;
        background-position: 0px 0px !important;
        background-repeat: initial initial !important;
      }
      .os-windows.monocolor-emoji .md-emoji {
        font-family: "Segoe UI Symbol", sans-serif;
      }
      .md-diagram-panel > svg {
        max-width: 100%;
      }
      [lang="flow"] svg,
      [lang="mermaid"] svg {
        max-width: 100%;
        height: auto;
      }
      [lang="mermaid"] .node text {
        font-size: 1rem;
      }
      table tr th {
        border-bottom-width: 0px;
      }
      video {
        max-width: 100%;
        display: block;
        margin: 0px auto;
      }
      iframe {
        max-width: 100%;
        width: 100%;
        border: none;
      }
      .highlight td,
      .highlight tr {
        border: 0px;
      }
      mark {
        background-color: rgb(255, 255, 0);
        color: rgb(0, 0, 0);
        background-position: initial initial;
        background-repeat: initial initial;
      }
      .md-html-inline .md-plain,
      .md-html-inline strong,
      mark .md-inline-math,
      mark strong {
        color: inherit;
      }
      .md-expand mark .md-meta {
        opacity: 0.3 !important;
      }
      mark .md-meta {
        color: rgb(0, 0, 0);
      }
      @media print {
        .typora-export h1,
        .typora-export h2,
        .typora-export h3,
        .typora-export h4,
        .typora-export h5,
        .typora-export h6 {
          break-inside: avoid;
        }
      }
      .md-diagram-panel .messageText {
        stroke: none !important;
      }
      .md-diagram-panel .start-state {
        fill: var(--node-fill);
      }
      .md-diagram-panel .edgeLabel rect {
        opacity: 1 !important;
      }
      .md-fences.md-fences-math {
        font-size: 1em;
      }
      .md-fences-advanced:not(.md-focus) {
        padding: 0px;
        white-space: nowrap;
        border: 0px;
      }
      .md-fences-advanced:not(.md-focus) {
        background-image: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        background-clip: inherit;
        background-color: inherit;
        background-position: inherit inherit;
        background-repeat: inherit inherit;
      }
      .typora-export-show-outline .typora-export-content {
        max-width: 1440px;
        margin: auto;
        display: flex;
        flex-direction: row;
      }
      .typora-export-sidebar {
        width: 300px;
        font-size: 0.8rem;
        margin-top: 80px;
        margin-right: 18px;
      }
      .typora-export-show-outline #write {
        --webkit-flex: 2;
        flex: 2 1 0%;
      }
      .typora-export-sidebar .outline-content {
        position: fixed;
        top: 0px;
        max-height: 100%;
        overflow: hidden auto;
        padding-bottom: 30px;
        padding-top: 60px;
        width: 300px;
      }
      @media screen and (max-width: 1024px) {
        .typora-export-sidebar,
        .typora-export-sidebar .outline-content {
          width: 240px;
        }
      }
      @media screen and (max-width: 800px) {
        .typora-export-sidebar {
          display: none;
        }
      }
      .outline-content li,
      .outline-content ul {
        margin-left: 0px;
        margin-right: 0px;
        padding-left: 0px;
        padding-right: 0px;
        list-style: none;
      }
      .outline-content ul {
        margin-top: 0px;
        margin-bottom: 0px;
      }
      .outline-content strong {
        font-weight: 400;
      }
      .outline-expander {
        width: 1rem;
        height: 1.428571429rem;
        position: relative;
        display: table-cell;
        vertical-align: middle;
        cursor: pointer;
        padding-left: 4px;
      }
      .outline-expander::before {
        content: "";
        position: relative;
        font-family: Ionicons;
        display: inline-block;
        font-size: 8px;
        vertical-align: middle;
      }
      .outline-item {
        padding-top: 3px;
        padding-bottom: 3px;
        cursor: pointer;
      }
      .outline-expander:hover::before {
        content: "";
      }
      .outline-h1 > .outline-item {
        padding-left: 0px;
      }
      .outline-h2 > .outline-item {
        padding-left: 1em;
      }
      .outline-h3 > .outline-item {
        padding-left: 2em;
      }
      .outline-h4 > .outline-item {
        padding-left: 3em;
      }
      .outline-h5 > .outline-item {
        padding-left: 4em;
      }
      .outline-h6 > .outline-item {
        padding-left: 5em;
      }
      .outline-label {
        cursor: pointer;
        display: table-cell;
        vertical-align: middle;
        text-decoration: none;
        color: inherit;
      }
      .outline-label:hover {
        text-decoration: underline;
      }
      .outline-item:hover {
        border-color: rgb(245, 245, 245);
        background-color: var(--item-hover-bg-color);
      }
      .outline-item:hover {
        margin-left: -28px;
        margin-right: -28px;
        border-left-width: 28px;
        border-left-style: solid;
        border-left-color: transparent;
        border-right-width: 28px;
        border-right-style: solid;
        border-right-color: transparent;
      }
      .outline-item-single .outline-expander::before,
      .outline-item-single .outline-expander:hover::before {
        display: none;
      }
      .outline-item-open > .outline-item > .outline-expander::before {
        content: "";
      }
      .outline-children {
        display: none;
      }
      .info-panel-tab-wrapper {
        display: none;
      }
      .outline-item-open > .outline-children {
        display: block;
      }
      .typora-export .outline-item {
        padding-top: 1px;
        padding-bottom: 1px;
      }
      .typora-export .outline-item:hover {
        margin-right: -8px;
        border-right-width: 8px;
        border-right-style: solid;
        border-right-color: transparent;
      }
      .typora-export .outline-expander::before {
        content: "+";
        font-family: inherit;
        top: -1px;
      }
      .typora-export .outline-expander:hover::before,
      .typora-export
        .outline-item-open
        > .outline-item
        > .outline-expander::before {
        content: "−";
      }
      .typora-export-collapse-outline .outline-children {
        display: none;
      }
      .typora-export-collapse-outline .outline-item-open > .outline-children,
      .typora-export-no-collapse-outline .outline-children {
        display: block;
      }
      .typora-export-no-collapse-outline .outline-expander::before {
        content: "" !important;
      }
      .typora-export-show-outline
        .outline-item-active
        > .outline-item
        .outline-label {
        font-weight: 700;
      }
      .md-inline-math-container mjx-container {
        zoom: 0.95;
      }

      :root {
        --side-bar-bg-color: #fafafa;
        --control-text-color: #777;
      }

      /*@include-when-export url(https://fonts.loli.net/css?family=Open+Sans:400italic,700italic,700,400&subset=latin,latin-ext);*/

      /* open-sans-regular - latin-ext_latin */
      /* open-sans-italic - latin-ext_latin */
      /* open-sans-700 - latin-ext_latin */
      /* open-sans-700italic - latin-ext_latin */
      html {
        font-size: 16px;
        -webkit-font-smoothing: antialiased;
      }

      body {
        font-family: "Open Sans", "Clear Sans", "Helvetica Neue", Helvetica,
          Arial, "Segoe UI Emoji", sans-serif;
        color: rgb(51, 51, 51);
        line-height: 1.6;
      }

      #write {
        max-width: 860px;
        margin: 0 auto;
        padding: 30px;
        padding-bottom: 100px;
      }

      @media only screen and (min-width: 1400px) {
        #write {
          max-width: 1024px;
        }
      }

      @media only screen and (min-width: 1800px) {
        #write {
          max-width: 1200px;
        }
      }

      #write > ul:first-child,
      #write > ol:first-child {
        margin-top: 30px;
      }

      a {
        color: #4183c4;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 1rem;
        font-weight: bold;
        line-height: 1.4;
        cursor: text;
      }
      h1:hover a.anchor,
      h2:hover a.anchor,
      h3:hover a.anchor,
      h4:hover a.anchor,
      h5:hover a.anchor,
      h6:hover a.anchor {
        text-decoration: none;
      }
      h1 tt,
      h1 code {
        font-size: inherit;
      }
      h2 tt,
      h2 code {
        font-size: inherit;
      }
      h3 tt,
      h3 code {
        font-size: inherit;
      }
      h4 tt,
      h4 code {
        font-size: inherit;
      }
      h5 tt,
      h5 code {
        font-size: inherit;
      }
      h6 tt,
      h6 code {
        font-size: inherit;
      }
      h1 {
        font-size: 2.25em;
        line-height: 1.2;
        border-bottom: 1px solid #eee;
      }
      h2 {
        font-size: 1.75em;
        line-height: 1.225;
        border-bottom: 1px solid #eee;
      }

      /*@media print {
    .typora-export h1,
    .typora-export h2 {
        border-bottom: none;
        padding-bottom: initial;
    }

    .typora-export h1::after,
    .typora-export h2::after {
        content: "";
        display: block;
        height: 100px;
        margin-top: -96px;
        border-top: 1px solid #eee;
    }
}*/

      h3 {
        font-size: 1.5em;
        line-height: 1.43;
      }
      h4 {
        font-size: 1.25em;
      }
      h5 {
        font-size: 1em;
      }
      h6 {
        font-size: 1em;
        color: #777;
      }
      p,
      blockquote,
      ul,
      ol,
      dl,
      table {
        margin: 0.8em 0;
      }
      li > ol,
      li > ul {
        margin: 0 0;
      }
      hr {
        height: 2px;
        padding: 0;
        margin: 16px 0;
        background-color: #e7e7e7;
        border: 0 none;
        overflow: hidden;
        box-sizing: content-box;
      }

      li p.first {
        display: inline-block;
      }
      ul,
      ol {
        padding-left: 30px;
      }
      ul:first-child,
      ol:first-child {
        margin-top: 0;
      }
      ul:last-child,
      ol:last-child {
        margin-bottom: 0;
      }
      blockquote {
        border-left: 4px solid #dfe2e5;
        padding: 0 15px;
        color: #777777;
      }
      blockquote blockquote {
        padding-right: 0;
      }
      table {
        padding: 0;
        word-break: initial;
      }
      table tr {
        border: 1px solid #dfe2e5;
        margin: 0;
        padding: 0;
      }
      table tr:nth-child(2n),
      thead {
        background-color: #f8f8f8;
      }
      table th {
        font-weight: bold;
        border: 1px solid #dfe2e5;
        border-bottom: 0;
        margin: 0;
        padding: 6px 13px;
      }
      table td {
        border: 1px solid #dfe2e5;
        margin: 0;
        padding: 6px 13px;
      }
      table th:first-child,
      table td:first-child {
        margin-top: 0;
      }
      table th:last-child,
      table td:last-child {
        margin-bottom: 0;
      }

      .CodeMirror-lines {
        padding-left: 4px;
      }

      .code-tooltip {
        box-shadow: 0 1px 1px 0 rgba(0, 28, 36, 0.3);
        border-top: 1px solid #eef2f2;
      }

      .md-fences,
      code,
      tt {
        border: 1px solid #e7eaed;
        background-color: #f8f8f8;
        border-radius: 3px;
        padding: 0;
        padding: 2px 4px 0px 4px;
        font-size: 0.9em;
      }

      code {
        background-color: #f3f4f4;
        padding: 0 2px 0 2px;
      }

      .md-fences {
        margin-bottom: 15px;
        margin-top: 15px;
        padding-top: 8px;
        padding-bottom: 6px;
      }

      .md-task-list-item > input {
        margin-left: -1.3em;
      }

      @media print {
        html {
          font-size: 13px;
        }
        table,
        pre {
          page-break-inside: avoid;
        }
        pre {
          word-wrap: break-word;
        }
      }

      .md-fences {
        background-color: #f8f8f8;
      }
      #write pre.md-meta-block {
        padding: 1rem;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f7f7f7;
        border: 0;
        border-radius: 3px;
        color: #777777;
        margin-top: 0 !important;
      }

      .mathjax-block > .code-tooltip {
        bottom: 0.375rem;
      }

      .md-mathjax-midline {
        background: #fafafa;
      }

      #write > h3.md-focus:before {
        left: -1.5625rem;
        top: 0.375rem;
      }
      #write > h4.md-focus:before {
        left: -1.5625rem;
        top: 0.285714286rem;
      }
      #write > h5.md-focus:before {
        left: -1.5625rem;
        top: 0.285714286rem;
      }
      #write > h6.md-focus:before {
        left: -1.5625rem;
        top: 0.285714286rem;
      }
      .md-image > .md-meta {
        /*border: 1px solid #ddd;*/
        border-radius: 3px;
        padding: 2px 0px 0px 4px;
        font-size: 0.9em;
        color: inherit;
      }

      .md-tag {
        color: #a7a7a7;
        opacity: 1;
      }

      .md-toc {
        margin-top: 20px;
        padding-bottom: 20px;
      }

      .sidebar-tabs {
        border-bottom: none;
      }

      #typora-quick-open {
        border: 1px solid #ddd;
        background-color: #f8f8f8;
      }

      #typora-quick-open-item {
        background-color: #fafafa;
        border-color: #fefefe #e5e5e5 #e5e5e5 #eee;
        border-style: solid;
        border-width: 1px;
      }

      /** focus mode */
      .on-focus-mode blockquote {
        border-left-color: rgba(85, 85, 85, 0.12);
      }

      header,
      .context-menu,
      .megamenu-content,
      footer {
        font-family: "Segoe UI", "Arial", sans-serif;
      }

      .file-node-content:hover .file-node-icon,
      .file-node-content:hover .file-node-open-state {
        visibility: visible;
      }

      .mac-seamless-mode #typora-sidebar {
        background-color: #fafafa;
        background-color: var(--side-bar-bg-color);
      }

      .md-lang {
        color: #b4654d;
      }

      /*.html-for-mac {
    --item-hover-bg-color: #E6F0FE;
}*/

      #md-notification .btn {
        border: 0;
      }

      .dropdown-menu .divider {
        border-color: #e5e5e5;
        opacity: 0.4;
      }

      .ty-preferences .window-content {
        background-color: #fafafa;
      }

      .ty-preferences .nav-group-item.active {
        color: white;
        background: #999;
      }

      .menu-item-container a.menu-style-btn {
        background-color: #f5f8fa;
        background-image: linear-gradient(
          180deg,
          hsla(0, 0%, 100%, 0.8),
          hsla(0, 0%, 100%, 0)
        );
      }

      @media print {
        @page {
          margin: 0 0 0 0;
        }
        body.typora-export {
          padding-left: 0;
          padding-right: 0;
        }
        #write {
          padding: 0;
        }
      }
      .typora-export li,
      .typora-export p,
      .typora-export,
      .footnote-line {
        white-space: normal;
      }
    </style>
    <title>URL And Website Whitelist</title>
    <link rel="icon" href="./favicon.ico" type="image/x-icon"/>
  </head>
  <body class="typora-export">
    <div class="typora-export-content">
      <div id="write" class="">
        <h2 id="url-and-website-whitelist">
          <span>URL And Website Whitelist</span>
        </h2>
        <p>
          <strong><span>Last updated</span></strong
          ><span> </span><strong><span>August 09, 2024</span></strong>
        </p>
        <p>
          <span
            >In order to resolve a complaint regarding the Services or to
            receive further information regarding use of the Services, please
            contact us at: </span
          ><a href="mailto:<EMAIL>" target="_blank" class="url"
            ><EMAIL></a
          ><span> or our </span
          ><a href="https://discord.gg/F33mhsAqqg"  target='_blank'><span>Discord</span></a
          ><span>.</span>
        </p>
        <ul>
          <li>
            <a href="https://arxiv.org" target="_blank" class="url"
              >https://arxiv.org</a
            >
          </li>
          <li>
            <a href="https://www.ncbi.nlm.nih.gov" target="_blank" class="url"
              >https://www.ncbi.nlm.nih.gov</a
            >
          </li>
          <li>
            <a href="https://scholar.google.com" target="_blank" class="url"
              >https://scholar.google.com</a
            >
          </li>
          <li>
            <a href="https://doaj.org" target="_blank" class="url"
              >https://doaj.org</a
            >
          </li>
          <li>
            <a href="https://ndltd.org" target="_blank" class="url"
              >https://ndltd.org</a
            >
          </li>
          <li>
            <a
              href="https://nap.nationalacademies.org"
              target="_blank"
              class="url"
              >https://nap.nationalacademies.org</a
            >
          </li>
          <li>
            <a href="https://www.base-search.net" target="_blank" class="url"
              >https://www.base-search.net</a
            >
          </li>
          <li>
            <a
              href="http://digital.library.upenn.edu"
              target="_blank"
              class="url"
              >http://digital.library.upenn.edu</a
            >
          </li>
          <li>
            <a
              href="https://library.harvard.edu/digital-collections"
              target="_blank"
              class="url"
              >https://library.harvard.edu/digital-collections</a
            >
          </li>
          <li>
            <a
              href="http://digitalcollections.library.yale.edu"
              target="_blank"
              class="url"
              >http://digitalcollections.library.yale.edu</a
            >
          </li>
          <li>
            <a href="https://lib.msu.edu" target="_blank" class="url"
              >https://lib.msu.edu</a
            >
          </li>
          <li>
            <a
              href="https://earchives.lib.purdue.edu"
              target="_blank"
              class="url"
              >https://earchives.lib.purdue.edu</a
            >
          </li>
          <li>
            <a
              href="https://rucore.libraries.rutgers.edu"
              target="_blank"
              class="url"
              >https://rucore.libraries.rutgers.edu</a
            >
          </li>
          <li>
            <a
              href="https://library.osu.edu/find/collections"
              target="_blank"
              class="url"
              >https://library.osu.edu/find/collections</a
            >
          </li>
          <li>
            <a href="https://library.syracuse.edu" target="_blank" class="url"
              >https://library.syracuse.edu</a
            >
          </li>
          <li>
            <a href="https://library.brown.edu" target="_blank" class="url"
              >https://library.brown.edu</a
            >
          </li>
          <li>
            <a href="https://www.jsu.edu/library" target="_blank" class="url"
              >https://www.jsu.edu/library</a
            >
          </li>
          <li>
            <a href="http://ota.ahds.ac.uk" target="_blank" class="url"
              >http://ota.ahds.ac.uk</a
            >
          </li>
          <li>
            <a href="https://ocw.mit.edu" target="_blank" class="url"
              >https://ocw.mit.edu</a
            >
          </li>
          <li>
            <a
              href="https://digital.library.sydney.edu.au"
              target="_blank"
              class="url"
              >https://digital.library.sydney.edu.au</a
            >
          </li>
          <li>
            <a
              href="https://catalogue.curtin.edu.au"
              target="_blank"
              class="url"
              >https://catalogue.curtin.edu.au</a
            >
          </li>
          <li>
            <a href="https://www.ice.com" target="_blank" class="url"
              >https://www.ice.com</a
            >
          </li>
          <li>
            <a href="https://www.nasdaq.com" target="_blank" class="url"
              >https://www.nasdaq.com</a
            >
          </li>
          <li>
            <a
              href="https://www.londonstockexchange.com"
              target="_blank"
              class="url"
              >https://www.londonstockexchange.com</a
            >
          </li>
          <li>
            <a href="https://www.tsx.com" target="_blank" class="url"
              >https://www.tsx.com</a
            >
          </li>
          <li>
            <a href="https://www.nasdaqomx.com" target="_blank" class="url"
              >https://www.nasdaqomx.com</a
            >
          </li>
          <li>
            <a href="https://www.euronext.com" target="_blank" class="url"
              >https://www.euronext.com</a
            >
          </li>
          <li>
            <a href="https://www.nsx.com.au" target="_blank" class="url"
              >https://www.nsx.com.au</a
            >
          </li>
          <li>
            <a href="https://www.six-group.com" target="_blank" class="url"
              >https://www.six-group.com</a
            >
          </li>
          <li>
            <a href="https://www.dbag.com" target="_blank" class="url"
              >https://www.dbag.com</a
            >
          </li>
          <li>
            <a
              href="https://www.deutsche-boerse.com"
              target="_blank"
              class="url"
              >https://www.deutsche-boerse.com</a
            >
          </li>
          <li>
            <a href="http://www.sse.com.cn" target="_blank" class="url"
              >http://www.sse.com.cn</a
            >
          </li>
          <li>
            <a href="http://www.szse.cn" target="_blank" class="url"
              >http://www.szse.cn</a
            >
          </li>
          <li>
            <a href="https://www.bse.cn" target="_blank" class="url"
              >https://www.bse.cn</a
            >
          </li>
          <li>
            <a href="https://www.twse.com.tw" target="_blank" class="url"
              >https://www.twse.com.tw</a
            >
          </li>
          <li>
            <a href="https://www.bseindia.com" target="_blank" class="url"
              >https://www.bseindia.com</a
            >
          </li>
          <li>
            <a href="https://www.nseindia.com" target="_blank" class="url"
              >https://www.nseindia.com</a
            >
          </li>
          <li>
            <a href="https://global.krx.co.kr" target="_blank" class="url"
              >https://global.krx.co.kr</a
            >
          </li>
          <li>
            <a href="https://www.jpx.co.jp" target="_blank" class="url"
              >https://www.jpx.co.jp</a
            >
          </li>
          <li>
            <a href="https://www.b3.com.br" target="_blank" class="url"
              >https://www.b3.com.br</a
            >
          </li>
          <li>
            <a href="https://www.jse.co.za" target="_blank" class="url"
              >https://www.jse.co.za</a
            >
          </li>
          <li>
            <a href="https://www.sec.gov" target="_blank" class="url"
              >https://www.sec.gov</a
            >
          </li>
          <li>
            <a href="https://www.uscourts.gov" target="_blank" class="url"
              >https://www.uscourts.gov</a
            >
          </li>
          <li>
            <a href="https://www.whitehouse.gov" target="_blank" class="url"
              >https://www.whitehouse.gov</a
            >
          </li>
          <li>
            <a href="https://www.wto.org" target="_blank" class="url"
              >https://www.wto.org</a
            >
          </li>
          <li>
            <a href="https://www.who.int" target="_blank" class="url"
              >https://www.who.int</a
            >
          </li>
          <li>
            <a href="https://www.un.org" target="_blank" class="url"
              >https://www.un.org</a
            >
          </li>
          <li>
            <a href="https://www.state.gov" target="_blank" class="url"
              >https://www.state.gov</a
            >
          </li>
          <li>
            <a href="https://dodiac.dtic.mil" target="_blank" class="url"
              >https://dodiac.dtic.mil</a
            >
          </li>
          <li>
            <a href="https://www.uscourts.gov" target="_blank" class="url"
              >https://www.uscourts.gov</a
            >
          </li>
          <li>
            <a href="https://curia.europa.eu" target="_blank" class="url"
              >https://curia.europa.eu</a
            >
          </li>
          <li>
            <a href="http://docjax.com" target="_blank" class="url"
              >http://docjax.com</a
            >
          </li>
          <li>
            <a href="https://cnki.net" target="_blank" class="url"
              >https://cnki.net</a
            >
          </li>
          <li>
            <a href="http://pubmed.cn" target="_blank" class="url"
              >http://pubmed.cn</a
            >
          </li>
          <li>
            <a href="https://www.wanfangdata.com.cn" target="_blank" class="url"
              >https://www.wanfangdata.com.cn</a
            >
          </li>
          <li>
            <a href="https://so.hiqq.com.cn" target="_blank" class="url"
              >https://so.hiqq.com.cn</a
            >
          </li>
          <li>
            <a href="http://www.sciencedirect.com" target="_blank" class="url"
              >http://www.sciencedirect.com</a
            >
          </li>
          <li>
            <a href="https://www.oalib.com" target="_blank" class="url"
              >https://www.oalib.com</a
            >
          </li>
          <li>
            <a href="https://www.highwirepress.com" target="_blank" class="url"
              >https://www.highwirepress.com</a
            >
          </li>
          <li>
            <a href="https://www.infox-med.com" target="_blank" class="url"
              >https://www.infox-med.com</a
            >
          </li>
          <li>
            <a href="https://oalib.com" target="_blank" class="url"
              >https://oalib.com</a
            >
          </li>
          <li>
            <a href="https://www.researchgate.net" target="_blank" class="url"
              >https://www.researchgate.net</a
            >
          </li>
          <li>
            <a href="https://www.sciencedirect.com" target="_blank" class="url"
              >https://www.sciencedirect.com</a
            >
          </li>
          <li>
            <a href="https://link.springer.com" target="_blank" class="url"
              >https://link.springer.com</a
            >
          </li>
          <li>
            <a
              href="https://onlinelibrary.wiley.com"
              target="_blank"
              class="url"
              >https://onlinelibrary.wiley.com</a
            >
          </li>
          <li>
            <a href="https://www.jstor.org" target="_blank" class="url"
              >https://www.jstor.org</a
            >
          </li>
          <li>
            <a
              href="https://pubmed.ncbi.nlm.nih.gov"
              target="_blank"
              class="url"
              >https://pubmed.ncbi.nlm.nih.gov</a
            >
          </li>
          <li>
            <a href="https://arxiv.org" target="_blank" class="url"
              >https://arxiv.org</a
            >
          </li>
          <li>
            <a href="https://ieeexplore.ieee.org" target="_blank" class="url"
              >https://ieeexplore.ieee.org</a
            >
          </li>
          <li>
            <a href="https://dl.acm.org" target="_blank" class="url"
              >https://dl.acm.org</a
            >
          </li>
          <li>
            <a href="https://ydzk.chineselaw.com" target="_blank" class="url"
            >https://ydzk.chineselaw.com</a
            >
          </li>
          <li>
            <a href="https://www.buch-der-synergie.de" target="_blank" class="url"
            >https://www.buch-der-synergie.de</a
            >
          </li>
          <li>
            <a href="https://plato.stanford.edu" target="_blank" class="url"
            >https://plato.stanford.edu</a
            >
          </li>
          <li>
            <a href="https://www.sergroup.com" target="_blank" class="url"
            >https://www.sergroup.com</a
            >
          </li>
          <li>
            <a href="https://edisciplinas.usp.br" target="_blank" class="url"
            >https://edisciplinas.usp.br</a
            >
          </li>
          <li>
            <a href="https://psyarxiv.com" target="_blank" class="url"
            >https://psyarxiv.com</a
            >
          </li>
          <li>
            <a href="https://support.castandcrew.com" target="_blank" class="url"
            >https://support.castandcrew.com</a
            >
          </li>
          <li>
            <a href="http://www.doxis.com" target="_blank" class="url"
            >http://www.doxis.com</a
            >
          </li>
          <li>
            <a href="https://www.biorxiv.org" target="_blank" class="url"
            >https://www.biorxiv.org</a
            >
          </li>
          <li>
            <a href="https://www.marxists.org" target="_blank" class="url"
            >https://www.marxists.org</a
            >
          </li>
          <li>
            <a href="https://www.tdcommons.org" target="_blank" class="url"
            >https://www.tdcommons.org</a
            >
          </li>
        </ul>
      </div>
    </div>
  </body>
</html>
