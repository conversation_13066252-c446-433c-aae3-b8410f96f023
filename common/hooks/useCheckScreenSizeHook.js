import { useState } from 'react';
import { SCREEN_SIZE_WIDTH } from '../data/constant';
import { useHandleResize } from './useHandleResizeHook';

export function useCheckScreenSize() {
  const [isMiniScreenSize, setIsMiniScreenSize] = useState(false);

  const getIsMiniScreenSize = () => {
    if (window.innerWidth <= SCREEN_SIZE_WIDTH.MINI_SCREEN_SIZE) {
      setIsMiniScreenSize(true);
    } else {
      setIsMiniScreenSize(false);
    }
  };

  useHandleResize(() => {
    getIsMiniScreenSize();
  });

  return { isMiniScreenSize };
}
