import { useState, useEffect, useRef, useCallback } from 'react';

export function useCountdown(seconds = 60) {
  const timer = useRef();
  const [countdown, setCountdown] = useState();
  const [isCountdowning, setIsCountdowning] = useState(false);

  const clearTimer = () => {
    if (timer.current) {
      clearInterval(timer.current);
      timer.current = null;
    }
  };

  const startCountdown = useCallback(() => {
    setCountdown(seconds);
    setIsCountdowning(true);
  }, [seconds]);

  const stopCountdown = useCallback(() => {
    clearTimer();
    setIsCountdowning(false);
    setCountdown();
  }, []);

  useEffect(() => {
    if (countdown === seconds) {
      clearTimer();
      timer.current = setInterval(() => {
        setCountdown((countdown) => --countdown);
      }, 1e3);
    } else if (countdown <= 0) {
      stopCountdown();
    }
  }, [countdown, seconds, stopCountdown]);

  useEffect(() => {
    return () => {
      stopCountdown();
    };
  }, [stopCountdown]);

  return {
    countdown,
    isCountdowning,
    startCountdown,
    stopCountdown,
  };
}
