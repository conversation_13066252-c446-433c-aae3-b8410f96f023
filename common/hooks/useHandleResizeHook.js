import { useEffect } from 'react';
import _ from 'lodash';

export function useHandleResize(handleResizeFunction, dependencyList = []) {
  const handleResize = _.throttle(
    () => {
      handleResizeFunction();
    },
    1000 / 60,
    { leading: false, trailing: true },
  );

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencyList);
}
