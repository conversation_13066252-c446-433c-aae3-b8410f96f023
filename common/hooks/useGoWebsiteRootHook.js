import { useState, useEffect } from 'react';

export function useGoWebsiteRoot() {
  const [currentHref, setCurrentHref] = useState();

  useEffect(() => {
    if (!currentHref) {
      return;
    }
    if (window.location.pathname === currentHref) return;
    window.location.href = currentHref;
  }, [currentHref]);

  const goWebsiteRoot = (url) => {
    setCurrentHref(url || '/');
    const isFullPath = url?.startsWith('http');
    const isRootPath = url?.startsWith('/');
    if (url && !isFullPath && !isRootPath) {
      setCurrentHref(`/${url}`);
    }
  };

  return goWebsiteRoot;
}
