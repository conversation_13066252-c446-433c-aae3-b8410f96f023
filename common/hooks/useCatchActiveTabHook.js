import { useState, useEffect } from 'react';

export function useCatchActiveTab(defaultKey, sessionKey) {
  const [tabActived, setTabActived] = useState(defaultKey);
  useEffect(() => {
    const index = window.sessionStorage.getItem(sessionKey);
    if (index) {
      setTabActived(index);
      window.sessionStorage.removeItem(sessionKey);
    }
  }, [sessionKey]);

  const updateTabActived = (index) => {
    window.sessionStorage.setItem(sessionKey, index);
    setTabActived(index);
  };

  return {
    tabActived,
    updateTabActived,
  };
}
