import React, { useContext } from 'react';
// import { useDetectMobile } from './useDetectMobileHook';

export const MobilePageContext = React.createContext({});

export const useMobilePageContext = () => {
  // const isDetectMobile = useDetectMobile();
  const { isMobilePage } = useContext(MobilePageContext);
  // const isMobile = isMobilePage || isDetectMobile;
  const isMobile = isMobilePage;

  return isMobile;
};
