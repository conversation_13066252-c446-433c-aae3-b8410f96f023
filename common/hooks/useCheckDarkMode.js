import { useState, useEffect, useCallback } from 'react';

export function useCheckDarkMode() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const query = '(prefers-color-scheme: dark)';

  const handleChange = useCallback((e) => {
    setIsDarkMode(window.matchMedia(query).matches);
  }, []);

  useEffect(() => {
    setIsDarkMode(window.matchMedia(query).matches);
  }, []);

  useEffect(() => {
    window.matchMedia(query).addEventListener('change', handleChange);
    return () => {
      window.matchMedia(query).removeEventListener('change', handleChange);
    };
  }, [handleChange]);

  return isDarkMode;
}
