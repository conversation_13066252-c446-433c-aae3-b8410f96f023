import { useCallback, useEffect, useRef } from 'react';
import { useIdle } from '@uidotdev/usehooks';
import { updateActivityStatisticApi } from '../../api/chatdoc/activity';
import { STATISTIC_TYPE } from '../../data/chatdoc/data';
import { useGlobalContext } from '../useGlobalHook';
import { useGetUrlQueryData } from '../useUrlQueryHook';
import { useMobilePageContext } from '../useMobilePageContext';
import { useDocumentVisibility } from './useDocumentVisibility';

export const useChatDOCIdle = () => {
  const { defaultSrc } = useGlobalContext();
  const isMobile = useMobilePageContext();
  const urlSrc = useGetUrlQueryData('src');

  const idle = useIdle(30 * 60 * 1e3); // 30 分钟
  const visibility = useDocumentVisibility();

  let entryTime = useRef(Date.now());

  const handleStatisticDuration = useCallback(
    async (useBeacon = false) => {
      const leaveTime = Date.now();
      const src = defaultSrc || urlSrc;
      if (src) {
        const duration = Math.round((leaveTime - entryTime.current) / 1e3);
        if (!duration) {
          return;
        }
        const data = {
          org_code: src,
          duration,
        };
        const params = {
          statistics_type: STATISTIC_TYPE.WEB_DURATION,
        };
        updateActivityStatisticApi(data, params, useBeacon);
      }
    },
    [defaultSrc, urlSrc],
  );

  const handleBeforeUnload = useCallback(() => {
    handleStatisticDuration(true);
  }, [handleStatisticDuration]);

  useEffect(() => {
    // 离开
    if (idle || visibility === 'hidden') {
      handleStatisticDuration(isMobile);
    }

    // 激活
    if (!idle || visibility === 'visible') {
      entryTime.current = Date.now();
    }
  }, [handleStatisticDuration, idle, isMobile, visibility]);

  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [handleBeforeUnload]);
};
