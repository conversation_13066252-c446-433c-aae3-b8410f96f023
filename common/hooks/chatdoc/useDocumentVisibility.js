import { useEffect, useState } from 'react';

export const useDocumentVisibility = () => {
  const [state, setState] = useState('visible');

  const visibilityChange = () => {
    setState(document.visibilityState);
  };

  useEffect(() => {
    document.addEventListener('visibilitychange', visibilityChange);
    return () =>
      document.removeEventListener('visibilitychange', visibilityChange);
  }, []);

  return state;
};
