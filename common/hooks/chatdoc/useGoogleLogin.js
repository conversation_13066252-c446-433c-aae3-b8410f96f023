import { useCallback, useEffect, useRef } from 'react';
import {
  getGoogleClientConfigApi,
  googleOneTapLoginApi,
} from '../../api/chatdoc/google';
import googleOneTap from '../../utils/chatdoc/google-one-tap';
import { getChatDOCLink } from '../../utils/chatdoc/chatdoc-link';
import { getConfig, getUserInfoApi } from '../../api/chatdoc/index';
import { hasGetConfig } from '../../api/chatdoc/http';
import { getUrlWithProduct } from '../../urls';
import { useGetUrlQueryData } from '../useUrlQueryHook';
import { useGlobalContext } from '../useGlobalHook';

export const useGoogleLogin = () => {
  const { defaultSrc } = useGlobalContext();
  const chatdocProjectHref = getUrlWithProduct('chatdoc', 'project');

  const urlSrc = useGetUrlQueryData('src');

  const src = useRef();
  const isLogin = useRef(false);
  const link = useRef('');

  const checkLoginStatus = useCallback(async () => {
    try {
      await getUserInfoApi();
      isLogin.current = true;
    } catch (e) {
      isLogin.current = false;
    }
  }, []);

  const googleLoginCallback = useCallback(async (credential) => {
    const data = {
      token: credential,
    };
    if (src.current) {
      data.org_code = src.current;
    }
    if (!hasGetConfig) {
      await getConfig();
    }
    const params = await googleOneTapLoginApi(data);
    window.location.href = getChatDOCLink(link.current, {
      ...params,
      src: src.current,
    });
  }, []);

  const initGoogleOneTap = useCallback(async () => {
    if (isLogin.current) {
      return;
    }
    try {
      const { client_id } = await getGoogleClientConfigApi();
      const options = {
        client_id,
        cancel_on_tap_outside: true, // optional
        use_fedcm_for_prompt: false,
      };
      await googleOneTap(options, ({ credential }) => {
        googleLoginCallback(credential);
      });
    } catch (e) {
      console.error(e);
    }
  }, [googleLoginCallback]);

  useEffect(() => {
    const init = async () => {
      await checkLoginStatus();
      await initGoogleOneTap();
    };
    init();
  }, [checkLoginStatus, initGoogleOneTap]);

  useEffect(() => {
    src.current = defaultSrc || urlSrc;
  }, [defaultSrc, urlSrc]);

  useEffect(() => {
    link.current = chatdocProjectHref;
  }, [chatdocProjectHref]);
};
