import { useState, useEffect } from 'react';
import UAParser from 'ua-parser-js';
import { useMobilePageContext } from './useMobilePageContext';

export function useShowBrowerTip() {
  const [isShowTip, setIsShowTip] = useState(false);
  const isMobile = useMobilePageContext();
  useEffect(() => {
    if (isMobile) {
      setIsShowTip(false);
      return;
    }
    const { name } = UAParser().browser;
    if (name === 'IE') {
      setIsShowTip(true);
    } else {
      setIsShowTip(false);
    }
  }, [isMobile]);
  return isShowTip;
}
