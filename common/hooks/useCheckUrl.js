import { useCallback, useMemo, useState } from 'react';
import { useMobilePageContext } from './useMobilePageContext';
import { getEnvVariables } from '../utils/env';
import { openWindow } from '../utils';
import { getUrlWithProduct } from '../urls';

export function useCheckUrl(url = '') {
  const { isStagingEnv, isProdEnv, product } = getEnvVariables();
  const isMobile = useMobilePageContext();

  const [loading, setLoading] = useState(false);

  const needCheckUrl = useMemo(() => {
    return (isStagingEnv || isProdEnv) && product === 'pdfparser';
  }, [isProdEnv, isStagingEnv, product]);

  const alternativeUrl = useMemo(() => {
    return url.replace('.com', '.site');
  }, [url]);

  const getValidUrl = useCallback(async () => {
    if (needCheckUrl) {
      const controller = new AbortController();
      const timer = setTimeout(() => controller.abort(), 2e3);
      try {
        const fetchUrl = getUrlWithProduct('chatdoc', 'index');
        const res = await fetch(fetchUrl, {
          method: 'HEAD',
          signal: controller.signal,
        });
        clearTimeout(timer);
        if (res.ok) {
          return url;
        } else {
          return alternativeUrl;
        }
      } catch (e) {
        clearTimeout(timer);
        return alternativeUrl;
      }
    } else {
      return url;
    }
  }, [alternativeUrl, needCheckUrl, url]);

  const handleLinkClicked = useCallback(
    async (e) => {
      if (needCheckUrl) {
        e.preventDefault();
        if (loading) {
          return;
        }
        try {
          setLoading(true);
          const resultUrl = await getValidUrl();
          openWindow(resultUrl, !isMobile);
        } finally {
          setLoading(false);
        }
      }
    },
    [getValidUrl, isMobile, loading, needCheckUrl],
  );

  return {
    loading,
    handleLinkClicked,
  };
}
