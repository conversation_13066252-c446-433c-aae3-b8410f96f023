import { useState, useEffect } from 'react';
import { ProdICPDict } from '../icp/pro.icp';
import { TestICPDict } from '../icp/test.icp';
import { getEnvVariables } from '../utils/env';

export function useICPHook() {
  const icp = '京ICP备17060691号';
  const [icpNo, setIcpNo] = useState(1);
  const { isTestEnv } = getEnvVariables();

  useEffect(() => {
    const host = window.location.host;
    const hostname = window.location.hostname;

    if (isTestEnv) {
      if (TestICPDict[host]) {
        setIcpNo(TestICPDict[host]);
      }
    } else {
      const hostnameSplit = hostname.split('.');
      const domain = hostnameSplit
        .slice(hostnameSplit.length - 2, hostnameSplit.length)
        .join('.');

      if (ProdICPDict[domain]) {
        setIcpNo(ProdICPDict[domain]);
      }
    }
  }, [isTestEnv]);

  return `${icp}-${icpNo}`;
}
