<svg width="1177" height="1083" viewBox="0 0 1177 1083" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_0_1)">
<ellipse cx="832" cy="690" rx="44.5" ry="93" fill="#627AE6" fill-opacity="0.3"/>
</g>
<g filter="url(#filter1_f_0_1)">
<ellipse cx="831" cy="690" rx="22.5" ry="46" fill="#627AE6" fill-opacity="0.3"/>
</g>
<g style="mix-blend-mode:hard-light" filter="url(#filter2_f_0_1)">
<ellipse cx="831" cy="690" rx="22.5" ry="46" fill="#627AE6" fill-opacity="0.3"/>
</g>
<g filter="url(#filter3_f_0_1)">
<ellipse cx="385.5" cy="392.5" rx="85.5" ry="92.5" fill="#4BB75A" fill-opacity="0.2"/>
</g>
<g filter="url(#filter4_f_0_1)">
<ellipse cx="384.205" cy="392.5" rx="42.75" ry="45.9731" fill="#4BB75A" fill-opacity="0.2"/>
</g>
<g style="mix-blend-mode:hard-light" filter="url(#filter5_f_0_1)">
<ellipse cx="384.205" cy="392.5" rx="42.75" ry="45.9731" fill="#4BB75A" fill-opacity="0.2"/>
</g>
<rect x="300.5" y="300.5" width="545" height="469" rx="24.5" fill="#111111"/>
<rect x="300.5" y="300.5" width="545" height="469" rx="24.5" stroke="white" stroke-opacity="0.2"/>
<rect x="300.5" y="300.5" width="545" height="469" rx="24.5" stroke="url(#paint0_linear_0_1)"/>
<rect x="300.5" y="300.5" width="545" height="469" rx="24.5" stroke="url(#paint1_linear_0_1)"/>
<rect x="300.5" y="300.5" width="545" height="469" rx="24.5" stroke="url(#paint2_linear_0_1)"/>
<rect x="323" y="314" width="10" height="10" rx="5" fill="#E75B30"/>
<rect x="339" y="314" width="10" height="10" rx="5" fill="#F6BD3F"/>
<rect x="355" y="314" width="10" height="10" rx="5" fill="#51CA8A"/>
<path d="M544.372 321.992L546.412 316.424H547.576L544.996 323H543.724L541.144 316.424H542.32L544.372 321.992ZM549.215 315.356C549.007 315.356 548.831 315.284 548.687 315.14C548.543 314.996 548.471 314.82 548.471 314.612C548.471 314.404 548.543 314.228 548.687 314.084C548.831 313.94 549.007 313.868 549.215 313.868C549.415 313.868 549.583 313.94 549.719 314.084C549.863 314.228 549.935 314.404 549.935 314.612C549.935 314.82 549.863 314.996 549.719 315.14C549.583 315.284 549.415 315.356 549.215 315.356ZM549.743 316.424V323H548.651V316.424H549.743ZM551.196 319.688C551.196 319.016 551.332 318.428 551.604 317.924C551.876 317.412 552.248 317.016 552.72 316.736C553.2 316.456 553.736 316.316 554.328 316.316C554.84 316.316 555.316 316.436 555.756 316.676C556.196 316.908 556.532 317.216 556.764 317.6V314.12H557.868V323H556.764V321.764C556.548 322.156 556.228 322.48 555.804 322.736C555.38 322.984 554.884 323.108 554.316 323.108C553.732 323.108 553.2 322.964 552.72 322.676C552.248 322.388 551.876 321.984 551.604 321.464C551.332 320.944 551.196 320.352 551.196 319.688ZM556.764 319.7C556.764 319.204 556.664 318.772 556.464 318.404C556.264 318.036 555.992 317.756 555.648 317.564C555.312 317.364 554.94 317.264 554.532 317.264C554.124 317.264 553.752 317.36 553.416 317.552C553.08 317.744 552.812 318.024 552.612 318.392C552.412 318.76 552.312 319.192 552.312 319.688C552.312 320.192 552.412 320.632 552.612 321.008C552.812 321.376 553.08 321.66 553.416 321.86C553.752 322.052 554.124 322.148 554.532 322.148C554.94 322.148 555.312 322.052 555.648 321.86C555.992 321.66 556.264 321.376 556.464 321.008C556.664 320.632 556.764 320.196 556.764 319.7ZM565.713 319.46C565.713 319.668 565.701 319.888 565.677 320.12H560.421C560.461 320.768 560.681 321.276 561.081 321.644C561.489 322.004 561.981 322.184 562.557 322.184C563.029 322.184 563.421 322.076 563.733 321.86C564.053 321.636 564.277 321.34 564.405 320.972H565.581C565.405 321.604 565.053 322.12 564.525 322.52C563.997 322.912 563.341 323.108 562.557 323.108C561.933 323.108 561.373 322.968 560.877 322.688C560.389 322.408 560.005 322.012 559.725 321.5C559.445 320.98 559.305 320.38 559.305 319.7C559.305 319.02 559.441 318.424 559.713 317.912C559.985 317.4 560.365 317.008 560.853 316.736C561.349 316.456 561.917 316.316 562.557 316.316C563.181 316.316 563.733 316.452 564.213 316.724C564.693 316.996 565.061 317.372 565.317 317.852C565.581 318.324 565.713 318.86 565.713 319.46ZM564.585 319.232C564.585 318.816 564.493 318.46 564.309 318.164C564.125 317.86 563.873 317.632 563.553 317.48C563.241 317.32 562.893 317.24 562.509 317.24C561.957 317.24 561.485 317.416 561.093 317.768C560.709 318.12 560.489 318.608 560.433 319.232H564.585ZM570.022 323.108C569.406 323.108 568.846 322.968 568.342 322.688C567.846 322.408 567.454 322.012 567.166 321.5C566.886 320.98 566.746 320.38 566.746 319.7C566.746 319.028 566.89 318.436 567.178 317.924C567.474 317.404 567.874 317.008 568.378 316.736C568.882 316.456 569.446 316.316 570.07 316.316C570.694 316.316 571.258 316.456 571.762 316.736C572.266 317.008 572.662 317.4 572.95 317.912C573.246 318.424 573.394 319.02 573.394 319.7C573.394 320.38 573.242 320.98 572.938 321.5C572.642 322.012 572.238 322.408 571.726 322.688C571.214 322.968 570.646 323.108 570.022 323.108ZM570.022 322.148C570.414 322.148 570.782 322.056 571.126 321.872C571.47 321.688 571.746 321.412 571.954 321.044C572.17 320.676 572.278 320.228 572.278 319.7C572.278 319.172 572.174 318.724 571.966 318.356C571.758 317.988 571.486 317.716 571.15 317.54C570.814 317.356 570.45 317.264 570.058 317.264C569.658 317.264 569.29 317.356 568.954 317.54C568.626 317.716 568.362 317.988 568.162 318.356C567.962 318.724 567.862 319.172 567.862 319.7C567.862 320.236 567.958 320.688 568.15 321.056C568.35 321.424 568.614 321.7 568.942 321.884C569.27 322.06 569.63 322.148 570.022 322.148ZM575.178 323.072C574.97 323.072 574.794 323 574.65 322.856C574.506 322.712 574.434 322.536 574.434 322.328C574.434 322.12 574.506 321.944 574.65 321.8C574.794 321.656 574.97 321.584 575.178 321.584C575.378 321.584 575.546 321.656 575.682 321.8C575.826 321.944 575.898 322.12 575.898 322.328C575.898 322.536 575.826 322.712 575.682 322.856C575.546 323 575.378 323.072 575.178 323.072ZM585.258 316.304C585.77 316.304 586.226 316.412 586.626 316.628C587.026 316.836 587.342 317.152 587.574 317.576C587.806 318 587.922 318.516 587.922 319.124V323H586.842V319.28C586.842 318.624 586.678 318.124 586.35 317.78C586.03 317.428 585.594 317.252 585.042 317.252C584.474 317.252 584.022 317.436 583.686 317.804C583.35 318.164 583.182 318.688 583.182 319.376V323H582.102V319.28C582.102 318.624 581.938 318.124 581.61 317.78C581.29 317.428 580.854 317.252 580.302 317.252C579.734 317.252 579.282 317.436 578.946 317.804C578.61 318.164 578.442 318.688 578.442 319.376V323H577.35V316.424H578.442V317.372C578.658 317.028 578.946 316.764 579.306 316.58C579.674 316.396 580.078 316.304 580.518 316.304C581.07 316.304 581.558 316.428 581.982 316.676C582.406 316.924 582.722 317.288 582.93 317.768C583.114 317.304 583.418 316.944 583.842 316.688C584.266 316.432 584.738 316.304 585.258 316.304ZM590.805 317.636C591.021 317.26 591.341 316.948 591.765 316.7C592.197 316.444 592.697 316.316 593.265 316.316C593.849 316.316 594.377 316.456 594.849 316.736C595.329 317.016 595.705 317.412 595.977 317.924C596.249 318.428 596.385 319.016 596.385 319.688C596.385 320.352 596.249 320.944 595.977 321.464C595.705 321.984 595.329 322.388 594.849 322.676C594.377 322.964 593.849 323.108 593.265 323.108C592.705 323.108 592.209 322.984 591.777 322.736C591.353 322.48 591.029 322.164 590.805 321.788V326.12H589.713V316.424H590.805V317.636ZM595.269 319.688C595.269 319.192 595.169 318.76 594.969 318.392C594.769 318.024 594.497 317.744 594.153 317.552C593.817 317.36 593.445 317.264 593.037 317.264C592.637 317.264 592.265 317.364 591.921 317.564C591.585 317.756 591.313 318.04 591.105 318.416C590.905 318.784 590.805 319.212 590.805 319.7C590.805 320.196 590.905 320.632 591.105 321.008C591.313 321.376 591.585 321.66 591.921 321.86C592.265 322.052 592.637 322.148 593.037 322.148C593.445 322.148 593.817 322.052 594.153 321.86C594.497 321.66 594.769 321.376 594.969 321.008C595.169 320.632 595.269 320.192 595.269 319.688ZM597.378 321.068V320.24L601.59 314.408H602.898V320.12H604.098V321.068H602.898V323H601.818V321.068H597.378ZM601.866 315.548L598.638 320.12H601.866V315.548Z" fill="white" fill-opacity="0.2"/>
<defs>
<filter id="filter0_f_0_1" x="487.5" y="297" width="689" height="786" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter1_f_0_1" x="708.5" y="544" width="245" height="292" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter2_f_0_1" x="708.5" y="544" width="245" height="292" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter3_f_0_1" x="0" y="0" width="771" height="785" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter4_f_0_1" x="241.455" y="246.527" width="285.5" height="291.946" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter5_f_0_1" x="241.455" y="246.527" width="285.5" height="291.946" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="279" y1="645.558" x2="156.686" y2="756.413" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="834.45" y1="527.769" x2="1007.7" y2="527.769" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.05"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="306" y1="297.5" x2="334.5" y2="336" gradientUnits="userSpaceOnUse">
<stop stop-color="#4BB75A"/>
<stop offset="1" stop-color="#4BB75A" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
