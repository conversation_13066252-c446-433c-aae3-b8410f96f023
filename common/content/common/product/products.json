[{"name": "基座简介", "headerSubname": "基座1", "subname": "基座2", "type": "base", "featuredImage": "./images/bg/base.png", "desc": "基座描述", "productList": [{"name": "庖丁解文", "headerTitle": "专业知识 AI 问答助手1", "title": "专业知识 AI 问答助手2", "desc": "ChatDOC描述", "featuredImage": "./images/icon/paodingjiewen.png", "url": {"product": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathName": "index"}, "hasWeb": true, "isShowProduct": true, "backgroundColor": "linear-gradient(230deg, rgba(157, 157, 249, 0.90) 0%, rgba(29, 69, 194, 0.90) 100%)"}, {"name": "PDFlux", "title": "PDF 数据提取神器", "desc": "PDFlux描述", "featuredImage": "./images/icon/pdflux.png", "url": {"product": "pdflux", "pathName": "index"}, "hasWeb": true, "hasClient": true, "hasWeChat": true, "isShowProduct": true, "canScanLogin": true, "backgroundColor": "linear-gradient(224.72deg, rgba(12, 206, 254, 0.9) -1%, rgba(180, 4, 229, 0.9) 100.5%)"}, {"name": "<PERSON><PERSON><PERSON>", "title": "文档信息抽取平台", "desc": "Scriber描述", "featuredImage": "./images/icon/scriber.png", "url": {"product": "scriber", "pathName": "index"}, "hasWeb": true, "hasClient": true, "isShowProduct": true, "backgroundColor": "linear-gradient(224.72deg, rgba(37, 192, 202, 0.9) -1%, rgba(17, 112, 180, 0.9) 100.5%)"}, {"name": "<PERSON>", "title": "智能语义搜索引擎", "desc": "<PERSON>描述", "featuredImage": "./images/icon/hunter.png", "url": {"product": "hunter", "pathName": "index"}}, {"name": "Semanmeter", "chineseName": "意表", "title": "截屏识别表格", "featuredImage": "./images/icon/semanmeter.png", "url": {"product": "semanmeter", "pathName": "index"}, "canScanLogin": true}]}, {"name": "审核简介", "headerSubname": "审核1", "subname": "审核2", "type": "check", "featuredImage": "./images/bg/check.png", "desc": "审核描述", "productList": [{"name": "AutoDoc", "title": "文档智能审核系统", "desc": "AutoDoc描述", "featuredImage": "./images/icon/autodoc.png", "url": {"product": "autodoc", "pathName": "index"}, "hasWeb": true, "isShowProduct": true, "canScanLogin": true, "backgroundColor": "linear-gradient(224.72deg, rgba(51, 153, 255, 0.9) -1%, rgba(18, 32, 222, 0.9) 100.5%)"}, {"name": "Calliper", "title": "文档内容对比神器", "desc": "Calliper描述", "featuredImage": "./images/icon/calliper.png", "url": {"product": "calliper", "pathName": "index"}, "hasWeb": true, "hasClient": true, "hasWeChat": true, "isShowProduct": true, "canScanLogin": true, "backgroundColor": "linear-gradient(224.72deg, rgba(4, 251, 250, 0.9) -1%, rgba(2, 140, 245, 0.9) 100.5%)"}, {"name": "Grater", "headerTitle": "银行流水智能识别核查1", "title": "银行流水智能识别核查2", "desc": "Grater描述", "featuredImage": "./images/icon/grater.png", "url": {"product": "grater", "pathName": "index"}, "hasWeb": true, "hasClient": true, "hasWeChat": true, "isShowProduct": true, "canScanLogin": true, "backgroundColor": "linear-gradient(209.71deg, rgba(144, 158, 253, 0.9) 4.61%, rgba(120, 109, 234, 0.9) 94.94%)"}, {"name": "MetalMesh", "title": "财报智能提取", "featuredImage": "./images/icon/metalmesh.png", "url": {"product": "metalmesh", "pathName": "index"}}, {"name": "Corrector", "title": "错别字校正", "featuredImage": "./images/icon/corrector.png", "url": {"product": "other", "pathName": "corrector"}, "hideInConnect": true}]}, {"name": "撰写简介", "headerSubname": "撰写1", "subname": "撰写2", "type": "write", "featuredImage": "./images/bg/write.png", "desc": "撰写描述", "productList": [{"name": "G<PERSON>zer", "title": "庖丁智能撰写", "desc": "Glazer描述", "featuredImage": "./images/icon/glazer.png", "url": {"product": "glazer", "pathName": "index"}, "hasWeb": true, "isShowProduct": true, "backgroundColor": "linear-gradient(224.72deg, rgba(113, 223, 254, 0.9) -1%, rgba(8, 34, 132, 0.9) 100.5%)"}]}, {"name": "开发工具简介", "headerSubname": "开发工具", "subname": "开发工具", "type": "platform", "featuredImage": "./images/bg/platform.png", "desc": "开发工具描述", "productList": [{"name": "PDFlux SDK", "headerTitle": "开箱即用的 PDF 工具包1", "title": "开箱即用的 PDF 工具包2", "desc": "PDFlux SDK描述", "featuredImage": "./images/icon/pdflux-sdk.png", "documentName": "开发文档", "documentLink": "", "url": {"product": "pdflux-sdk", "pathName": "index"}, "isShowProduct": true, "backgroundColor": "linear-gradient(224.72deg, rgba(157, 157, 249, 0.9) -1%, rgba(29, 69, 194, 0.9) 100.5%)"}, {"name": "Foundry", "title": "自然语言语义理解平台", "desc": "Foundry描述", "featuredImage": "./images/icon/foundry.png", "url": {"product": "paodingai", "pathName": "foundry"}}, {"name": "AI Platform", "chineseName": "AI 开放平台", "headerTitle": "可靠的基础 AI 能力", "title": "提供可靠的基础 AI 能力", "desc": "AI Platform描述", "featuredImage": "./images/icon/ai-platform.png", "documentName": "API文档", "documentLink": "", "url": {"product": "paodingai", "pathName": "aiPlatform"}, "isHotProduct": true, "isShowProduct": true, "backgroundColor": "linear-gradient(247.36deg, #EDF0F8 15.93%, #E4EBF5 27.27%, #CADFEF 46.71%, #B2D3E8 62.1%, #94B1DA 86.39%, #7B91B5 96.11%)"}, {"name": "数据智能平台", "title": "数智视一体化解决方案", "desc": "数据智能平台描述", "featuredImage": "./images/icon/data-intelligence-platform.png", "url": {"product": "paodingai", "pathName": "dataIntelligence"}, "isHotProduct": true, "isShowProduct": true, "backgroundColor": "linear-gradient(224.72deg, rgba(249, 185, 124, 0.9) -1%, rgba(236, 49, 6, 0.9) 100.5%)"}]}]