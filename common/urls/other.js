const hosts = {
  test: {
    crm: 'http://**********:19000',
    ocrDemo: 'https://**********5:1085',
    corrector: 'http://**********1:55650/corrector',
    pdfluxSdkDemo: 'https://**********5:1910',
    webtool: 'https://**********5:1088',
    pdfluxSaas: 'http://***************:5005',
    pdfViewerDocs: 'https://bohr.cheftin.cn:8075/docs/',
    pdfparserApiReference:
      'https://bohr.cheftin.cn:7081/reference/#tag/PDF-Parser/operation/PDF_Parser_pdf_parser__upload_id__get',
    ocrflux: 'http://***********:22000',
  },
  prod: {
    crm: 'https://crm.paodingai.com',
    ocrDemo: 'https://ocrdemo.pdflux.com',
    corrector: 'https://corrector.paodingai.com',
    pdfluxSdkDemo: 'https://sdi.pdflux.com',
    webtool: 'https://webtool.pdflux.com',
    pdfluxSaas: 'https://saas.pdflux.com',
    pdfViewerDocs: 'https://docs.pdflux.com/pdf-viewer/',
    pdfparserApiReference:
      'https://api-reference.chatdoc.com/#tag/PDF-Parser/operation/PDF_Parser_pdf_parser__upload_id__get',
    ocrflux: 'https://ocrflux.pdfparser.io',
  },
};

const generateUrls = (env) => {
  const {
    crm,
    ocrDemo,
    corrector,
    webtool,
    pdfluxSdkDemo,
    pdfluxSaas,
    pdfViewerDocs,
    pdfparserApiReference,
    ocrflux,
  } = hosts[env];

  const urls = {
    ocrDemo: `${ocrDemo}`,
    corrector: `${corrector}/`,
    pdfluxSdkDemo: `${pdfluxSdkDemo}`,
    pdfluxHelpCenter: `${crm}/#/helpCenter/PDFlux`,
    pdfluxHelpCenterAPI: `${crm}/#/helpCenter/PDFlux?search=六、API文档`,
    calliperHelpCenter: `${crm}/#/helpCenter/Calliper`,
    paodingjiewenHelpCenter: `${crm}/#/helpCenter/庖丁解文`,
    webtool: `${webtool}`,
    webtoolConvert: `${webtool}/#/convert`,
    webtoolPredict: `${webtool}/#/?toolbar=predict`,
    webtoolMark: `${webtool}/#/?toolbar=mark`,
    pdfluxSaasLogin: `${pdfluxSaas}/#/login`,
    pdfViewer: `${pdfViewerDocs}`,
    pdfparserApiReference: `${pdfparserApiReference}`,
    ocrflux,
    pdfluxApiDocs:
      'https://sr.paodingai.com/pdflux-api-docs/PDFluxSaaS接口说明文档.pdf',
    pdfluxBrowserPlugin:
      'https://sr.paodingai.com/pdflux-browser-extensions/download/pdflux-extension.zip',
    semanmeterDownload:
      'https://sr.paodingai.com/semanmeter/Semanmeter_latest_x64_zh-CN.msi',
    chatdocExtensionChrome:
      'https://chromewebstore.google.com/detail/chatdoc-analyse-pdf-and-c/gcgignbmeagiipblicfmdlmnngjhaajl',
    chatdocExtensionEdge:
      'https://microsoftedge.microsoft.com/addons/detail/chatdoc-pdf-ai-reading-a/cadmgffpgcplelmnpkbgfmjpeaandini',
    chatdocTwitter: 'https://twitter.com/chatdoc_ai',
    chatdocDiscord: 'https://discord.gg/F33mhsAqqg',
    chatdocProductHunt:
      'https://www.producthunt.com/posts/chatdoc-gpt-for-any-file?utm_source=badge-featured&utm_medium=badge&utm_souce=badge-chatdoc-gpt-for-any-file',
    chatdbTwitter: '',
    chatdbProductHunt: '',
    chatitdoneTwitter: '',
    chatitdoneDiscord: '',
    chatitdoneProductHunt: '',
    wxIntelligentDocument: 'https://mp.weixin.qq.com/s/aH2kEqtUElAtub3El1l_kg',
    wxTableIdentification: 'https://mp.weixin.qq.com/s/nVfu8alNRf3HIQlNHBarqw',
    wxLargeLanguageModelsApplication:
      'https://mp.weixin.qq.com/s/wqhJIlB615OL0gKSE0xVZg',
    wxEnhancedPDFStructureRecognition:
      'https://mp.weixin.qq.com/s/JJHlJsWEqFG77LdzhvzDNw',
  };

  return urls;
};

const urlsMap = {};

const envs = ['test', 'prod'];

envs.forEach((env) => {
  urlsMap[env] = generateUrls(env);
});

export { urlsMap };
