function createUrlsMap(host, paths) {
  const result = {};

  Object.keys(paths).forEach((key) => {
    let path = paths[key];

    if (host.endsWith('/') && path.startsWith('/')) {
      host = host.slice(0, -1);
    }

    result[key] = `${host}${path}`;
  });

  return result;
}

function getUrlsMap(envs, paths) {
  const urlsMap = { internal: paths };

  envs.forEach(({ host, env }) => {
    urlsMap[env] = createUrlsMap(host, paths);
  });

  return urlsMap;
}

export { getUrlsMap };
