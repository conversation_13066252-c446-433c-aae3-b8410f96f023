import { getUrlsMap } from '../utils';

const paths = {
  index: '/',
  blog: '/blog/',
  changelog: '/log/',
  policy: '/privacy_policy.html',
  terms: '/term_of_service.html',
  project: '/chatpaper/',
};

const envs = [
  { host: 'https://bohr.cheftin.cn:8100', env: 'test' },
  { host: 'https://staging.chatpaper.com/', env: 'staging' },
  { host: 'https://chatpaper.com', env: 'prod' },
];

const urlsMap = getUrlsMap(envs, paths);

export { urlsMap };
