import { urlsMap as autodocUrlsMap } from './products/autodoc';
import { urlsMap as autodocVipUrlsMap } from './products/autodoc-vip';
import { urlsMap as autodocNextUrlsMap } from './products/autodoc-next';
import { urlsMap as calliperUrlsMap } from './products/calliper';
import { urlsMap as calliperVipUrlsMap } from './products/calliper-vip';
import { urlsMap as glazerUrlsMap } from './products/glazer';
import { urlsMap as graterUrlsMap } from './products/grater';
import { urlsMap as graterVipUrlsMap } from './products/grater-vip';
import { urlsMap as hunterUrlsMap } from './products/hunter';
import { urlsMap as metalmeshUrlsMap } from './products/metalmesh';
import { urlsMap as paodingaiUrlsMap } from './products/paodingai';
import { urlsMap as paodingjiewenUrlsMap } from './products/paodingjiewen';
import { urlsMap as pdfluxSdkUrlsMap } from './products/pdflux-sdk';
import { urlsMap as pdfluxUrlsMap } from './products/pdflux';
import { urlsMap as pdfparserUrlsMap } from './products/pdfparser';
import { urlsMap as scriberUrlsMap } from './products/scriber';
import { urlsMap as semanmeterUrlsMap } from './products/semanmeter';
import { urlsMap as chatdocUrlsMap } from './products/chatdoc';
import { urlsMap as chatpaperUrlsMap } from './products/chatpaper';
import { urlsMap as pdfchatUrlsMap } from './products/pdfchat';
import { urlsMap as chatdbUrlsMap } from './products/chatdb';
import { urlsMap as chatitdoneUrlsMap } from './products/chatitdone';
import { urlsMap as otherUrlsMap } from './other';
import { getEnvVariables } from '../utils/env';

const products = {
  autodoc: autodocUrlsMap,
  'autodoc-vip': autodocVipUrlsMap,
  'autodoc-next': autodocNextUrlsMap,
  calliper: calliperUrlsMap,
  'calliper-vip': calliperVipUrlsMap,
  glazer: glazerUrlsMap,
  grater: graterUrlsMap,
  'grater-vip': graterVipUrlsMap,
  hunter: hunterUrlsMap,
  metalmesh: metalmeshUrlsMap,
  paodingai: paodingaiUrlsMap,
  paodingjiewen: paodingjiewenUrlsMap,
  'pdflux-sdk': pdfluxSdkUrlsMap,
  pdflux: pdfluxUrlsMap,
  pdfparser: pdfparserUrlsMap,
  scriber: scriberUrlsMap,
  semanmeter: semanmeterUrlsMap,
  chatdoc: chatdocUrlsMap,
  chatpaper: chatpaperUrlsMap,
  pdfchat: pdfchatUrlsMap,
  chatdb: chatdbUrlsMap,
  chatitdone: chatitdoneUrlsMap,
  other: otherUrlsMap,
};

function getUrlWithProduct(productName, pathName, isEnternalUrl) {
  const { env, product: currentProductName } = getEnvVariables();

  if (productName) {
    const isInternalUrl = productName === currentProductName;
    let urls =
      products[productName][!isEnternalUrl && isInternalUrl ? 'internal' : env];
    if (!urls) {
      urls = products[productName]['prod'];
    }
    return urls[pathName];
  }
}

export { getUrlWithProduct };
