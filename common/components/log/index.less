.update-version {
  margin: 0 auto;
  padding-bottom: 100px !important;

  .last-version {
    padding-bottom: 20px;
    background-color: #fbfbfb;
    border-bottom: @border-base;

    .link-btn,
    .log-download .download-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 199px;
      height: 73px;
      font-size: 24px;
    }

    .link-btn {
      margin: 0;
    }

    .log-download {
      .download-btn {
        background-image: linear-gradient(45deg, #2abdf7, #aa25e4);
      }

      .download-btn-mobile {
        width: auto;
        height: auto;
        background-color: unset;

        .download-text {
          background-image: linear-gradient(45deg, #2abdf7, #aa25e4);
        }
      }
    }
  }

  .version-item {
    width: 80%;
    max-width: 100%;
    margin: auto;
  }

  .version-top-line {
    width: 100%;
    height: 3px;
    margin: 12px 0 30px;
    background: linear-gradient(
      226.45deg,
      rgba(12, 206, 254, 90%) 2.05%,
      rgba(180, 4, 229, 90%) 100.49%
    );
    border-radius: 99px;
    backdrop-filter: blur(1.27641px);
  }
}

@media (min-width: 1366px) {
  .update-version {
    .version-item {
      max-width: 1366px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .update-version {
    .last-version {
      .link-btn,
      .log-download .download-btn {
        width: 140px;
        height: 46px;
        font-size: 18px;
      }
    }

    .version-item {
      width: calc(100% - 32px);
    }

    .version-top-line {
      margin-top: 4px;
    }
  }
}
