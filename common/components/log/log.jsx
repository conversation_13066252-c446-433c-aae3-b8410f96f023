import React, { useMemo, useRef, useEffect } from 'react';
import LazyLoad from 'vanilla-lazyload';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import VersionItem from '../version/version-item';
import Download from '../download/download';
import { downloadClient } from '../../utils/download';
import { useMobilePageContext } from '../../hooks/useMobilePageContext';
import './index.less';

const Log = (props) => {
  const isMobile = useMobilePageContext();
  const { nodes, downloadUrl, logButton } = props;
  const lazyloadInstance = useRef(null);

  const logData = useMemo(() => {
    return nodes
      .filter((node) => node.content.data?.childMarkdownRemark)
      .map((node) => {
        const { content, videos } = node;
        const { frontmatter, html, rawMarkdownBody } =
          node.content.data.childMarkdownRemark;

        return {
          version: frontmatter.version,
          date: dayjs(node.publishAt).format('YYYY-MM-DD'),
          html,
          rawMarkdownBody,
          content,
          videos,
        };
      });
  }, [nodes]);

  useEffect(() => {
    if (
      window.__browserFeatures?.isIntersectionObserverSupported &&
      isMobile !== undefined
    ) {
      lazyloadInstance.value = new LazyLoad({
        elements_selector: '.lazyload',
        data_src: 'lazy-src',
        callback_enter(element) {
          if (!element.hasAttribute('autoplay')) {
            element.setAttribute('autoplay', '');
          }
        },
      });
    }
  }, [isMobile]);

  setTimeout(() => {
    if (lazyloadInstance.value) {
      lazyloadInstance.value.update();
    }
  });

  return (
    <ul className="update-version">
      {logData.map((node, index) => {
        const { version, date, html, rawMarkdownBody, content, videos } = node;

        if (index === 0) {
          return (
            <li className="last-version" key={index}>
              <VersionItem
                title={`Version ${version}`}
                date={date}
                html={html}
                content={content}
                rawMarkdownBody={rawMarkdownBody}
                className="version-item"
                isStrapiData={true}
                lazyloadVideo={props.lazyloadVideo}
                videos={videos}>
                {logButton || (
                  <Download
                    downloadUrl={downloadUrl}
                    className="log-download"
                  />
                )}
                <div className="version-top-line"></div>
              </VersionItem>
            </li>
          );
        } else {
          return (
            <li key={index}>
              <VersionItem
                title={`Version ${version}`}
                className="version-item"
                date={date}
                content={content}
                rawMarkdownBody={rawMarkdownBody}
                html={html}
                lazyloadVideo={props.lazyloadVideo}
                isStrapiData={true}
                videos={videos}
              />
            </li>
          );
        }
      })}
    </ul>
  );
};

Log.defaultProps = {
  downloadUrl: downloadClient(),
  lazyloadVideo: false,
};

Log.propTypes = {
  nodes: PropTypes.arrayOf(PropTypes.object),
  downloadUrl: PropTypes.string,
  logButton: PropTypes.element,
  lazyloadVideo: PropTypes.bool,
};

export default Log;
