import React, { Component } from 'react';
import { Element, scroller } from 'react-scroll';

export const ScrollToElement = (ElementComponent, key) => {
  return class extends Component {
    render() {
      return (
        <Element name={key} id={key}>
          <ElementComponent {...this.props} />
        </Element>
      );
    }
  };
};

export const ScrollNav = (NavComponent) => {
  return class extends Component {
    scrollToElement = (
      ElementName,
      scrollPath = '/',
      elementScrollOffset = '0',
    ) => {
      let options = {
        duration: 500,
        smooth: true,
        offset: -elementScrollOffset,
      };
      const pathname = window.location.pathname;
      if (pathname === scrollPath) {
        scroller.scrollTo(ElementName, options);
      } else {
        window.location.href = `${scrollPath}#${ElementName}`;
      }
    };

    render() {
      return (
        <NavComponent scrollToElement={this.scrollToElement} {...this.props} />
      );
    }
  };
};
