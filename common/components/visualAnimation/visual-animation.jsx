import React, { Component } from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import 'intersection-observer';

export default class VisualAnimation extends Component {
  constructor(props) {
    super(props);
    this.state = { isVisible: false };
    this.io = null;
    this.container = React.createRef();
  }

  componentDidMount() {
    const { threshold } = this.props;
    this.io = new IntersectionObserver(
      ([entries]) => {
        if (entries.isIntersecting) {
          this.setState({ isVisible: entries.isIntersecting });
          this.io.disconnect();
        }
      },
      { threshold },
    );
    this.io.observe(this.container.current);
  }

  componentWillUnmount() {
    if (this.io) {
      this.io.disconnect();
    }
  }

  render() {
    const { children, animationClass, className, renderChildren } = this.props;
    const { isVisible } = this.state;
    const divClassNames = classnames({
      [className]: className,
      [animationClass]: isVisible && animationClass,
    });
    return (
      <div
        ref={this.container}
        className={divClassNames}
        style={{ visibility: isVisible ? 'visible' : 'hidden' }}>
        {renderChildren ? children : isVisible && children}
      </div>
    );
  }
}

VisualAnimation.propTypes = {
  className: PropTypes.string,
  animationClass: PropTypes.string.isRequired,
  threshold: PropTypes.arrayOf(PropTypes.number),
};

VisualAnimation.defaultProps = {
  threshold: [0.5, 0.75],
};
