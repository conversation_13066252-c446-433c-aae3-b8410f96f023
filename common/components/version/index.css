.timeline-item {
  position: relative;
}

.timeline-item-tail {
  position: absolute;
  left: 16px;
  width: 4px;
  height: calc(100% + 100px);
  background: #f0f0f3;
}

.timeline-item-node {
  position: absolute;
  top: 70px;
  left: 8px;
  z-index: 10;
  width: 20px;
  height: 20px;
  background-image: linear-gradient(45deg, #aa25e4 0%, #2abdf7 100%);
  border-radius: 50%;
  box-shadow: 0 10px 35px 0 rgba(53, 16, 150, 10%);
}

.timeline-item-wrapper {
  box-sizing: border-box;
  padding-top: 46px;
  padding-left: 50px;
  overflow: hidden;
}

.version-title {
  height: 62px;
  color: #3e3f42;
  font-weight: 500;
  font-size: 44px;
  line-height: 62px;
}

.version-time {
  height: 28px;
  margin-bottom: 30px;
  color: #6b6c6f;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0;
  opacity: 0;
}

.version-time.show {
  opacity: 0.6;
}

.version-subtitle {
  color: #6b6c6f;
  font-size: 17px;
  line-height: 32px;
  letter-spacing: 0;
}

.version-subtitle ol {
  margin-left: 17px;
  list-style: decimal;
}

.version-subtitle ol > li {
  list-style-type: decimal;
}

.version-subtitle ol > ul {
  margin-left: 20px;
}

.version-subtitle ul {
  list-style: inside;
}

.version-item-video,
.version-item-image,
.version-subtitle img {
  width: 766px;
}

.version-subtitle img {
  display: block;
}

@media (max-width: 768px) {
  .version-title {
    height: 28px;
    font-size: 20px;
    line-height: 28px;
  }

  .version-time {
    height: 20px;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 20px;
  }

  .version-subtitle img,
  .version-item-image,
  .version-item-video {
    width: 100%;
  }

  .timeline-item-tail {
    left: 7px;
  }

  .timeline-item-node {
    top: 52px;
    left: 1px;
    width: 16px;
    height: 16px;
  }

  .version-subtitle {
    margin-top: 20px;
    font-size: 14px;
    line-height: 24px;
  }

  .timeline-item-wrapper {
    padding-left: 26px;
  }
}
