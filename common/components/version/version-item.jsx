import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import ReactMarkdown from 'react-markdown';
import { getImage, GatsbyImage } from 'gatsby-plugin-image';
import { useMobilePageContext } from '../../hooks/useMobilePageContext';
import classnames from 'classnames';
import './index.css';

const videoFormats = ['mp4', 'ogg', 'avi'];

function createVideoEl(localFile, isMobile, lazyloadVideo = false) {
  const normalVideoEl = (
    <video
      loop
      muted
      autoPlay
      controls={isMobile}
      src={localFile.publicURL}
      className="version-item-video"></video>
  );

  if (!lazyloadVideo) {
    return normalVideoEl;
  }

  const isRenderInBrowser = typeof window !== 'undefined';
  if (!isRenderInBrowser) {
    return (
      <video
        loop
        muted
        controls={isMobile}
        data-lazy-src={localFile.publicURL}
        className="version-item-video"></video>
    );
  } else {
    if (window.__browserFeatures?.isIntersectionObserverSupported) {
      const formats = videoFormats.filter((format) =>
        localFile.publicURL.endsWith(format),
      );
      return (
        <video
          loop
          muted
          controls={isMobile}
          data-lazy-src={localFile.publicURL}
          className="version-item-video lazyload">
          {formats.map((format) => {
            return (
              <source
                key={format}
                type={`video/${format}`}
                data-lazy-src={localFile.publicURL}
              />
            );
          })}
        </video>
      );
    } else {
      return normalVideoEl;
    }
  }
}

const VersionItem = (props) => {
  const isMobile = useMobilePageContext();
  const {
    title,
    date,
    children,
    className,
    rawMarkdownBody,
    content,
    html,
    isStrapiData,
    videos,
    lazyloadVideo,
  } = props;

  const [showVersionTime, setShowVersionTime] = useState(false);

  useEffect(() => {
    setShowVersionTime(true);
  }, []);

  if (isStrapiData) {
    const components = {
      p: 'div',
      img(props) {
        const media = content.medias.find((m) => m.src === props.src);
        const image = getImage(media.localFile);
        return (
          <GatsbyImage
            image={image}
            alt="An image in blog article"
            imgStyle={{ width: '100%' }}
            objectFit={'fill'}
            loading={isMobile ? 'eager' : 'lazy'}
            className="version-item-image"></GatsbyImage>
        );
      },
      a(props) {
        if (props?.href?.match(/\.mp4$/)) {
          if (!videos) return null;
          const tempArr = props.href.split('/');
          const fileName = tempArr[tempArr.length - 1];
          const pathObj = videos.find((item) =>
            item.localFile.publicURL.includes(fileName),
          );
          if (pathObj?.localFile?.publicURL) {
            return createVideoEl(pathObj.localFile, isMobile, lazyloadVideo);
          } else {
            return null;
          }
        } else {
          return (
            <a href={props.href} target="_blank" rel="noreferrer">
              {props.children[0]}
            </a>
          );
        }
      },
    };

    return (
      <div className={`timeline-item ${className} customClass`}>
        <div className="timeline-item-tail" />
        <div className="timeline-item-node" />
        <div className="timeline-item-wrapper">
          <h3 className="version-title">{title}</h3>
          <div
            className={classnames({
              'version-time': true,
              show: showVersionTime,
            })}>
            {date}
          </div>
          {children}
          <div className="version-subtitle">
            {
              <ReactMarkdown
                children={rawMarkdownBody}
                components={components}></ReactMarkdown>
            }
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className={`timeline-item ${className}`}>
        <div className="timeline-item-tail" />
        <div className="timeline-item-node" />
        <div className="timeline-item-wrapper">
          <h3 className="version-title">{title}</h3>
          <div className="version-time">{date}</div>
          <div
            className="version-subtitle"
            dangerouslySetInnerHTML={{ __html: html }}
          />
          {children}
        </div>
      </div>
    );
  }
};

VersionItem.propTypes = {
  title: PropTypes.string,
  date: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  className: PropTypes.string,
  rawMarkdownBody: PropTypes.string,
  content: PropTypes.object,
  html: PropTypes.string,
  isStrapiData: PropTypes.bool,
  videos: PropTypes.arrayOf(PropTypes.object),
  lazyloadVideo: PropTypes.bool,
};

export default VersionItem;
