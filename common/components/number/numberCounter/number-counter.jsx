import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import loadable from '@loadable/component';
import 'odometer/themes/odometer-theme-default.css';
import './number-counter.less';

const Odometer = loadable(() => import('../../odometer/odometer'));

const NumberCounter = ({ value, duration }) => {
  const numberCounterRef = useRef();
  // 设置 defaultPrefix 解决了 odometer 插件无法设置前导0的问题，处理：在 value 前增加一个数字，并在 CSS 中隐藏增加的第一个数字。
  const defaultPrefix = 1;
  const leadingZeros = `${'0'.repeat(value.toString().length)}`;
  const defaultValue = `${defaultPrefix}${leadingZeros}`;
  const [currentValue, setCurrentValue] = useState(defaultValue);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          const newValue = `${defaultPrefix}${value}`;
          setCurrentValue(newValue);
        }, 500);
      }
    });
    observer.observe(numberCounterRef.current);
  }, [value]);

  return (
    <div className="number-counter" ref={numberCounterRef}>
      <Odometer
        value={currentValue}
        duration={duration}
        animation="count"
        format="d"
      />
    </div>
  );
};

NumberCounter.propTypes = {
  value: PropTypes.number,
  duration: PropTypes.number,
};

export default NumberCounter;
