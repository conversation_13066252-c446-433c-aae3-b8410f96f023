import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { Table } from 'react-bootstrap';

const RenderTd = ({ render, text, record }) => <td>{render(text, record)}</td>;

export default class BaseTable extends Component {
  createTds = (data, columns) => {
    return (
      <Fragment>
        {columns.map((column, index) => {
          const text = data[column.dataIndex];
          const render = column.render;
          return column.render ? (
            <RenderTd
              key={column.key || index}
              render={render}
              text={text}
              record={data}>
              {data[column]}
            </RenderTd>
          ) : (
            <td key={column.key || index}>{data[column.dataIndex]}</td>
          );
        })}
      </Fragment>
    );
  };

  render() {
    const { dataSource, columns, ...other } = this.props;
    return (
      <Table {...other}>
        <thead>
          <tr>
            {columns.map((item, index) => {
              if (item.colspan && item.colspan === 0) {
                return null;
              } else {
                return (
                  <th
                    colSpan={Number(item.colspan) || 1}
                    key={item.key || index}>
                    {item.title}
                  </th>
                );
              }
            })}
          </tr>
        </thead>
        <tbody>
          {dataSource.map((item, index) => (
            <tr key={item.key || index}>{this.createTds(item, columns)}</tr>
          ))}
        </tbody>
      </Table>
    );
  }
}

BaseTable.propTypes = {
  className: PropTypes.string,
  dataSource: PropTypes.arrayOf(PropTypes.object),
  columns: PropTypes.arrayOf(PropTypes.object),
};
