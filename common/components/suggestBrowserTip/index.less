.suggest-browser-tip {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  color: #f6ab2f;
  font-size: 20px;
  line-height: 26px;
  background-color: #fff;

  .tip-content {
    height: 100%;
    font-size: 20px;
    background-color: rgba(246, 171, 47, 10%);

    a {
      color: #f6ab2f;
      border-bottom: 1px solid #f6ab2f;

      &:hover {
        color: #f6ab2f;
      }
    }
  }

  .tip-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding-left: 10px;
    column-gap: 20px;
  }
}

@media (max-width: @mini-screen-size) {
  .suggest-browser-tip {
    .tip-content {
      font-size: 14px;
      line-height: 18px;
    }

    .tip-container {
      padding-right: 20px;
      padding-left: 30px;
    }
  }
}
