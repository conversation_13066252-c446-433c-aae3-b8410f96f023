import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import BrowserTipIcon from '../../assets/icons/browser-tip.svg';
import { useShowBrowerTip } from '../../hooks/useShowBrowerTipHook';
import { useHandleResize } from '../../hooks/useHandleResizeHook';
import './index.less';

const LANGUAGE = {
  zh: {
    desc: '为保证最佳体验，推荐您使用新版Edge或Chrome浏览器访问本网站。',
    downloadText: '去下载Edge',
  },
  en: {
    desc: 'Please use the latest Edge or Chrome browser for optimal performance. ',
    downloadText: 'Download Edge',
  },
};

const SuggestBrowserTip = ({ headerClass, locale }) => {
  const [height, setHeight] = useState('80px');
  const isShowTip = useShowBrowerTip();

  const getHeaderHeight = useCallback(() => {
    const headerDom = document.querySelector(`.${headerClass}`);
    if (headerDom) {
      setHeight(`${headerDom.clientHeight}px`);
    }
  }, [headerClass]);

  useHandleResize(() => {
    getHeaderHeight();
  });

  const atLang = LANGUAGE[locale] || LANGUAGE['zh'];

  return isShowTip ? (
    <div className="suggest-browser-tip" style={{ height: height }}>
      <div className="tip-content">
        <Container className="tip-container">
          <BrowserTipIcon />
          <span>
            {atLang['desc']}
            <a
              href="https://www.microsoft.com/zh-cn/edge/download?form=MA13FJ"
              target="_blank"
              rel="noopener noreferrer">
              {atLang['downloadText']}
              {'>>>'}
            </a>
          </span>
        </Container>
      </div>
    </div>
  ) : null;
};

SuggestBrowserTip.defaultProps = {
  locale: 'zh',
};

SuggestBrowserTip.propTypes = {
  headerClass: PropTypes.string,
  locale: PropTypes.oneOf(['zh', 'en']),
};

export default SuggestBrowserTip;
