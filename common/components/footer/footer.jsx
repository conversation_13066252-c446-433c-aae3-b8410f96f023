import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { useICPHook } from '../../hooks/useICPHook';
import { useFormatMessage } from '../../hooks/useFormatMessageHook';
import qrcodeImg from '../../assets/images/qrcode.jpg';
import './footer.less';

const ADDRESS = '地址：北京朝阳区中国铁建广场A座704';

const Footer = ({ hidePhone, hideEmail, hideWX, hideIcp, hideCompany }) => {
  const icp = useICPHook();
  const { getFormatMessage } = useFormatMessage();

  return (
    <footer className="footer-wrapper">
      <Container>
        <ul className="pdow-footer">
          <li>Copyright &copy; 2016-{new Date().getFullYear()}</li>
          <li>{getFormatMessage('北京庖丁科技有限公司')}</li>
          {!hidePhone && (
            <li>{`${getFormatMessage('联系电话2')}: ${getFormatMessage(
              '庖丁科技联系电话',
            )}`}</li>
          )}
          {!hideEmail && (
            <li>
              {`${getFormatMessage('邮箱')}: `}
              <a
                href={'mailto://' + getFormatMessage('庖丁科技联系邮箱')}
                className="footer-link">
                {getFormatMessage('庖丁科技联系邮箱')}
              </a>
            </li>
          )}
          {!hideIcp && (
            <li>
              <a
                href={getFormatMessage('庖丁科技icpLink')}
                className="footer-link"
                target="_blank"
                rel="noopener noreferrer">
                {icp}
              </a>
            </li>
          )}
          <li>All Rights Reserved</li>
          {!hideWX && (
            <li className="wx-item">
              微信公众号
              <div className="wx-img">
                <img
                  className="wx-qrcode"
                  src={qrcodeImg}
                  alt="qrcode"
                  style={{ display: 'inline-block' }}
                />
              </div>
            </li>
          )}
          {!hideCompany && <li>{ADDRESS}</li>}
        </ul>
      </Container>
    </footer>
  );
};

export default Footer;

Footer.propTypes = {
  hidePhone: PropTypes.bool,
  hideEmail: PropTypes.bool,
  hideWX: PropTypes.bool,
  hideIcp: PropTypes.bool,
  hideCompany: PropTypes.bool,
};
