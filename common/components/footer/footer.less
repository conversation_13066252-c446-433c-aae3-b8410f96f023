.footer-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 86px;
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  background: linear-gradient(0deg, #000, #000),
    linear-gradient(90deg, #2764ce 0%, #6561e9 100%);

  .footer-link {
    color: #fff;

    &:hover {
      color: #2764ce;
    }
  }

  .pdow-footer {
    width: 100%;

    & > li {
      position: relative;
      display: inline-block;
      margin-left: 10px;
    }
  }

  .wx-item {
    cursor: pointer;

    &:hover {
      .wx-img {
        display: block;
      }
    }
  }

  .wx-img {
    position: absolute;
    bottom: 25px;
    left: 50%;
    display: none;
    width: 130px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 30%);
    transform: translateX(-50%);
  }

  .wx-qrcode {
    width: 100%;
    height: 100%;
  }
}

@media (max-width: @large-screen-size) {
  .footer-wrapper {
    font-size: 14px;
    line-height: 22px;
  }
}

@media (max-width: @small-screen-size) {
  .footer-wrapper {
    .wx-img {
      right: 0;
      bottom: 20px;
      left: auto;
      transform: none;
    }
  }
}

@media (max-width: 550px) {
  .footer-wrapper {
    padding: 15px 0;

    .pdow-footer {
      display: flex;
      flex-wrap: wrap;
      width: 80%;
      min-width: 330px;
      margin: 0 auto;
      column-gap: 10px;

      & > li {
        width: calc((100% - 10px) / 2);
        margin-left: 0;
        white-space: nowrap;
        text-align: left;
      }
    }
  }
}

@media (max-width: 400px) {
  .footer-wrapper {
    font-size: 12px;

    .pdow-footer {
      width: 100%;
      min-width: unset;
      max-width: 300px;
    }
  }
}

@media (max-width: 350px) {
  .footer-wrapper {
    .pdow-footer {
      & > li {
        width: unset;
      }
    }
  }
}
