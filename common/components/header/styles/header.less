body {
  overflow: auto;

  &.no-scroll {
    overflow: hidden;
  }
}

.header-wrapper {
  --header-height: 80px;
  --header-nav-btn-height: 60px;

  position: sticky;
  top: 0;
  left: 0;
  z-index: 11;
  height: var(--header-height);
  background-color: #fff;

  &.header-relative {
    position: relative;
  }

  &.shadow {
    box-shadow: 0 2px 2px rgba(101, 122, 147, 20%);
  }

  .container {
    height: 100%;
  }

  .header-navbar {
    height: 100%;
    padding: 0;
  }

  .navbar-brand {
    margin-right: 50px;
    padding: 0;
  }

  .paoding-logo {
    height: 70px;
  }

  .product-brand-moblie {
    position: absolute;
    left: 150px;
    margin-right: auto;
    margin-left: 0;
    padding-left: 15px;
    border-left: 1px solid #eaedf3;
  }

  .navbar-dropdown {
    display: flex;
    align-items: center;
    width: 100%;
    backface-visibility: hidden;
  }

  .nav-menu-container {
    display: flex;
    width: 100%;
  }

  .nav-btn {
    display: flex;
    align-items: center;
  }

  .lang-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 92px;
    height: 38px;
    margin-right: 14px;
    border-radius: 4px;

    .lang-dropdown-toggle {
      display: flex;
      align-items: center;
      font-weight: 500;
    }

    &.show,
    &:hover {
      background: rgba(103, 88, 229, 10%);

      .lang-icon {
        background: url('../../../assets/images/language-hover.svg') no-repeat;
      }

      .lang-dropdown-toggle {
        color: rgba(103, 88, 229, 100%);
      }
    }

    .lang-icon {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      background: url('../../../assets/images/language.svg') no-repeat;
    }

    .lang-dropdown-toggle {
      color: rgba(20, 33, 50, 100%);

      &::after {
        display: none;
      }
    }

    &:hover {
      .dropdown-menu {
        display: block;
      }
    }

    .dropdown-menu {
      top: 40px;
      min-width: 92px;
      padding: 0;
      background: #fff;
      border: 1px solid #dedede;
      border-radius: 4px;

      .lang-link {
        font-size: 14px;

        &:hover {
          color: rgba(103, 88, 229, 100%);
          font-weight: 500;
          background: rgba(103, 88, 229, 10%);
        }
      }

      &::before {
        position: absolute;
        inset: -10px -1px -1px;
        z-index: -1;
        content: '';
      }
    }
  }

  .nav-menu {
    align-items: center;
    font-weight: 500;

    .nav-link {
      margin: 0 10px;
      padding: 5px 15px;
      color: @dark-color;
      font-size: 16px;
      line-height: 22px;
      white-space: nowrap;

      // nav-active的使用：如Grater官网个人版官网中对应nav-link一直高亮，则需添加类名nav-active。
      &[aria-expanded='true'],
      &:hover,
      &.nav-active {
        color: @primary-color;
        background-color: @content-background-color;
        border-radius: @border-radius-base;
      }

      &::after {
        display: none;
      }
    }

    .nav-item {
      width: 100%;

      &.dropdown:hover {
        .dropdown-menu {
          display: block;
        }
      }
    }
  }

  .nav-menu-product {
    padding-left: 20px;
    border-left: 1px solid #eaedf3;
  }

  .nav-menu-product-brand {
    padding-left: 0;

    .product-dropdown {
      border-right: 1px solid #eaedf3;
    }

    .dropdown-menu {
      &[aria-labelledby='product-dropdown'] {
        top: 40px;
      }
    }
  }

  .nav-menu-left {
    margin-right: auto;
  }

  .nav-menu-right {
    margin-right: 25px;
  }

  .dropdown-menu {
    top: 32px;
    margin: 0;
    background-color: transparent;
    border: none;
  }

  .navbar-toggler {
    display: none;
    padding: 0;
    color: #000;
    border: none;

    &:focus {
      box-shadow: none;
    }
  }

  @import './header-collapse.less';
  @import './header-media.less';
}
