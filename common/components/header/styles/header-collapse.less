:root {
  --page-height: 100vh;
}

.header-navbar-show-collapse {
  justify-content: space-between;

  .navbar-toggler {
    display: block;
  }

  .navbar-collapse {
    position: fixed;
    top: calc(var(--header-height) - 2px);
    left: 0;
    display: block !important;
    height: calc(var(--page-height) - var(--header-height) - 2px);
    background-color: @navbar-collapse-bg;
    border-top: @border-base;

    &:not(.show) {
      display: none !important;
    }
  }

  .navbar-dropdown {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    box-shadow: @box-shadow-base;
  }

  .nav-menu-container {
    display: block;
    max-height: calc(var(--page-height) - var(--header-height) - 60px);
    overflow-y: scroll;
  }

  .nav-menu {
    flex: 1;
    flex-direction: column;
    width: 100vw;
    margin-right: 0;
    padding-left: 0;
    overflow-y: auto;
    border: none;

    .nav-link {
      display: flex;
      align-items: center;
      width: 100%;
      margin: 0;
      padding: 10px 20px;
      font-size: 14px;
      line-height: 20px;

      &[aria-expanded='true'],
      &:hover {
        color: @dark-color;
        background-color: transparent;
      }

      &.nav-active {
        background-color: unset;
        border-radius: unset;
      }

      &::after {
        display: inline-block;
      }
    }
  }

  .nav-btn {
    display: flex;
    align-items: center;
    width: 100%;
    height: var(--header-nav-btn-height);
    margin: 0;
    padding: 0 20px;
    border-top: @border-base;

    .connect-btn {
      width: 100%;
    }
  }

  .lang-dropdown {
    justify-content: flex-start;
    width: 100%;
    height: 50px;
    margin-right: 0;
    padding: 0 20px;
    border-bottom: 1px solid #dedede;

    &:hover {
      background: unset;

      .lang-icon {
        background: url('../../../assets/images/language.svg') no-repeat;
      }
    }

    .lang-icon {
      margin-right: 10px;
    }

    .lang-link {
      display: flex;
      align-items: center;
      height: 20px;

      &.active {
        color: @primary-color;
        font-weight: 500;
      }
    }

    .lang-link + .lang-link {
      margin-left: 10px;
      padding-left: 10px;
      border-left: 1px solid #eaedf3;
    }
  }

  .product-dropdown {
    border: none;
  }

  .dropdown-menu {
    position: relative;
    top: 0;
    display: none !important;
    width: 100%;
    padding: 0;
    background-color: #f6f8fc;
    border-top: @border-base;
    border-bottom: @border-base;
    border-radius: 0;

    &.show {
      display: block !important;
    }
  }

  .dropdown-wrapper {
    display: block;
    margin-top: 0;
    padding: 15px 20px;
    overflow: auto;
    background-color: transparent;
    border: none;
    box-shadow: none;

    .dropdown-category {
      padding: 10px 0;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }

    .dropdown-title {
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 20px;
    }

    .dropdown-link {
      margin: 0;
    }

    .info-name {
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
    }
  }

  .dropdown-product {
    .products-list {
      flex-flow: wrap;
      row-gap: 10px;
    }

    .product-link {
      width: auto;
      min-width: 50%;

      .gatsby-image-wrapper {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        padding: 10px;
      }
    }

    .info-desc {
      display: none;
    }
  }

  .dropdown-application {
    .dropdown-category {
      &:last-child {
        .dropdown-title {
          margin-bottom: 0;
        }
      }
    }

    .industry-box {
      justify-content: normal;
    }

    .industry-link-box {
      display: block;
      width: auto;
      min-width: calc(100% / 3);
      padding: 0 5px;
    }

    .industry-link {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: fit-content;
      padding: 0 15px;
    }

    .industry-icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      margin-bottom: 0;
    }

    .industry-type {
      font-size: 16px;
    }

    .story-link-box {
      position: relative;
      width: 100%;
      padding: 0;
      white-space: unset;
      border-bottom: none;

      &::after {
        position: absolute;
        bottom: -7px;
        width: 100%;
        height: 1px;
        background-color: #dedede;
        content: '';
      }

      &:last-child {
        &::after {
          content: unset;
        }
      }

      .gatsby-image-wrapper {
        left: -13px;
        width: 120px;
      }

      .story-img-HKEX {
        left: -32px;
      }

      .story-img-BOC {
        left: -27px;
      }

      .story-img-CGS {
        left: -17px;
      }
    }

    .story-desc {
      width: auto;
    }
  }

  .dropdown-cooperation {
    .category-cooperation {
      width: 100%;
    }

    .cooperation-box {
      display: block;
    }

    .cooperation-link {
      padding-bottom: 20px;

      &:last-child {
        padding-bottom: 0;
      }

      .info-name {
        font-weight: 500;
        font-size: 16px;
      }

      .info-desc {
        white-space: normal;
      }
    }

    .cooperation-icon {
      width: 22px;
      height: 22px;
      margin-right: 10px;
    }
  }
}
