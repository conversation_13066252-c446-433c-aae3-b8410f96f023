.dropdown-wrapper {
  padding: 30px;

  .dropdown-category {
    padding: 0 15px;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }
}

.dropdown-product {
  .info-name-box {
    flex-direction: column;
    align-items: normal;

    .scan-use-tag {
      position: unset;
      width: fit-content;
    }
  }
}

@media (max-width: @max-screen-size) {
  .dropdown-wrapper {
    .info-desc {
      font-size: 12px;
    }
  }
}

@media (max-width: @large-screen-size) {
  .dropdown-wrapper {
    max-height: 80vh;
    overflow: hidden auto;
  }

  .dropdown-product {
    display: flex;
    flex-flow: column;

    .dropdown-category {
      padding: 10px 0;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }

    .category-product {
      border: none;
    }

    .info-name-box {
      .scan-use-tag {
        right: 5px;
      }
    }
  }

  .dropdown-application {
    .category-industry {
      width: 230px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .header-wrapper {
    .nav-menu-right {
      margin-right: 0;
    }
  }

  .dropdown-application {
    .dropdown-category {
      padding: 10px 0;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }

    .dropdown-title {
      margin-bottom: 10px;
    }

    .category-industry {
      width: 100%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .dropdown-cooperation {
    .cooperation-icon {
      width: 35px;
      height: 35px;
      margin-right: 10px;
    }
  }

  .dropdown-application {
    .story-desc {
      width: 400px;
      line-height: 18px;
      white-space: normal;
    }
  }
}

@media (max-width: @least-screen-size) {
  .header-wrapper {
    .header-navbar-show-collapse {
      .dropdown-application {
        .industry-link-box {
          min-width: calc(100% / 2);
        }
      }
    }
  }
}
