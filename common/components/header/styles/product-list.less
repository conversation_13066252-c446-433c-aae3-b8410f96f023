.dropdown-product {
  .info-box {
    width: 100%;
  }

  .info-name-box {
    position: relative;
    display: flex;
    align-items: center;

    .scan-use-tag {
      position: absolute;
      right: -15px;
      height: auto;
    }
  }

  .category-product {
    &:not(:first-of-type) {
      border-left: @border-base;
    }
  }

  .products-list {
    display: flex;
    flex-flow: column;
  }
}

@media (max-width: @middle-screen-size) {
  .dropdown-product {
    display: flex;
    flex-flow: column;

    .category-product {
      border: none;

      &:not(:first-of-type) {
        border-left: none;
      }
    }

    .info-name-box {
      .scan-use-tag {
        right: 5px;
      }
    }
  }
}
