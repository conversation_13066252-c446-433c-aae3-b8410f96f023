@media (max-width: @large-screen-size) {
  .header-navbar-has-brand {
    .navbar-brand {
      margin-right: 30px;
    }

    .nav-menu {
      .nav-link {
        margin: 0 8px;
      }
    }
  }
}

@media (max-width: @middle-screen-size) {
  .navbar-brand {
    margin-right: 30px;
  }

  .nav-menu-product {
    padding-left: 10px;
  }

  .header-navbar-has-menu-right {
    &.header-navbar-has-brand {
      .navbar-brand {
        margin-right: 10px;
      }

      .nav-menu {
        .nav-link {
          margin: 0 5px;
        }
      }
    }

    .nav-menu-product {
      padding-left: 0;
    }

    .nav-menu {
      .nav-link {
        padding: 5px 8px;
      }
    }

    .nav-menu-right {
      margin-right: 10px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .nav-menu {
    .nav-link {
      padding: 5px 10px;
    }
  }

  .nav-menu-product {
    padding-left: 10px;
  }
}

@media (max-width: @mini-screen-size) {
  --header-height: 65px;

  .navbar-brand {
    margin-right: 10px;
  }

  .paoding-logo {
    height: 60px;
  }

  .product-brand-moblie {
    left: 120px;
  }

  .nav-menu {
    .nav-link {
      font-size: 14px;

      &:hover + .dropdown-menu {
        display: block;
      }
    }
  }

  .dropdown-menu {
    &:hover {
      display: block;
    }
  }
}

@media (max-width: 350px) {
  .product-brand-moblie {
    left: 110px;
    padding-left: 0;
  }
}
