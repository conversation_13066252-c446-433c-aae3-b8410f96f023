.dropdown-wrapper {
  display: flex;
  margin-top: 10px;
  padding: 30px 60px;
  background-color: #fff;
  border: @border-base;
  border-radius: @border-radius-base;
  box-shadow: 0 20px 38px rgba(101, 122, 147, 20%);

  .dropdown-category {
    padding: 0 30px;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }

  .dropdown-title {
    margin-bottom: 20px;
    color: #9ea0a5;
    font-weight: 500;
    font-size: 18px;
    line-height: 25px;
    word-break: keep-all;
  }

  .dropdown-link {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0;
    font-size: 14px;
    white-space: nowrap;
    cursor: pointer;

    &:hover {
      background-color: transparent;

      p {
        color: @primary-color;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .gatsby-image-wrapper {
      flex: none;
      width: 36px;
      height: 36px;
      margin-right: 16px;
    }
  }

  .info-name {
    color: @dark-color;
    font-weight: 500;
    font-size: 18px;
    white-space: nowrap;
  }

  .info-desc {
    color: @text-color;
    font-weight: 500;
    font-size: 14px;
  }
}

@media (max-width: @large-screen-size) {
  .dropdown-wrapper {
    padding: 30px 40px;

    .dropdown-category {
      padding: 0 20px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .dropdown-wrapper {
    max-height: 80vh;
    padding: 30px;
    overflow: hidden auto;

    .dropdown-category {
      padding: 20px 0;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }
  }
}
