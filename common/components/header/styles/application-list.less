.dropdown-application {
  .category-industry {
    display: flex;
    flex-direction: column;
    width: 270px;
    height: fit-content;
  }

  .category-story {
    margin-left: 15px;
    border-left: @border-base;
  }

  .industry-box {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
  }

  .industry-link-box {
    display: block;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50%;
    margin-bottom: 0;
    padding: 15px;
    color: @dark-color;
    font-weight: 400;
    font-size: 14px;
    text-align: center;

    &:first-child {
      border-bottom: @border-base;
    }

    &:nth-child(2) {
      border-bottom: @border-base;
      border-left: @border-base;
    }

    &:last-child {
      border-left: @border-base;
    }
  }

  .industry-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 8px 20px;
    border-radius: @border-radius-base;

    &:hover {
      background-color: #6758e51a;
    }
  }

  .industry-icon {
    width: 44px;
    height: 44px;
    margin-bottom: 10px;
  }

  .industry-type {
    color: @dark-color;
    font-size: 14px;
    text-align: center;

    &:hover {
      color: @primary-color;
    }
  }

  .story-link-box {
    display: block;
    margin-bottom: 0;
    padding: 0 0 5px;

    &:not(:last-child) {
      margin-bottom: 5px;
      border-bottom: @border-base;
    }

    .gatsby-image-wrapper {
      position: relative;
      left: -18px;
      width: 130px;
      height: auto;
    }

    .story-img-HKEX {
      left: -37px;
    }

    .story-img-BOC {
      left: -32px;
    }

    .story-img-CGS {
      left: -22px;
    }
  }

  .story-desc {
    color: @dark-color;
    font-size: 14px;
    line-height: 20px;

    &:hover {
      color: @primary-color;
    }
  }
}

@media (max-width: @large-screen-size) {
  .dropdown-application {
    .industry-link-box {
      padding: 10px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .dropdown-application {
    display: flex;
    flex-flow: column;

    .category-industry,
    .category-story {
      width: 100%;
      margin: 0;
    }

    .industry-box {
      justify-content: space-between;
    }

    .industry-link-box {
      width: 25%;
      padding: 0;

      &:first-child,
      &:nth-child(2),
      &:last-child {
        border: none;
      }
    }

    .industry-link {
      padding: 5px 20px;
    }

    .industry-icon {
      width: 40px;
      height: 40px;
      margin-bottom: 5px;
    }

    .category-story {
      border: none;
    }
  }
}
