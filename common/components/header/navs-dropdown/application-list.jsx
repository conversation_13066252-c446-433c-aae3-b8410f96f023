import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { NavDropdown } from 'react-bootstrap';
import { getUrlWithProduct } from '../../../urls';
import { useHeaderContext } from '../../../hooks/useHeaderContextHook';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import '../styles/dropdown-list.less';
import '../styles/application-list.less';

const ApplicationList = ({
  data: {
    allCustomerCaseData: { nodes: customerCaseDataNodes },
    allApplicationData: { nodes: applicationDataNodes },
  },
}) => {
  const { handleCloseCollapse } = useHeaderContext();
  const { getFormatMessage } = useFormatMessage();

  return (
    <div className="dropdown-wrapper dropdown-application">
      <div className="dropdown-category category-industry">
        <p className="dropdown-title">{getFormatMessage('行业')}</p>
        <div className="industry-box">
          {applicationDataNodes[0].childrenApplicationJson.map((industry) => (
            <NavDropdown.Item
              className="industry-link-box dropdown-link"
              key={industry.type}
              href={getUrlWithProduct('paodingai', industry.name)}
              target="_blank"
              title={`${getFormatMessage(industry.type)} ${getFormatMessage(
                '行业应用',
              )}`}
              onClick={handleCloseCollapse}>
              <div className="industry-link">
                <img
                  src={industry.icon.publicURL}
                  alt="icon"
                  className="industry-icon"
                />
                <p className="industry-type">
                  {getFormatMessage(industry.type)}
                </p>
              </div>
            </NavDropdown.Item>
          ))}
        </div>
      </div>
      <div className="dropdown-category category-story">
        <p className="dropdown-title">{getFormatMessage('成功故事')}</p>
        {customerCaseDataNodes[0].childrenCustomerCaseJson.map((customer) => (
          <NavDropdown.Item
            className="story-link-box dropdown-link"
            key={customer.name}
            href={getUrlWithProduct('paodingai', customer.type)}
            target="_blank"
            title={`${getFormatMessage('成功故事')}${getFormatMessage(
              '冒号符号',
            )}${getFormatMessage(customer.name)}${getFormatMessage(
              '冒号符号',
            )}${getFormatMessage(customer.articleInfo.title)}`}
            onClick={handleCloseCollapse}>
            <GatsbyImage
              className={`story-img-${customer.type}`}
              image={customer.icon.childImageSharp.gatsbyImageData}
              alt="story-img"
            />
            <p className="story-desc">
              {getFormatMessage(customer.name)}
              {getFormatMessage('冒号符号')}
              {getFormatMessage(customer.articleInfo.title)}
            </p>
          </NavDropdown.Item>
        ))}
      </div>
    </div>
  );
};

export default ApplicationList;
