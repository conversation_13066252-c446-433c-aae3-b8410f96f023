import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { ScanUseTag } from '../../tags/tags';
import { getUrlWithProduct } from '../../../urls';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { useHeaderContext } from '../../../hooks/useHeaderContextHook';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import { SOURCE, SOURCE_MAP } from '../../../../common/data/source';
import '../styles/dropdown-list.less';
import '../styles/product-list.less';

const ProductList = ({ data }) => {
  const {
    allProductsData: { nodes },
  } = data;
  const isMobile = useMobilePageContext();
  const { getFormatMessage } = useFormatMessage();
  const { productName, handleCloseCollapse, isShowCollapse } =
    useHeaderContext();

  const getProductHref = (href) => {
    if (href?.includes('cooperation/connect') && productName) {
      return `${href}&source=${encodeURIComponent(productName)}`;
    }
    return href;
  };

  return (
    <div className="dropdown-wrapper dropdown-product">
      {nodes[0].childrenProductsJson.map((item) => (
        <div
          className="dropdown-category category-product"
          key={item.headerSubname}>
          <p className="dropdown-title">
            {getFormatMessage(item.headerSubname)}
          </p>
          <div className="products-list">
            {/* 暂时隐藏MetalMesh的入口。 */}
            {item.productList
              .filter(
                (product) => product.name !== SOURCE_MAP[SOURCE.METALMESH],
              )
              .map((product) => (
                <a
                  key={product.name}
                  href={getProductHref(
                    getUrlWithProduct(
                      product.url.product,
                      product.url.pathName,
                    ),
                  )}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="dropdown-link product-link"
                  onClick={handleCloseCollapse}>
                  <GatsbyImage
                    image={
                      product.featuredImage.childImageSharp.gatsbyImageData
                    }
                    alt="product-img"
                  />
                  <div className="info-box">
                    <p className="info-desc">
                      {product.headerTitle
                        ? getFormatMessage(product.headerTitle)
                        : getFormatMessage(product.title)}
                    </p>
                    <div className="info-name-box">
                      <p className="info-name">
                        {product.chineseName
                          ? getFormatMessage(product.chineseName)
                          : getFormatMessage(product.name)}
                      </p>
                      {!isMobile && !isShowCollapse && product.canScanLogin && (
                        <ScanUseTag />
                      )}
                    </div>
                  </div>
                </a>
              ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductList;
