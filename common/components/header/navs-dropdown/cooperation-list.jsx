import React from 'react';
import { NavDropdown } from 'react-bootstrap';
import studentSvg from '../../../assets/images/connect-student.svg';
import createSvg from '../../../assets/images/connect-create.svg';
import teacherSvg from '../../../assets/images/connect-teacher.svg';
import { getUrlWithProduct } from '../../../urls';
import { useHeaderContext } from '../../../hooks/useHeaderContextHook';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import '../styles/dropdown-list.less';
import '../styles/cooperation-list.less';

const CONNECT_TYPE_DATA = [
  {
    name: 'explore',
    headerType: '行业合作',
    icon: createSvg,
    headerLinkDesc: '提出您的文档智能需求1',
  },
  {
    name: 'student',
    headerType: '在校学生',
    icon: studentSvg,
    headerLinkDesc: '申请免费使用产品',
  },
  {
    name: 'academic',
    headerType: '高校教师',
    icon: teacherSvg,
    headerLinkDesc: '申请学术科研合作',
  },
];

const CooperationList = () => {
  const { productName, handleCloseCollapse } = useHeaderContext();
  const { getFormatMessage } = useFormatMessage();

  const getCooperationHref = (href) => {
    if (productName) {
      return `${href}?source=${encodeURIComponent(productName)}`;
    }
    return href;
  };

  return (
    <div className="dropdown-wrapper dropdown-cooperation">
      <div className="dropdown-category category-cooperation">
        <p className="dropdown-title">{getFormatMessage('合作与生态')}</p>
        <div className="cooperation-box">
          {CONNECT_TYPE_DATA.map((type) => (
            <NavDropdown.Item
              key={type.name}
              className="dropdown-link cooperation-link"
              href={getCooperationHref(
                getUrlWithProduct('paodingai', type.name),
              )}
              target="_blank"
              title={getFormatMessage(type.headerLinkDesc)}
              onClick={handleCloseCollapse}>
              <img src={type.icon} alt="icon" className="cooperation-icon" />
              <div>
                <p className="info-name">{getFormatMessage(type.headerType)}</p>
                <p className="info-desc">
                  {getFormatMessage(type.headerLinkDesc)}
                </p>
              </div>
            </NavDropdown.Item>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CooperationList;
