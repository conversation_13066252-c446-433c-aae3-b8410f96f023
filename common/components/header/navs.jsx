import React, { useCallback, useEffect, useMemo } from 'react';
import { Nav, NavDropdown } from 'react-bootstrap';
import { ScrollNav } from '../../components/scroll/pageScroll/page-scroll';
import ProductBrand from './product-brand';
import ProductList from './navs-dropdown/product-list';
import ApplicationList from './navs-dropdown/application-list';
import CooperationList from './navs-dropdown/cooperation-list';
import { getUrlWithProduct } from '../../urls';
import { useHeaderContext } from '../../hooks/useHeaderContextHook';
import { useFormatMessage } from '../../hooks/useFormatMessageHook';
import { useHandleResize } from '../../hooks/useHandleResizeHook';
import classnames from 'classnames';

const Navs = ({ data }) => {
  const { getFormatMessage } = useFormatMessage();
  const aboutUsHref = getUrlWithProduct('paodingai', 'aboutUs');
  const blogHref = getUrlWithProduct('paodingai', 'blog');

  const {
    currentProductData,
    hideProduct,
    hideApplication,
    hideCooperation,
    hideAboutUs,
    hideBlog,
    navMenuRight,
    showProductBrand,
    handleCloseCollapse,
    isShowCollapse,
  } = useHeaderContext();

  const toggleBodyScrollOnCollapse = (hasNavMenuContainerScrollBar) => {
    document.body.classList.toggle('no-scroll', hasNavMenuContainerScrollBar);
  };

  const checkNavMenuContainerScrollBar = useCallback(() => {
    const navMenuContainer = document.querySelector('.nav-menu-container');
    setTimeout(() => {
      const hasNavMenuContainerScrollBar =
        isShowCollapse &&
        navMenuContainer &&
        navMenuContainer.scrollHeight > navMenuContainer.clientHeight;
      toggleBodyScrollOnCollapse(hasNavMenuContainerScrollBar);
    });
  }, [isShowCollapse]);

  const handleClick = useCallback(
    (e) => {
      checkNavMenuContainerScrollBar();
      if (Array.from(e.target.classList).includes('nav-item')) {
        handleCloseCollapse();
      }
      if (
        isShowCollapse &&
        Array.from(e.target.classList).includes('nav-dropdown-toggle')
      ) {
        setTimeout(() => {
          e.target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 10);
      }
    },
    [isShowCollapse, handleCloseCollapse, checkNavMenuContainerScrollBar],
  );

  useEffect(() => {
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [handleClick]);

  const closeExpandedNav = (e) => {
    const id = e.target.id;
    const expandEl = document.querySelector("[aria-expanded='true']");
    if (expandEl) {
      if (id !== expandEl.id) {
        const dropdownEl = document.querySelector(
          `[aria-labelledby='${expandEl.id}']`,
        );
        dropdownEl.classList = 'dropdown-menu';
        document.querySelector("[aria-expanded='true']").ariaExpanded = false;
      }
    }
  };

  useEffect(() => {
    if (!isShowCollapse) {
      const navsElList = document.querySelectorAll('.dropdown-toggle');
      navsElList.forEach((navEl) => {
        navEl.addEventListener('mouseenter', closeExpandedNav);
      });

      return () => {
        navsElList.forEach((navEl) => {
          navEl.removeEventListener('mouseenter', closeExpandedNav);
        });
      };
    }
  }, [isShowCollapse]);

  const productDropdownTitle = useMemo(() => {
    if (showProductBrand && !isShowCollapse) {
      return <ProductBrand currentProductData={currentProductData} />;
    } else {
      return getFormatMessage('产品');
    }
  }, [currentProductData, showProductBrand, isShowCollapse, getFormatMessage]);

  useHandleResize(() => {
    checkNavMenuContainerScrollBar();
  }, [checkNavMenuContainerScrollBar]);

  return (
    <div className="nav-menu-container">
      <Nav
        className={classnames({
          'nav-menu': true,
          'nav-menu-product': true,
          'nav-menu-product-brand': showProductBrand && !isShowCollapse,
        })}>
        {!hideProduct && (
          <NavDropdown
            className="product-dropdown dropdown-has-arrow"
            title={productDropdownTitle}
            bsPrefix="nav-link nav-dropdown-toggle"
            id="product-dropdown"
            renderMenuOnMount>
            <ProductList data={data} />
          </NavDropdown>
        )}
      </Nav>
      <Nav className="nav-menu nav-menu-left">
        {!hideApplication && (
          <NavDropdown
            title={getFormatMessage('客户案例')}
            bsPrefix="nav-link nav-dropdown-toggle"
            className="dropdown-has-arrow"
            id="application-dropdown"
            renderMenuOnMount>
            <ApplicationList data={data} />
          </NavDropdown>
        )}
        {!hideCooperation && (
          <NavDropdown
            title={getFormatMessage('合作与生态')}
            bsPrefix="nav-link nav-dropdown-toggle"
            className="dropdown-has-arrow"
            id="cooperation-dropdown"
            renderMenuOnMount>
            <CooperationList />
          </NavDropdown>
        )}
        {!hideAboutUs && (
          <Nav.Link className="nav-item" target="_blank" href={aboutUsHref}>
            {getFormatMessage('关于我们')}
          </Nav.Link>
        )}
        {!hideBlog && (
          <Nav.Link className="nav-item" target="_blank" href={blogHref}>
            {getFormatMessage('新闻中心')}
          </Nav.Link>
        )}
      </Nav>
      <Nav className="nav-menu nav-menu-right">{navMenuRight}</Nav>
    </div>
  );
};

export default ScrollNav(Navs);
