import React from 'react';
import PropTypes from 'prop-types';
import arrowDownSvg from '../../assets/images/arrow-down.svg';
import { useFormatMessage } from '../../hooks/useFormatMessageHook';
import './styles/product-brand.less';

const ProductBrand = ({ currentProductData, hideArrow }) => {
  const { getFormatMessage } = useFormatMessage();
  return (
    <div className="product-brand">
      <img
        src={currentProductData.featuredImage.publicURL}
        alt={`${currentProductData.name}-icon`}
        className="product-brand-icon"
      />
      <div className="product-brand-info">
        <span className="product-brand-desc">
          {getFormatMessage(currentProductData.title)}
        </span>
        <span className="product-brand-title">
          {currentProductData.chineseName
            ? getFormatMessage(currentProductData.chineseName)
            : getFormatMessage(currentProductData.name)}
        </span>
      </div>
      {!hideArrow && (
        <img
          src={arrowDownSvg}
          alt="arrow-down"
          className="product-brand-arrow"
        />
      )}
    </div>
  );
};

ProductBrand.propTypes = {
  currentProductData: PropTypes.object,
  hideArrow: PropTypes.bool,
};

export default ProductBrand;
