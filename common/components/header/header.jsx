import React, { useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Container, Navbar } from 'react-bootstrap';
import Navs from './navs';
import ProductBrand from './product-brand';
import ConnectBtn from '../connect/connectButton/connect-btn';
import LangSwitch from '../lang/lang-switch';
import paoding<PERSON>ogo from '../../assets/logo/paoding-logo.png';
import menuMobileSvg from '../../assets/images/menu-mobile.svg';
import closeSvg from '../../assets/images/close.svg';
import { SCREEN_SIZE_WIDTH } from '../../data/constant';
import { getUrlWithProduct } from '../../urls';
import { getEnvVariables } from '../../utils/env';
import { HeaderContext } from '../../hooks/useHeaderContextHook';
import { useHandleResize } from '../../hooks/useHandleResizeHook';
import { useGlobalContext } from '../../hooks/useGlobalHook';
import classnames from 'classnames';
import './styles/header.less';

const Header = ({
  showHeaderFeature,
  productName,
  showProductBrand,
  navMenuRight,
  navbarExpand = 'md',
  hideProduct,
  hideApplication,
  hideCooperation,
  hideAboutUs,
  announceBar,
  data,
}) => {
  const [isShowHeaderShadow, setIsShowHeaderShadow] = useState(false);
  const [isShowCollapse, setIsShowCollapse] = useState(false);
  const [collapseOpen, setCollapseOpen] = useState(false);
  const paodingaiHref = getUrlWithProduct('paodingai', 'index');

  const { lang, setLang } = useGlobalContext();

  const {
    allProductsData: { nodes },
  } = data;

  const currentProductData = useMemo(() => {
    const productList = nodes[0].childrenProductsJson.flatMap(
      (item) => item.productList,
    );
    let product = productList.find((item) => item.name === productName);
    if (!product) {
      product = productList.find(
        (item) => item.name === productName?.split(' ')[0],
      );
    }
    return product;
  }, [nodes, productName]);

  const handleCloseCollapse = () => {
    setCollapseOpen(false);
  };

  const stopPropagation = (e) => {
    e.stopPropagation();
  };

  const getShowCollapse = () => {
    if (
      (window.innerWidth <= SCREEN_SIZE_WIDTH.SMALL_SCREEN_SIZE &&
        navbarExpand === 'lg') ||
      (window.innerWidth <= SCREEN_SIZE_WIDTH.MINI_SCREEN_SIZE &&
        navbarExpand === 'md')
    ) {
      setIsShowCollapse(true);
    } else {
      setIsShowCollapse(false);
    }
  };

  const getPageHeight = () => {
    document.documentElement.style.setProperty(
      '--page-height',
      `${window.innerHeight}px`,
    );
  };

  useHandleResize(() => {
    getPageHeight();
    getShowCollapse();
  });

  const handleScroll = (e) => {
    const scrollTop = e.srcElement.scrollingElement.scrollTop;
    setIsShowHeaderShadow(!!scrollTop);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const headerContext = useMemo(() => {
    return {
      productName,
      hideProduct,
      hideApplication,
      hideCooperation,
      hideAboutUs,
      navMenuRight,
      showProductBrand,
      handleCloseCollapse,
      isShowCollapse,
      currentProductData,
    };
  }, [
    isShowCollapse,
    productName,
    hideProduct,
    hideApplication,
    hideCooperation,
    hideAboutUs,
    navMenuRight,
    showProductBrand,
    currentProductData,
  ]);

  return (
    <HeaderContext.Provider value={headerContext}>
      <header
        className={classnames({
          'header-wrapper': true,
          'header-relative': !showHeaderFeature,
          shadow: showHeaderFeature && isShowHeaderShadow,
        })}>
        <Container>
          <Navbar
            className={classnames({
              'header-navbar': true,
              'header-navbar-show-collapse': isShowCollapse,
              'header-navbar-has-brand': showProductBrand && !isShowCollapse,
              'header-navbar-has-menu-right': navMenuRight && !isShowCollapse,
            })}
            bg="#3E3F42"
            expand={navbarExpand}>
            <Navbar.Brand href={paodingaiHref}>
              <img
                src={paodingLogo}
                className="paoding-logo"
                alt="北京庖丁科技有限公司"
              />
            </Navbar.Brand>
            {showProductBrand && isShowCollapse && (
              <Navbar.Brand className="product-brand-moblie">
                <ProductBrand
                  currentProductData={currentProductData}
                  hideArrow
                />
              </Navbar.Brand>
            )}
            {showHeaderFeature && (
              <>
                <Navbar.Toggle
                  aria-controls="basic-navbar-nav"
                  onClick={() => setCollapseOpen(!collapseOpen)}>
                  <img
                    src={collapseOpen ? closeSvg : menuMobileSvg}
                    alt="navbar"
                  />
                </Navbar.Toggle>
                <Navbar.Collapse
                  id="basic-navbar-nav"
                  in={collapseOpen}
                  onPointerDown={handleCloseCollapse}>
                  <div
                    className="navbar-dropdown"
                    onPointerDown={stopPropagation}>
                    {lang && isShowCollapse && (
                      <LangSwitch
                        lang={lang}
                        setLang={setLang}
                        isShowCollapse
                      />
                    )}
                    <Navs data={data} />
                    <div className="nav-btn" onClick={handleCloseCollapse}>
                      {lang && !isShowCollapse && (
                        <LangSwitch lang={lang} setLang={setLang} />
                      )}
                      <ConnectBtn productName={productName} />
                    </div>
                  </div>
                </Navbar.Collapse>
              </>
            )}
          </Navbar>
        </Container>
        {announceBar}
      </header>
    </HeaderContext.Provider>
  );
};

Header.defaultProps = {
  navbarExpand: 'md',
  productName: getEnvVariables().currentProduct,
};

Header.propTypes = {
  showHeaderFeature: PropTypes.bool,
  productName: PropTypes.string,
  showProductBrand: PropTypes.bool,
  navMenuRight: PropTypes.element,
  navbarExpand: PropTypes.string,
  hideProduct: PropTypes.bool,
  hideApplication: PropTypes.bool,
  hideCooperation: PropTypes.bool,
  hideAboutUs: PropTypes.bool,
};

export default Header;
