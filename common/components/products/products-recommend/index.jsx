import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import CommonSwiper from '../../swiper/common-swiper';
import { getUrlWithProduct } from '../../../urls';
import recommendTitlePng from '../../../assets/images/products-recommend.png';
import classnames from 'classnames';
import './index.less';

const ProductCard = ({ item }) => {
  return (
    <div className="product-card">
      <GatsbyImage
        className="product-logo"
        image={item.image.childImageSharp.gatsbyImageData}
        alt={`${item.name} logo`}
      />
      <p className="product-content">{item.content}</p>
      <a
        href={getUrlWithProduct(item.name.toLowerCase(), 'index')}
        target="_blank"
        rel="noreferrer"
        className="product-link">
        试用
      </a>
    </div>
  );
};

const ProductsRecommend = ({ className, title, data }) => {
  const productsData = data.allProductCaseData.nodes[0].childrenProductCaseJson;

  return (
    <div
      className={classnames({
        'products-recommend': true,
        [className]: className,
      })}>
      <Container className="products-container">
        <div className="recommend-title">{title}</div>
        <img
          src={recommendTitlePng}
          className="recommend-title-img"
          alt="recommend-title"
        />
        <div className="products-list">
          {productsData.map((item) => (
            <ProductCard item={item} key={item.id} />
          ))}
        </div>
        <CommonSwiper
          className="products-list-swiper products-list-mobile"
          swiperData={productsData}
          swiperSlideChildren={(item) => (
            <ProductCard item={item} key={item.id} />
          )}
        />
      </Container>
    </div>
  );
};

ProductsRecommend.defaultProps = {
  title: 'AI产品推荐',
};

ProductsRecommend.propTypes = {
  className: PropTypes.string,
  title: PropTypes.string,
  data: PropTypes.object,
};

export default ProductsRecommend;
