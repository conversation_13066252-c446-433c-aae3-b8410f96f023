.products-recommend {
  padding: 35px 0 43px;

  .products-container {
    width: 1050px;
  }

  .recommend-title {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    color: #303133;
    font-weight: 500;
    font-size: 26px;
  }

  .recommend-title-img {
    display: none;
    width: 195px;
    margin: 0 0 20px 30px;
  }

  .products-list {
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    column-gap: 25px;
  }

  .products-list-mobile {
    display: none;
  }

  .product-card {
    display: flex;
    flex-flow: column;
    align-items: flex-start;
    width: 350px;
    padding: 25px 31px 19px;
    background-image: linear-gradient(180deg, #fff 0%, #fff 100%);
    border-radius: 10px;
    box-shadow: 1px 1px 15px 0 rgba(29, 53, 139, 7%);
    row-gap: 10px;
  }

  .product-logo {
    max-width: 139px;
  }

  .product-content {
    color: #7a8ba6;
    font-size: 14px;
    line-height: 25px;
  }

  .product-link {
    align-self: flex-end;
    width: 64px;
    height: 28px;
    color: #6757e5;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    text-decoration: none;
    background: rgba(255, 255, 255, 0%);
    border: 1px solid #6757e5;
    border-radius: 3px;
  }
}

@media (max-width: @mini-screen-size) {
  .products-recommend {
    .products-list {
      display: none;
    }

    .products-list-mobile {
      display: block;
    }

    .recommend-title {
      margin: 0;
      font-size: 25px;
    }

    .products-container {
      width: 100%;
    }

    .products-list-swiper {
      background-color: #fff;

      .swiper-slide {
        width: fit-content !important;
        margin: 20px;
        border: 1px solid #edf0f5;
        border-radius: 10px;
        box-shadow: 1px 1px 20px 0 #1d358b1a;
      }

      .swiper-pagination {
        position: relative;
        bottom: 10px;
        margin-top: 0;
        line-height: unset;
      }

      .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        margin: 0 4px !important;
        background-color: #ccc;
        opacity: 1;
      }

      .swiper-pagination-bullet-active {
        background-color: @primary-color;
      }
    }

    .product-card {
      width: 100%;
    }
  }
}
