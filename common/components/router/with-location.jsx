import React from 'react';
import { Location } from '@reach/router';
import queryString from 'query-string';

/**
 * component获取路由信息HOC
 * @param ComponentToWrap
 * @return {function(*): *}
 */
const withLocation = (ComponentToWrap) => (props) => (
  <Location>
    {({ location, navigate }) => (
      <ComponentToWrap
        {...props}
        location={location}
        navigate={navigate}
        search={location.search ? queryString.parse(location.search) : ''}
      />
    )}
  </Location>
);

export default withLocation;
