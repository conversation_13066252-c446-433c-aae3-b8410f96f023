import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import { GatsbyImage } from 'gatsby-plugin-image';
import { getUrlWithProduct } from '../../../urls';
import { getEnvVariables } from '../../../utils/env';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import './footer.less';
import { useCheckUrl } from '../../../hooks/useCheckUrl';

const Footer = ({ currentFooterBg, currentFooterBgMini, linkedProduct }) => {
  const { product } = getEnvVariables();
  const src = useGetUrlQueryData('src');
  const {
    defaultSrc,
    footerBg,
    footerBgMini,
    hidePaperInFooter,
    hideLogInFooter,
  } = useGlobalContext();

  const currentProduct = linkedProduct || product;

  const policyLink = useMemo(() => {
    return getUrlWithProduct(currentProduct, 'policy');
  }, [currentProduct]);

  const teamsLink = useMemo(() => {
    return getUrlWithProduct(currentProduct, 'terms');
  }, [currentProduct]);

  const changelogLink = useMemo(() => {
    return getChatDOCLink(getUrlWithProduct(currentProduct, 'changelog'), {
      src: src || defaultSrc,
    });
  }, [currentProduct, defaultSrc, src]);

  const { loading: policyLoading, handleLinkClicked: handlePolicyLinkCLicked } =
    useCheckUrl(policyLink);

  const { loading: teamsLoading, handleLinkClicked: handleTeamsLinkClicked } =
    useCheckUrl(teamsLink);

  const {
    loading: changelogLoading,
    handleLinkClicked: handleChangelogLinkClicked,
  } = useCheckUrl(changelogLink);

  const faqsFooterLink = useMemo(() => {
    return [
      {
        name: 'Paper',
        link: getUrlWithProduct('chatpaper', 'project'),
        hide: hidePaperInFooter,
      },
      {
        name: 'Policy',
        link: policyLink,
        loading: policyLoading,
        onClick: handlePolicyLinkCLicked,
      },
      {
        name: 'Terms',
        link: teamsLink,
        loading: teamsLoading,
        onClick: handleTeamsLinkClicked,
      },
      {
        name: 'Twitter',
        link: getUrlWithProduct('other', 'chatdocTwitter'),
      },
      {
        name: 'Discord',
        link: getUrlWithProduct('other', 'chatdocDiscord'),
      },
      {
        name: 'Blog',
        link: getChatDOCLink(getUrlWithProduct(product, 'blog'), {
          src: src || defaultSrc,
        }),
      },
      {
        name: 'Changelog',
        link: changelogLink,
        hide: hideLogInFooter,
        loading: changelogLoading,
        onClick: handleChangelogLinkClicked,
      },
    ];
  }, [
    changelogLink,
    changelogLoading,
    defaultSrc,
    handleChangelogLinkClicked,
    handlePolicyLinkCLicked,
    handleTeamsLinkClicked,
    hideLogInFooter,
    hidePaperInFooter,
    policyLink,
    policyLoading,
    product,
    src,
    teamsLink,
    teamsLoading,
  ]);

  return (
    <footer className="footer-wrapper">
      <div>
        {faqsFooterLink.map(
          (item, index) =>
            !item.hide && (
              <React.Fragment key={index}>
                <a
                  href={item.link}
                  target="_blank"
                  rel="noreferrer"
                  className={classnames({
                    'footer-link': true,
                    'link-loading': item.loading,
                  })}
                  onClick={item.onClick || null}>
                  {item.name}
                </a>
                <span className="footer-point"> · </span>
              </React.Fragment>
            ),
        )}
      </div>
      <GatsbyImage
        image={(currentFooterBg || footerBg).childImageSharp.gatsbyImageData}
        alt="Footer Background"
        className="footer-bg"
      />
      <GatsbyImage
        image={
          (currentFooterBgMini || footerBgMini).childImageSharp.gatsbyImageData
        }
        alt="Footer Background Mini"
        className="footer-bg-mini"
      />
    </footer>
  );
};

Footer.propTypes = {
  currentFooterBg: PropTypes.object,
  currentFooterBgMini: PropTypes.object,
  linkedProduct: PropTypes.string,
};

export default Footer;
