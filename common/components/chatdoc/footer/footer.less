.footer-wrapper {
  position: relative;
  z-index: 0;
  padding-bottom: 60px;
  text-align: center;

  .footer-link {
    color: @dark-text-color;
    font-size: 20px;
    line-height: 36px;
    text-decoration: underline;
    text-underline-position: under;
  }

  .footer-point {
    padding: 0 3px;
    color: @dark-text-color;
    font-size: 20px;

    &:last-child {
      display: none;
    }
  }

  .footer-bg,
  .footer-bg-mini {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -1;
    width: 100%;
  }

  .footer-bg-mini {
    display: none;
  }
}

@media (max-width: @mini-screen-size) {
  .footer-wrapper {
    padding: 0 20px 24px;

    .footer-link {
      font-size: 14px;
      line-height: 36px;
    }

    .footer-point {
      padding: 0 2px;
      font-size: 14px;
    }

    .footer-bg {
      display: none;
    }

    .footer-bg-mini {
      display: block;
    }
  }
}
