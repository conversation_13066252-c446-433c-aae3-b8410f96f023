import React from 'react';
import { Container } from 'react-bootstrap';
import ProjectButton from '../button/project-button';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './link.less';

const Link = () => {
  const { linkData, linkButtonText, isShowAnimationButtonInLink } =
    useGlobalContext();

  return (
    <div className="link-wrapper">
      <Container className="link-container">
        <div className="link-content">
          <div>
            <h3 className="common-title link-title">{linkData.title}</h3>
            <p className="common-subtitle link-subtitle">{linkData.desc}</p>
          </div>
          <ProjectButton
            className="link-btn"
            text={linkButtonText || 'Get Started'}
            isShowAnimationButton={isShowAnimationButtonInLink}
          />
        </div>
      </Container>
    </div>
  );
};

export default Link;
