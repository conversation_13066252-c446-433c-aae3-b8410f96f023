import React from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './not-found.less';

const NotFound = () => {
  const { notFoundImage } = useGlobalContext();

  return (
    <div className="not-found-wrapper">
      <Container>
        <p className="text">The page you are looking for can’t be found.</p>
        <GatsbyImage
          className="image"
          alt="Not Found Image"
          image={notFoundImage.childImageSharp.gatsbyImageData}
        />
      </Container>
    </div>
  );
};

export default NotFound;
