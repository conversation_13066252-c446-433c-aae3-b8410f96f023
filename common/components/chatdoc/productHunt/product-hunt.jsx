import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Nav } from 'react-bootstrap';
// import productHuntLight from '../../../assets/images/chatdoc/product-hunt-light.svg';
// import productHuntDark from '../../../assets/images/chatdoc/product-hunt-dark.svg';
import productHuntLight from '../../../assets/images/chatdoc/product-hunt-light-top.svg';
import productHuntDark from '../../../assets/images/chatdoc/product-hunt-dark-top.svg';
import { getUrlWithProduct } from '../../../urls';
import { useCheckDarkMode } from '../../../../common/hooks/useCheckDarkMode';
import classnames from 'classnames';
import './product-hunt.less';

const ProductHunt = ({ className }) => {
  const [showRemoteFile, setShowRemoteFile] = useState(false);
  const productHuntHref = getUrlWithProduct('other', 'chatdocProductHunt');
  const isDarkMode = useCheckDarkMode();

  const altText =
    'ChatDOC - GPT for any file - Chat with any documents and get responses with cited sources | Product Hunt';

  const remoteUrl = useMemo(() => {
    const theme = isDarkMode ? 'dark' : 'light';
    return `https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=420087&theme=${theme}&period=daily`;
  }, [isDarkMode]);

  const localUrl = useMemo(() => {
    return isDarkMode ? productHuntDark : productHuntLight;
  }, [isDarkMode]);

  useEffect(() => {
    const img = new Image();
    img.src = remoteUrl;
    img.onerror = () => {
      setShowRemoteFile(false);
    };
    img.onload = () => {
      setShowRemoteFile(true);
    };
  }, [remoteUrl]);

  return (
    <Nav.Link
      href={productHuntHref}
      target="_blank"
      className={classnames({
        'product-hunt': true,
        [className]: className,
      })}>
      <img
        src={showRemoteFile ? remoteUrl : localUrl}
        alt={altText}
        className="product-hunt-img"
      />
    </Nav.Link>
  );
};

ProductHunt.propTypes = {
  className: PropTypes.string,
};

export default ProductHunt;
