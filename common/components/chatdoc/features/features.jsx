import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Lazy<PERSON>oad, { forceCheck } from 'react-lazyload';
import { Container, Row, Col, Carousel } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { NewTag } from '../../tags/tags';
import XGPlayerVideo from '../../video/xgplayer-video/xgplayer-video';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import classnames from 'classnames';
import './features.less';

const FeatureVideo = ({ videoData }) => {
  return (
    <XGPlayerVideo
      className="video-item"
      src={videoData.video.publicURL}
      poster={videoData.poster}
    />
  );
};

const FeatureItem = ({ featureItemData }) => {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const isMobile = useMobilePageContext();

  const handleChangeFeature = (descIndex) => {
    const delayTime = isMobile ? 500 : 10;
    setCurrentVideoIndex(descIndex);
    setTimeout(() => {
      forceCheck();
    }, delayTime);
  };

  return (
    <div className="feature-item">
      <Container>
        <Row className="feature-row">
          <Col lg={4} md={12} className="feature-content">
            <div className="content-title">
              <GatsbyImage
                image={featureItemData.icon.childImageSharp.gatsbyImageData}
                alt={`${featureItemData.name}-icon`}
                class="feature-icon"
              />
              <h3 className="title-text">{featureItemData.title}</h3>
            </div>
            <p className="content-subtitle">{featureItemData.subtitle}</p>
            <ol className="desc-list">
              {featureItemData.descList.map((descItem, descIndex) => (
                <li
                  key={descIndex}
                  className={classnames({
                    'desc-item': true,
                    'list-item-active': descIndex === currentVideoIndex,
                  })}
                  onClick={() => handleChangeFeature(descIndex)}>
                  <div className="list-item-selected desc-item-selected"></div>
                  <p className="list-item-text desc-item-text">
                    {descItem.desc}
                    {descItem.isNew && <NewTag />}
                  </p>
                </li>
              ))}
            </ol>
          </Col>
          <div className="feature-video">
            <Carousel
              interval={null}
              activeIndex={currentVideoIndex}
              controls={false}
              indicators={false}
              className="carousel-vertically">
              {featureItemData.descList.map((descItem, descIndex) => (
                <Carousel.Item key={descIndex}>
                  {descIndex === 0 ? (
                    <FeatureVideo videoData={descItem} />
                  ) : (
                    <LazyLoad>
                      <FeatureVideo videoData={descItem} />
                    </LazyLoad>
                  )}
                </Carousel.Item>
              ))}
            </Carousel>
          </div>
        </Row>
      </Container>
    </div>
  );
};

const Features = () => {
  const { featuresData } = useGlobalContext();

  return (
    <div className="features-wrapper">
      {featuresData.map((item, index) => (
        <FeatureItem featureItemData={item} key={index} />
      ))}
    </div>
  );
};

FeatureItem.propTypes = {
  featureItemData: PropTypes.object,
};

export default Features;
