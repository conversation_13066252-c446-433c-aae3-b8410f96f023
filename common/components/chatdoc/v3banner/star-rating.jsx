import React from 'react';
import './star-rating.less';

const Star = ({ fill = 1, index }) => {
  const gradId = `starGrad${index}`;
  return (
    <span className="star" style={{ position: 'relative' }}>
      <svg
        width="20"
        height="18"
        viewBox="22 1 12 17"
        style={{ position: 'absolute', top: 0, left: 0 }}>
        <path
          d="M28.6549 2.72548C29.2051 1.61061 30.7949 1.61061 31.3451 2.72548L32.5899 5.24775C32.8084 5.69047 33.2308 5.99732 33.7193 6.06831L36.5028 6.47278C37.7332 6.65156 38.2244 8.16352 37.3341 9.03132L35.32 10.9946C34.9665 11.3392 34.8051 11.8357 34.8886 12.3223L35.3641 15.0946C35.5742 16.3199 34.2881 17.2544 33.1876 16.6758L30.698 15.367C30.261 15.1372 29.739 15.1372 29.302 15.367L26.8124 16.6758C25.7119 17.2544 24.4258 16.3199 24.6359 15.0946L25.1114 12.3223C25.1949 11.8357 25.0335 11.3392 24.68 10.9946L22.6659 9.03132C21.7756 8.16352 22.2668 6.65156 23.4972 6.47278L26.2807 6.06831C26.7692 5.99732 27.1916 5.69047 27.4101 5.24775L28.6549 2.72548Z"
          fill="#444"
        />
      </svg>
      <svg
        width="20"
        height="18"
        viewBox="22 1 12 17"
        style={{ position: 'relative', zIndex: 1 }}>
        <defs>
          <linearGradient id={gradId} x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stopColor="#F6BD3F" />
            <stop offset="50%" stopColor="#F6BD3F" />
            <stop offset="100%" stopColor="#FCECC7" />
          </linearGradient>
        </defs>
        <path
          d="M28.6549 2.72548C29.2051 1.61061 30.7949 1.61061 31.3451 2.72548L32.5899 5.24775C32.8084 5.69047 33.2308 5.99732 33.7193 6.06831L36.5028 6.47278C37.7332 6.65156 38.2244 8.16352 37.3341 9.03132L35.32 10.9946C34.9665 11.3392 34.8051 11.8357 34.8886 12.3223L35.3641 15.0946C35.5742 16.3199 34.2881 17.2544 33.1876 16.6758L30.698 15.367C30.261 15.1372 29.739 15.1372 29.302 15.367L26.8124 16.6758C25.7119 17.2544 24.4258 16.3199 24.6359 15.0946L25.1114 12.3223C25.1949 11.8357 25.0335 11.3392 24.68 10.9946L22.6659 9.03132C21.7756 8.16352 22.2668 6.65156 23.4972 6.47278L26.2807 6.06831C26.7692 5.99732 27.1916 5.69047 27.4101 5.24775L28.6549 2.72548Z"
          fill={
            fill === 1
              ? `url(#${gradId})`
              : fill === 0
                ? 'none'
                : `url(#${gradId})`
          }
          style={
            fill < 1 && fill > 0
              ? { clipPath: `inset(0 ${100 - fill * 100}% 0 0)` }
              : {}
          }
        />
      </svg>
    </span>
  );
};

const StarRating = ({ score = 4.7, max = 5 }) => {
  const stars = [];
  for (let i = 1; i <= max; i++) {
    let fill = 0;
    if (score >= i) fill = 1;
    else if (score > i - 1) fill = score - (i - 1);
    stars.push(<Star key={i} fill={fill} index={i} />);
  }

  return (
    <div className="star-rating-bubble">
      <div className="star-rating-stars">
        {stars}
        <span className="star-rating-score">{score.toFixed(1)}</span>
      </div>
    </div>
  );
};

export default StarRating;
