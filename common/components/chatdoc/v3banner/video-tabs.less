.video-tabs-box {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 548px;
  padding: 38px 14px 14px;

  .video-tabs {
    display: flex;

    .video-tab-btn {
      padding: 12px 23px 5px;
      color: rgba(255, 255, 255, 40%);
      font-weight: 400;
      font-size: 20px;
      text-align: center;
      background: none;
      border: none;
      border: 1px transparent;
      cursor: pointer;

      &.active {
        position: relative;
        top: 1px;
        color: rgba(255, 255, 255, 80%);
        background: #000;
        border: 1px solid rgba(255, 255, 255, 10%);
        border-bottom: 1px solid #000;
        border-radius: 10px 10px 0 0;
      }

      &:hover {
        color: rgba(255, 255, 255, 80%);
      }
    }
  }

  .video-player-box {
    flex: 1;
    width: 100%;
    background: #000;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 0 16px 16px;

    .banner-video {
      width: 100%;
      height: 100%;
      border-radius: 0 16px 16px;
    }
  }
}
