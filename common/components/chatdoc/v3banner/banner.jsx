import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import TitleTypewriter from './title-typewriter';
import VideoTabs from './video-tabs';
import StarRating from './star-rating';
import Customer from './customer';
import videoBg from '../../../assets/images/chatdoc/video-bg.svg';

import './banner.less';

const Banner = () => {
  const { bannerData, bannerTitle } = useGlobalContext();
  return (
    <div className="v3-banner-wrapper">
      <Container className="banner-container">
        <Row className="banner-row">
          <Col lg={6} md={12} className="banner-left">
            <h1 className="module-title">
              <div className="title-box">
                {bannerData.title}
                <TitleTypewriter bannerTitle={bannerTitle} />
              </div>
              <p>{bannerData.subtitle}</p>
            </h1>
            <p className="banner-desc">{bannerData.desc}</p>
          </Col>
          <Col lg={6} md={12} className="banner-right">
            <div className="video-box">
              <div className="video-bg">
                <img
                  className="video-bg-img"
                  src={videoBg}
                  alt="ChatDOC is an AI tool as ChatGPT for any PDF"></img>
                <div className="video-bg-blue-line"></div>
              </div>
              <VideoTabs />
            </div>
          </Col>
        </Row>
        <StarRating />
        <Customer />
      </Container>
    </div>
  );
};

export default Banner;
