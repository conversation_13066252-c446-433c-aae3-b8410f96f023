import React, { useState } from 'react';
import './video-tabs.less';

const videos = [
  { src: '', title: 'Tab1' },
  { src: '', title: 'Tab2' },
  { src: '', title: 'Tab3' },
];

const VideoTabs = () => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="video-tabs-box">
      <div className="video-tabs">
        {videos.map((video, idx) => (
          <button
            key={video.title}
            className={`video-tab-btn${activeTab === idx ? ' active' : ''}`}
            onClick={() => setActiveTab(idx)}>
            {video.title}
          </button>
        ))}
      </div>
      <div className="video-player-box">
        <video className="banner-video" src={videos[activeTab].src} poster="" />
      </div>
    </div>
  );
};

export default VideoTabs;
