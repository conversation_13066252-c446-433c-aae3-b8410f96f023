import React from 'react';
import B<PERSON><PERSON>rumbNav from '../breadCrumb/bread-crumb-nav';
import Log from '../../log/log';
import ProjectButton from '../button/project-button';
import { PAGE_CRUMB_DATA } from '../../../data/chatdoc/data';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './log-main.less';

const LogMain = () => {
  const { logData } = useGlobalContext();

  return (
    <main>
      <BreadCrumbNav crumbs={[PAGE_CRUMB_DATA.LOG]} />
      <div className="changelog-wrapper">
        <Log
          nodes={logData}
          logButton={
            <ProjectButton buttonText="Get Started" className="link-btn" />
          }
          lazyloadVideo={true}
        />
      </div>
    </main>
  );
};

export default LogMain;
