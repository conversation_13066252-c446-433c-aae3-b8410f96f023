.changelog-wrapper {
  @changelog-bg-color: linear-gradient(
    219.79deg,
    rgba(157, 157, 249, 0.9) 3.61%,
    rgba(29, 69, 194, 0.9) 87.27%
  );

  padding-top: 20px;

  .last-version {
    background-color: unset;
    border: none;

    .link-btn {
      font-family: Poppins-Medium;
      background-image: @changelog-bg-color;
      border: none;
      border-radius: 8px;
    }

    .version-top-line {
      background-image: @changelog-bg-color;
    }
  }

  .timeline-item-wrapper {
    padding: 90px 0 0 60px;
  }

  .timeline-item-node {
    top: 110px;
    left: 3px;
    width: 30px;
    height: 30px;
    background-image: @changelog-bg-color;
  }

  .version-title {
    height: auto;
    margin-bottom: 6px;
    font-family: Poppins-Medium;
    line-height: 66px;
  }

  .version-time {
    height: auto;
    margin-bottom: 28px;
    line-height: 30px;
  }

  .version-subtitle {
    font-size: 20px;

    > ol {
      margin-left: 24px;
    }
  }

  .version-item-image,
  .version-item-video {
    width: 50%;
    margin: 10px 0;
  }
}

@media (min-width: @max-screen-size) {
  .changelog-wrapper {
    .timeline-item {
      max-width: 1460px !important;
    }
  }
}

@media (max-width: @small-screen-size) {
  .changelog-wrapper {
    .version-item-image,
    .version-item-video {
      width: 80%;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .changelog-wrapper {
    .timeline-item-node {
      top: 86px;
      left: 1px;
      width: 16px;
      height: 16px;
    }

    .timeline-item-wrapper {
      padding: 80px 0 0 25px;
    }

    .version-title {
      margin-bottom: 0;
      line-height: 28px;
    }

    .version-time {
      margin-bottom: 0;
      line-height: 20px;
    }

    .version-subtitle {
      font-size: 14px;
    }

    .version-item-image,
    .version-item-video {
      margin: 5px 0;
    }
  }
}
