import React from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import Banner from '../banner/banner';
import Scenarios from '../scenarios/scenarios';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './banner-and-scenarios.less';

const BannerAndScenarios = ({
  currentHideRecommendProjects,
  currentHideSupportFormats,
  currentHideImage,
  currentHideUsers,
  currentBannerData,
}) => {
  const { bannerAndScenariosBg, bannerAndScenariosBgMini } = useGlobalContext();

  return (
    <div className="banner-and-scenarios-wrapper">
      <GatsbyImage
        image={bannerAndScenariosBg.childImageSharp.gatsbyImageData}
        alt="Banner And Scenarios Background"
        className="banner-and-scenarios-bg"
      />
      <GatsbyImage
        image={bannerAndScenariosBgMini.childImageSharp.gatsbyImageData}
        alt="Banner And Scenarios Background Mini"
        className="banner-and-scenarios-mini"
      />
      <Banner
        currentHideRecommendProjects={currentHideRecommendProjects}
        currentHideSupportFormats={currentHideSupportFormats}
        currentHideImage={currentHideImage}
        currentHideUsers={currentHideUsers}
        currentBannerData={currentBannerData}
      />
      <Scenarios />
    </div>
  );
};

BannerAndScenarios.propTypes = {
  currentHideRecommendProjects: PropTypes.bool,
  currentHideSupportFormats: PropTypes.bool,
  currentHideImage: PropTypes.bool,
  currentHideUsers: PropTypes.bool,
  currentBannerData: PropTypes.object,
};

export default BannerAndScenarios;
