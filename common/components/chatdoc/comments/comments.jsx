import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import CommonSwiper from '../../swiper/common-swiper';
import { GLOBAL_USERS_NUMBER } from '../../../data/chatdoc/data';
import { getEnvVariables } from '../../../utils/env';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useCheckScreenSize } from '../../../hooks/useCheckScreenSizeHook';
import _ from 'lodash';
import './comments.less';

const CommentItem = ({ item }) => {
  const { currentProduct } = getEnvVariables();

  return (
    <a
      href={item.link}
      target="_blank"
      rel="noopener noreferrer"
      className="comment-link">
      <GatsbyImage
        image={item.image.childImageSharp.gatsbyImageData}
        alt={item.alt.replace(/ChatDOC/g, currentProduct)}
        className="comment-image"
      />
    </a>
  );
};

const Comments = () => {
  const { currentProduct } = getEnvVariables();
  const { isMiniScreenSize } = useCheckScreenSize();
  const { commentsData, commentsBg } = useGlobalContext();
  const [currentCommentsData, setCurrentCommentsData] = useState(commentsData);

  const getNewCommentsData = useCallback(() => {
    const sortedCommentsData = [...commentsData].sort(
      (a, b) =>
        a.image.childImageSharp.gatsbyImageData.height -
        b.image.childImageSharp.gatsbyImageData.height,
    );
    const minHeightImage = sortedCommentsData[0];
    const secondMinHeightImage = sortedCommentsData[1];
    const minHeightImageIndex = commentsData.findIndex(
      (img) => img === minHeightImage,
    );
    const secondMinHeightImageIndex = commentsData.findIndex(
      (img) => img === secondMinHeightImage,
    );
    const newCommentsData = [
      ...commentsData.slice(0, minHeightImageIndex),
      [minHeightImage, secondMinHeightImage],
      ...commentsData.slice(minHeightImageIndex + 1, secondMinHeightImageIndex),
      ...commentsData.slice(secondMinHeightImageIndex + 1),
    ];
    return newCommentsData;
  }, [commentsData]);

  const getCurrentCommentsData = useCallback(() => {
    if (isMiniScreenSize) {
      setCurrentCommentsData(getNewCommentsData());
    } else {
      setCurrentCommentsData(commentsData);
    }
  }, [isMiniScreenSize, commentsData, getNewCommentsData]);

  useEffect(() => {
    getCurrentCommentsData();
  }, [getCurrentCommentsData]);

  return (
    <div className="comments-wrapper">
      <Container>
        <h3 className="common-title comments-title">
          Loved by {GLOBAL_USERS_NUMBER}+ Global Users
        </h3>
        <p className="common-subtitle comments-subtitle">
          Ready for a productivity boost? Try {currentProduct} today!
        </p>
        <div className="comments-images">
          {commentsData.map((item) => (
            <CommentItem item={item} key={item.image.name} />
          ))}
        </div>
        <CommonSwiper
          className="comments-images-swiper"
          swiperData={currentCommentsData}
          swiperSlideChildren={(item) =>
            _.isArray(item) ? (
              item.map((imageItem) => (
                <CommentItem item={imageItem} key={imageItem.image.name} />
              ))
            ) : (
              <CommentItem item={item} key={item.image.name} />
            )
          }
        />
      </Container>
      <GatsbyImage
        image={commentsBg.childImageSharp.gatsbyImageData}
        alt="Comments Background"
        className="comments-bg"
      />
    </div>
  );
};

CommentItem.propTypes = {
  item: PropTypes.object,
};

export default Comments;
