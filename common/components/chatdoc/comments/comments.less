.comments-wrapper {
  position: relative;
  padding: 120px 0 29px;
  border-bottom: @border-base;

  .comments-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
  }

  .comments-images {
    column-count: 3;
    column-gap: 31px;
  }

  .comments-images-swiper {
    display: none;

    // 因初始化swpier时，图片渲染的宽度不能自适应，需对其特殊处理，等待初始化结束后再显示轮播图。
    &.swiper {
      visibility: hidden;
    }

    &.swiper-initialized {
      visibility: visible;
    }
  }

  .comment-image {
    position: relative;
    margin-bottom: 31px;
  }
}

@media (min-width: @large-screen-size) {
  .comments-wrapper {
    .container {
      max-width: 1230px !important;
    }
  }
}

@media (max-width: @large-screen-size) {
  .comments-wrapper {
    .container {
      width: 80%;
    }

    .comments-images {
      column-gap: 20px;
    }

    .comment-image {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .comments-wrapper {
    padding-top: 90px;

    .comments-images {
      column-count: 2;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .comments-wrapper {
    padding-top: 60px;

    .container {
      width: 100%;
    }

    .comments-images {
      display: none;
    }

    .comments-images-swiper {
      display: block;

      .swiper-slide {
        row-gap: 10px;
        padding: 0 2px;
      }
    }

    .comment-image {
      margin-bottom: 0;
    }
  }
}
