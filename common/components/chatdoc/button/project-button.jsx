import React from 'react';
import PropTypes from 'prop-types';
import <PERSON><PERSON> from 'lottie-react';
import { ACTIVITY_ACTIONS } from '../../../data/chatdoc/data';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import { getUrlWithProduct } from '../../../urls';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import buttonAnimationData from '../../../assets/json/chatdoc/button-animation.json';
import classnames from 'classnames';
import './project-button.less';
import rightArrow from '../../../../common/assets/images/chatdoc/arrow-right-circle.svg';

const DefaultProjectButton = ({ className, text, href, params, isV3 }) => {
  const { defaultSrc } = useGlobalContext();
  const chatdocProjectHref = getUrlWithProduct('chatdoc', 'project');
  const urlSrc = useGetUrlQueryData('src');
  const src = defaultSrc || urlSrc;

  return (
    <a
      href={
        href ||
        getChatDOCLink(chatdocProjectHref, {
          ...params,
          src,
          action: src ? ACTIVITY_ACTIONS.TRY_FOR_FREE : '',
        })
      }
      target="_blank"
      rel="noreferrer"
      className={classnames({
        'default-btn project-button': true,
        [className]: className,
        'v3-project-button': isV3,
      })}>
      <span className="project-button-text">{text}</span>
      {isV3 && <img src={rightArrow} alt="rightArrow" />}
    </a>
  );
};

const ProjectButton = ({
  isShowAnimationButton,
  className,
  text,
  href,
  params,
  isV3,
}) => {
  return isShowAnimationButton ? (
    <div
      className={classnames({
        'project-button-animation': true,
        [className]: className,
      })}>
      <div className="project-button-border" />
      <DefaultProjectButton text={text} href={href} params={params} />
      <div className="project-button-lottie">
        <Lottie
          animationData={buttonAnimationData}
          loop={true}
          autoplay={true}
        />
      </div>
    </div>
  ) : (
    <DefaultProjectButton
      className={className}
      text={text}
      href={href}
      params={params}
      isV3={isV3}
    />
  );
};

DefaultProjectButton.propTypes = {
  text: PropTypes.string,
  href: PropTypes.string,
  params: PropTypes.object,
  className: PropTypes.string,
  isV3: PropTypes.bool,
};

ProjectButton.defaultProps = {
  text: 'Get Started',
};

ProjectButton.propTypes = {
  text: PropTypes.string,
  href: PropTypes.string,
  params: PropTypes.object,
  className: PropTypes.string,
  isShowAnimationButton: PropTypes.bool,
  isV3: PropTypes.bool,
};

export default ProjectButton;
