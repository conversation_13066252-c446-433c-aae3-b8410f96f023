.advantages-wrapper {
  padding: 120px 0;
  background-color: #fff;
  border-bottom: @border-base;

  .advantages-content {
    display: flex;
    justify-content: space-between;
  }

  .advantages-content-swiper {
    display: none;
  }

  .advantage-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 22%;
  }

  .advantage-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 25px;
  }

  .advantage-title {
    margin-bottom: 30px;
    color: @dark-color;
    font-size: 24px;
    font-family: Poppins-Medium;
    line-height: 42px;
  }

  .advantage-info {
    color: @text-color;
    font-size: 16px;
    line-height: 24px;
    white-space: pre;
    text-align: center;
  }
}

@media (max-width: 1400px) {
  .advantages-wrapper {
    .advantage-item {
      width: 25%;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .advantages-wrapper {
    .advantage-item {
      width: 32%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .advantages-wrapper {
    padding: 90px 0;

    .advantages-content {
      flex-direction: column;
      align-items: center;
    }

    .advantage-item {
      width: 70%;
      margin-bottom: 40px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .advantage-icon,
    .advantage-title {
      margin-bottom: 15px;
    }

    .advantage-info {
      white-space: unset;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .advantages-wrapper {
    padding: 60px 0;

    .advantages-content {
      display: none;
    }

    .advantages-content-swiper {
      display: block;
    }

    .advantage-item {
      width: 100%;
    }
  }
}

@media (max-width: @least-screen-size) {
  .advantages-wrapper {
    padding: 30px 0;

    .advantage-item {
      margin-bottom: 0;
    }

    .advantage-icon {
      margin-bottom: 25px;
    }

    .advantage-title {
      margin-bottom: 30px;
    }

    .advantage-info {
      padding: 0 15px;
    }
  }
}
