import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import CommonSwiper from '../../swiper/common-swiper';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './advantages.less';

const AdvantageItem = ({ item }) => {
  return (
    <div className="advantage-item">
      <GatsbyImage
        image={item.icon.childImageSharp.gatsbyImageData}
        alt={`${item.icon.name}`}
        className="advantage-icon"
      />
      <h6 className="advantage-title">{item.title}</h6>
      <p className="advantage-info">{item.info}</p>
    </div>
  );
};

const Advantages = () => {
  const { advantagesData } = useGlobalContext();

  return (
    <div className="advantages-wrapper">
      <Container>
        <div className="advantages-content">
          {advantagesData.map((item, index) => (
            <AdvantageItem key={index} item={item} />
          ))}
        </div>
        <CommonSwiper
          className="advantages-content-swiper"
          swiperData={advantagesData}
          swiperSlideChildren={(item) => <AdvantageItem item={item} />}
        />
      </Container>
    </div>
  );
};

AdvantageItem.propTypes = {
  item: PropTypes.object,
};

export default Advantages;
