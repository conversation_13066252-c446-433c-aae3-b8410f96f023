.users-wrapper {
  z-index: -2;
  display: flex;
  align-items: center;
  width: 100%;

  &::before,
  &::after {
    flex: 1 1;
    margin: auto;
    border: @dashed-border-base;
    content: '';
  }

  &::before {
    margin-right: 10px;
  }

  &::after {
    margin-left: 10px;
  }

  .users-avatars {
    line-height: 22px;
  }

  .users-avatar {
    width: 22px;
    height: 22px;
    box-shadow: 0 10px 35px 0 rgba(53, 16, 150, 10%);
  }

  .users-text {
    margin-left: 8px;
    color: #9ea0a5;
    font-size: 18px;
    line-height: 26px;
  }
}

@media (max-width: @mini-screen-size) {
  .users-wrapper {
    z-index: 1;

    .users-text {
      font-size: 14px;
      line-height: 26px;
    }
  }
}

@media (max-width: @least-screen-size) {
  .users-wrapper {
    position: relative;
    flex-direction: column;

    &::before {
      content: unset;
    }

    &::after {
      position: absolute;
      bottom: 0;
      width: 100%;
      margin: 0;
    }

    .users-avatars {
      margin-bottom: 6px;
    }

    .users-text {
      margin-bottom: 12px;
      margin-left: 0;
    }
  }
}
