.support-formats {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  align-items: center;
  justify-content: center;
  margin: 60px 0 20px;

  .formats-title {
    color: #9ea0a5;
    font-size: 18px;
    line-height: 27px;
    white-space: nowrap;
  }

  .formats-content {
    display: flex;
    flex-flow: row wrap;
    gap: 10px;
    align-items: center;
    justify-content: center;
  }

  .formats-item {
    padding: 1px 18px;
    font-size: 16px;
    line-height: 24px;
    background-image: linear-gradient(
      220deg,
      rgba(157, 157, 249, 90%) 3.61%,
      rgba(29, 69, 194, 90%) 87.27%
    );
    background-clip: text;
    border: 1px solid #9d9df9;
    border-radius: 17px;
    box-shadow: 0 10px 35px 0 rgba(53, 16, 150, 10%);
    -webkit-text-fill-color: transparent;
  }
}

@media (max-width: @large-screen-size) {
  .support-formats {
    margin: 30px 0 20px;
  }
}

@media (max-width: @mini-screen-size) {
  .support-formats {
    margin: 20px 0 16px;

    .formats-title {
      font-size: 13px;
      line-height: 20px;
    }

    .formats-item {
      font-size: 12px;
      line-height: 18px;
    }
  }
}
