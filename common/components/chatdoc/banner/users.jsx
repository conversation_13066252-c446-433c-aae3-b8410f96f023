import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { GLOBAL_USERS_NUMBER } from '../../../data/chatdoc/data';
import './users.less';

const Users = () => {
  const { usersData } = useGlobalContext();

  return (
    <div className="users-wrapper">
      <span className="users-avatars">
        {usersData.map((image, index) => (
          <GatsbyImage
            key={index}
            image={image.childImageSharp.gatsbyImageData}
            alt={image.name}
            className="users-avatar"
          />
        ))}
      </span>
      <p className="users-text">Loved by {GLOBAL_USERS_NUMBER}+ global users</p>
    </div>
  );
};

export default Users;
