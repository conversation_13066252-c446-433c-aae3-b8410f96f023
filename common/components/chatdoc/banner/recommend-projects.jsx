import React from 'react';
import { Carousel } from 'react-bootstrap';
import newTag from '../../../assets/images/chatdoc/new-tag.svg';
import arrowRight from '../../../assets/images/chatdoc/arrow-right-purple.svg';
import { RECOMMEND_PROJECTS_DATA } from '../../../data/chatdoc/data';
import { getUrlWithProduct } from '../../../urls';
import './recomment-projects.less';

const RecommendProjects = () => {
  return (
    <Carousel
      controls={false}
      indicators={false}
      interval={3000}
      className="recommend-projects carousel-vertically">
      {RECOMMEND_PROJECTS_DATA.map((item) => (
        <Carousel.Item key={item.name}>
          <a
            href={getUrlWithProduct(item.url.product, item.url.pathName)}
            target="_blank"
            rel="noreferrer"
            className="recommend-projects-link">
            <div className="link-content">
              <img src={newTag} className="new-tag" alt="New Tag" />
              <div className="link-text">{item.text}</div>
              <img src={arrowRight} className="arrow-right" alt="Arrow" />
            </div>
          </a>
        </Carousel.Item>
      ))}
    </Carousel>
  );
};

export default RecommendProjects;
