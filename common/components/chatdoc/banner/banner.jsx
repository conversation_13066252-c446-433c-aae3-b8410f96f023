import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import ProductHunt from '../productHunt/product-hunt';
import RecommendProjects from './recommend-projects';
import SupportFormats from './support-formats';
import Users from './users';
import ProjectButton from '../button/project-button';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import './banner.less';
import _ from 'lodash';

const Banner = ({
  currentHideRecommendProjects,
  currentHideSupportFormats,
  currentHideImage,
  currentHideUsers,
  currentHideButton,
  currentBannerData,
}) => {
  const {
    bannerData,
    bannerImage,
    bannerButton,
    hideProductHunt,
    hideRecommendProjectsInBanner,
    hideSupportFormatsInBanner,
    hideImageInBanner,
    hideUsersInBanner,
  } = useGlobalContext();
  const isMobile = useMobilePageContext();

  return (
    <div className="banner-wrapper">
      <Container className="banner-container">
        {!hideProductHunt && <ProductHunt className="banner-product-hunt" />}
        {(_.isBoolean(currentHideRecommendProjects)
          ? !currentHideRecommendProjects
          : !hideRecommendProjectsInBanner) &&
          !isMobile && <RecommendProjects />}
        <h1 className="banner-title">
          <p
            dangerouslySetInnerHTML={{
              __html: (currentBannerData || bannerData).title,
            }}></p>
          <br />
          {(currentBannerData || bannerData).subtitle}
        </h1>
        <span className="banner-desc">
          {(currentBannerData || bannerData).desc}
        </span>
        {_.isBoolean(currentHideButton) && currentHideButton
          ? null
          : bannerButton || (
              <div className="banner-button">
                <ProjectButton isShowAnimationButton text="Try for Free" />
              </div>
            )}
        {(_.isBoolean(currentHideSupportFormats)
          ? !currentHideSupportFormats
          : !hideSupportFormatsInBanner) && <SupportFormats />}
        {(_.isBoolean(currentHideImage)
          ? !currentHideImage
          : !hideImageInBanner) && (
          <GatsbyImage
            image={bannerImage.childImageSharp.gatsbyImageData}
            alt="ChatDOC is an AI tool as ChatGPT for any PDF"
            className="banner-image"
          />
        )}
        {(_.isBoolean(currentHideUsers)
          ? !currentHideUsers
          : !hideUsersInBanner) && <Users />}
      </Container>
    </div>
  );
};

Banner.propTypes = {
  currentHideRecommendProjects: PropTypes.bool,
  currentHideSupportFormats: PropTypes.bool,
  currentHideImage: PropTypes.bool,
  currentHideUsers: PropTypes.bool,
  currentHideButton: PropTypes.bool,
  currentBannerData: PropTypes.object,
};

export default Banner;
