.banner-wrapper {
  background-image: linear-gradient(180deg, #f6faff 0%, #eaf3ff 100%);

  .banner-container {
    position: relative;
    top: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 22px;
  }

  .banner-title {
    margin-bottom: 30px;
    color: @dark-title-color;
    font-size: 50px;
    font-family: Poppins-Bold;
    line-height: 60px;
    white-space: pre;
    text-align: center;
  }

  .banner-desc {
    margin-bottom: 20px;
    color: @dark-text-color;
    font-size: 18px;
    line-height: 27px;
    white-space: pre;
    text-align: center;
  }

  .banner-button {
    margin-bottom: 30px;
  }

  .banner-image {
    width: 100%;
    max-width: 1126px;
    overflow: visible;
  }

  .banner-product-hunt {
    display: none;
  }
}

@media (max-width: @large-screen-size) {
  .banner-wrapper {
    .banner-title {
      margin-bottom: 20px;
    }

    .banner-image {
      width: 80%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .banner-wrapper {
    .banner-container {
      top: 10px;
      padding-top: 70px;
    }

    .banner-title {
      font-size: 32px;
      line-height: 46px;
    }

    .banner-desc {
      font-size: 16px;
      line-height: 22px;
    }

    .banner-product-hunt {
      position: absolute;
      top: 20px;
      display: block;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .banner-wrapper {
    .banner-container {
      top: 7px;
      padding-top: 62px;
    }

    .banner-title {
      font-size: 26px;
      line-height: 39px;
      white-space: normal;
    }

    .banner-desc {
      font-size: 13px;
      line-height: 20px;
      white-space: normal;
    }

    .banner-image {
      width: 100%;
    }
  }
}
