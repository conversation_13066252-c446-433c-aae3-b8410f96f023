import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Container, Row, Col } from 'react-bootstrap';
import { SCREEN_SIZE_WIDTH } from '../../../data/constant';
import { useHandleResize } from '../../../hooks/useHandleResizeHook';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { scroller } from 'react-scroll';
import classnames from 'classnames';
import './subscription-help.less';

const RightContentBox = ({ rightContentData }) => {
  return (
    <div id={rightContentData.name} className="content-box">
      <h3 className="content-question">{rightContentData.question}</h3>
      &nbsp;
      <div
        className="content-answer"
        dangerouslySetInnerHTML={{
          __html: rightContentData.answer?.childMarkdownRemark.html,
        }}></div>
    </div>
  );
};

const SubscriptionHelp = ({ currentSubscriptionHelpData }) => {
  const { subscriptionHelpData } = useGlobalContext();

  const subscriptionHelpDataList = useMemo(() => {
    return (currentSubscriptionHelpData || subscriptionHelpData).filter(
      (item) => item.name !== 'question_different_price',
    );
  }, [currentSubscriptionHelpData, subscriptionHelpData]);

  const [questionAnchor, setQuestionAnchor] = useState(null);
  const [rightContent, setRightContent] = useState({
    question: subscriptionHelpDataList[0].question,
    answer: subscriptionHelpDataList[0].answer,
  });
  const [isMiniScreenSize, setIsMiniScreenSize] = useState(false);

  const getQuestionAnchor = (e) => {
    setQuestionAnchor(decodeURI(e.target.location.hash.substr(1)));
  };

  useEffect(() => {
    setQuestionAnchor(decodeURI(window.location.hash.substr(1)));
    window.addEventListener('hashchange', getQuestionAnchor);
    return () => window.removeEventListener('hashchange', getQuestionAnchor);
  }, []);

  useEffect(() => {
    if (questionAnchor !== null) {
      const subscriptionHelpDataItem = subscriptionHelpDataList.find(
        (item) => item.name === questionAnchor,
      );
      if (subscriptionHelpDataItem) {
        setRightContent({
          question: subscriptionHelpDataItem.question,
          answer: subscriptionHelpDataItem.answer,
        });
      } else {
        window.location.hash = `#${subscriptionHelpDataList[0].name}`;
      }
    }
  }, [questionAnchor, subscriptionHelpDataList]);

  const getIsMiniScreenSize = useCallback(() => {
    if (window.innerWidth <= SCREEN_SIZE_WIDTH.MINI_SCREEN_SIZE) {
      setIsMiniScreenSize(true);
      setTimeout(() => {
        scroller.scrollTo(questionAnchor);
      });
    } else {
      setIsMiniScreenSize(false);
    }
  }, [questionAnchor]);

  useHandleResize(() => {
    getIsMiniScreenSize();
  }, [questionAnchor]);

  return (
    <div className="subscription-help">
      <Container>
        <Row>
          <Col xl={3} md={4} className="left-nav">
            <ul className="nav-ul">
              {subscriptionHelpDataList.map((item, index) => (
                <li
                  key={index}
                  className={classnames({
                    'nav-li': true,
                    'nav-li-active': item.name === questionAnchor,
                  })}>
                  <a href={'#' + item.name} className="nav-question">
                    {item.question}
                  </a>
                </li>
              ))}
            </ul>
          </Col>
          {!isMiniScreenSize ? (
            <Col xl={9} md={8} className="right-content">
              <RightContentBox rightContentData={rightContent} />
            </Col>
          ) : (
            <Col className="right-content">
              {subscriptionHelpDataList.map((item, index) => (
                <RightContentBox rightContentData={item} key={index} />
              ))}
            </Col>
          )}
        </Row>
      </Container>
    </div>
  );
};

RightContentBox.propTypes = {
  rightContentData: PropTypes.object,
};

SubscriptionHelp.propTypes = {
  currentSubscriptionHelpData: PropTypes.array,
};

export default SubscriptionHelp;
