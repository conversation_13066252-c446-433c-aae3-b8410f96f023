.subscription-help {
  border-top: @border-base;

  .container {
    height: calc(100vh - 80px);
  }

  .row {
    height: inherit;
    margin: 0;
  }

  .left-nav {
    box-sizing: content-box;
    width: 300px;
    padding: 0;
    font-weight: 500;
    border-right: 1px solid #eaedf3;
  }

  .nav-li {
    margin-top: 12px;
    color: @dark-color;

    &:first-child {
      margin-top: 21px;
    }
  }

  .nav-li-active {
    color: #6576db;
    background-color: #f6f9fd;
  }

  .nav-question {
    display: block;
    padding: 9px 30px;
    color: inherit;
    font-size: 16px;
    line-height: 22px;
  }

  .right-content {
    flex: 1;
    padding: 60px 60px 100px 120px;
    color: @dark-color;
    line-height: 30px;
  }

  .content-question {
    margin-bottom: 10px;
    color: @dark-color;
    font-size: 22px;
    font-family: Poppins-Medium;
  }

  .content-answer {
    color: @text-color;
    font-size: 18px;
    line-height: 32px;

    ul {
      margin-left: 13px;
    }

    li {
      padding-left: 5px;
      list-style-type: disc;
    }

    a {
      color: @text-color;
      text-decoration: underline;
      text-underline-offset: 4px;

      &:hover {
        color: #6576db;
      }
    }

    .gatsby-resp-image-wrapper {
      margin: 8px 100px 12px 40px !important;
    }
  }
}

@media (max-width: @large-screen-size) {
  .subscription-help {
    .right-content {
      padding: 50px 60px 100px 100px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .subscription-help {
    .left-nav {
      width: 30%;
    }

    .right-content {
      padding: 40px 50px 100px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .subscription-help {
    .nav-question {
      padding: 8px 20px;
      font-size: 14px;
      line-height: 20px;
    }

    .right-content {
      line-height: 28px;
    }

    .content-question {
      font-size: 20px;
    }

    .content-answer {
      font-size: 16px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .subscription-help {
    .container {
      height: auto;
    }

    .left-nav {
      width: auto;
      border: none;
    }

    .right-content {
      width: auto;
      padding: 0;
    }

    .content-box {
      &:last-child {
        margin-bottom: 50vh;
      }
    }

    .content-question {
      padding-top: 90px;
    }

    .content-answer {
      .gatsby-resp-image-wrapper {
        margin: 8px 10px 12px !important;
      }
    }
  }
}
