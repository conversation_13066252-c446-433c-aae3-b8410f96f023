.blog-detail-wrapper {
  .blog-detail-container {
    display: flex;
  }

  .blog-detail-left,
  .blog-detail-right {
    margin-top: 40px;
  }

  .blog-detail-left {
    position: sticky;
    top: 120px;
    z-index: 10;
    width: 300px;
    height: 100%;
    margin-right: 60px;
  }

  .blog-detail-right {
    flex: 1 1;
    margin-bottom: 180px;
    overflow: auto;
  }

  .blog-detail-title {
    margin-bottom: 24px;
    color: #000;
    font-weight: 600;
    font-size: 42px;
    line-height: 45px;
  }

  .blog-updated-time {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
  }

  .blog-updated-time-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  .blog-updated-time-text {
    color: #303133;
    font-weight: 500;
    font-size: 20px;
    line-height: 20px;
  }

  .blog-detail-content {
    img,
    video {
      width: 100%;
      margin-bottom: 32px;
    }

    blockquote {
      padding: 0 16px;
      border-left: 8px solid #5a65ea;

      p {
        margin-bottom: 0;
        color: #303133;
        font-weight: 600;
        font-size: 18px;
        line-height: 32px;
      }
    }

    table {
      display: block;
      margin-bottom: 10px;
      overflow-x: auto;

      thead {
        background-color: rgb(234, 234, 239);
      }

      tr {
        border: 1px solid rgb(220, 220, 228);
      }

      th {
        padding: 16px;
      }

      td {
        padding: 12px;
      }

      th,
      td {
        border-right: 1px solid rgb(220, 220, 228);
        border-left: 1px solid rgb(220, 220, 228);
      }
    }

    h1 {
      margin-bottom: 54px;
      color: #000;
      font-weight: 600;
      font-size: 42px;
      line-height: 45px;
    }

    h2 {
      color: #5a65ea;
      font-weight: 600;
      font-size: 32px;
      line-height: 45px;
    }

    h3 {
      color: #303133;
      font-weight: 600;
      font-size: 28px;
      line-height: 42px;
    }

    h4 {
      color: #303133;
      font-weight: 600;
      font-size: 24px;
      line-height: 24px;
    }

    h5 {
      color: #303133;
      font-weight: 600;
      font-size: 20px;
      line-height: 14px;
    }

    h6 {
      color: #303133;
      font-weight: 600;
      font-size: 18px;
      line-height: 14px;
    }

    p {
      color: #606266;
      font-weight: 400;
      font-size: 18px;
      line-height: 32px;

      > img,
      > video {
        margin-top: 32px;
      }
    }

    ul,
    ol {
      padding-inline-start: 40px;

      li {
        overflow-wrap: break-word;
      }
    }

    ul {
      list-style: initial;
    }

    ol {
      list-style: decimal;
    }

    blockquote,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    ul,
    ol,
    pre {
      margin-bottom: 16px;
    }

    code {
      display: block;
      padding: 24px;
      color: #545659;
      font-size: 18px;
      background: #efefef;
      border: 1px solid #dfdfe2;
      border-radius: 8px;
    }

    // :is(p, h1, h2, h3, h4, h5, h6, ul, ol)
    //   + :is(h1, h2, h3, h4, h5, h6, img, video) {
    //   margin-bottom: 32px;
    // }
    p + h1,
    p + h2,
    p + h3,
    p + h4,
    p + h5,
    p + h6,
    p + img,
    p + video,
    h1 + h1,
    h1 + h2,
    h1 + h3,
    h1 + h4,
    h1 + h5,
    h1 + h6,
    h1 + img,
    h1 + video,
    h2 + h1,
    h2 + h2,
    h2 + h3,
    h2 + h4,
    h2 + h5,
    h2 + h6,
    h2 + img,
    h2 + video,
    h3 + h1,
    h3 + h2,
    h3 + h3,
    h3 + h4,
    h3 + h5,
    h3 + h6,
    h3 + img,
    h3 + video,
    h4 + h1,
    h4 + h2,
    h4 + h3,
    h4 + h4,
    h4 + h5,
    h4 + h6,
    h4 + img,
    h4 + video,
    h5 + h1,
    h5 + h2,
    h5 + h3,
    h5 + h4,
    h5 + h5,
    h5 + h6,
    h5 + img,
    h5 + video,
    h6 + h1,
    h6 + h2,
    h6 + h3,
    h6 + h4,
    h6 + h5,
    h6 + h6,
    h6 + img,
    h6 + video,
    ul + h1,
    ul + h2,
    ul + h3,
    ul + h4,
    ul + h5,
    ul + h6,
    ul + img,
    ul + video,
    ol + h1,
    ol + h2,
    ol + h3,
    ol + h4,
    ol + h5,
    ol + h6,
    ol + img,
    ol + video {
      margin-top: 32px;
    }
  }

  .blog-try-card {
    position: relative;
    margin: 75px 0;
    padding: 60px 60px 42px;
    background-image: linear-gradient(
      91deg,
      #ff8371 -0.39%,
      #e37cf3 61.5%,
      #7291ff 100%
    );
    background-size: cover;
    border-radius: 20px;
  }

  .try-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
  }

  .try-card-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .try-card-title {
    margin-bottom: 22px;
    padding: 0 5px;
    color: #fff;
    font-weight: 600;
    font-size: 42px;
    line-height: 60px;
    letter-spacing: -1px;
    background-image: linear-gradient(
      to right,
      rgba(241, 62, 49, 70%),
      rgba(137, 76, 227, 70%)
    );
    background-clip: text;
    -webkit-text-stroke: 8px transparent;
  }

  .try-card-desc {
    margin-bottom: 32px;
    color: #fff;
    font-weight: 400;
    font-size: 26px;
    line-height: 36px;
  }

  .try-card-btn {
    padding: 15px 50px;
    color: #894ce3;
    font-weight: 600;
    font-size: 36px;
    line-height: 42px;
    background-color: #fff;
    border: none;
    border-radius: 17px;
  }

  .recommend-title {
    margin-bottom: 42px;
    font-weight: 600;
    font-size: 32px;
    line-height: 42px;
    text-align: center;
  }

  .recommend-list {
    display: flex;
    justify-content: space-between;
    column-gap: 42px;
  }

  .recommend-list-left {
    justify-content: flex-start;
  }
}

@media (max-width: @middle-screen-size) {
  .blog-detail-wrapper {
    .blog-detail-left {
      width: 260px;
      margin-right: 40px;
    }

    .blog-detail-right {
      margin-bottom: 100px;
    }

    .blog-try-card {
      margin: 50px 0;
      padding: 22px;
    }

    .try-card-title {
      font-size: 32px;
      line-height: 40px;
    }

    .try-card-desc {
      margin-bottom: 20px;
      font-size: 22px;
      line-height: 30px;
    }

    .try-card-btn {
      padding: 12px 30px;
      font-size: 26px;
      line-height: 30px;
      border-radius: 10px;
    }

    .recommend-title {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .blog-detail-wrapper {
    .recommend-list {
      flex-direction: column;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .blog-detail-wrapper {
    .blog-detail-container {
      flex-direction: column;
    }

    .blog-detail-left {
      top: calc(var(--header-height) + 44px);
      width: 100%;
      margin: 0;
      background-color: #fbfbfb;
    }

    .blog-detail-right {
      margin-bottom: 20px;
    }

    .blog-detail-title {
      margin-bottom: 12px;
      font-size: 24px;
      line-height: 28px;
    }

    .blog-updated-time {
      margin-bottom: 12px;
    }

    .blog-updated-time-icon {
      width: 15px;
      height: 15px;
    }

    .blog-updated-time-text {
      font-size: 14px;
    }

    .blog-detail-content {
      img,
      video {
        width: 100%;
        margin-bottom: 16px;
        pointer-events: none;
      }

      h1 {
        margin-bottom: 24px;
        color: #303133;
        font-size: 24px;
        line-height: 28px;
      }

      h2 {
        font-size: 20px;
        line-height: 24px;
      }

      h3 {
        font-size: 18px;
        line-height: 24px;
      }

      h4 {
        font-size: 17px;
        line-height: 20px;
      }

      h5 {
        font-size: 16px;
        line-height: 20px;
      }

      h6 {
        font-size: 15px;
        line-height: 20px;
      }

      p {
        font-size: 14px;
        line-height: 20px;

        > img,
        > video {
          margin-top: 16px;
        }
      }

      ul,
      ol {
        padding-inline-start: 24px;
      }

      blockquote,
      h2,
      h3,
      h4,
      h5,
      h6,
      p,
      ul,
      ol {
        margin-bottom: 8px;
      }

      p + h1,
      p + h2,
      p + h3,
      p + h4,
      p + h5,
      p + h6,
      p + img,
      p + video,
      h1 + h1,
      h1 + h2,
      h1 + h3,
      h1 + h4,
      h1 + h5,
      h1 + h6,
      h1 + img,
      h1 + video,
      h2 + h1,
      h2 + h2,
      h2 + h3,
      h2 + h4,
      h2 + h5,
      h2 + h6,
      h2 + img,
      h2 + video,
      h3 + h1,
      h3 + h2,
      h3 + h3,
      h3 + h4,
      h3 + h5,
      h3 + h6,
      h3 + img,
      h3 + video,
      h4 + h1,
      h4 + h2,
      h4 + h3,
      h4 + h4,
      h4 + h5,
      h4 + h6,
      h4 + img,
      h4 + video,
      h5 + h1,
      h5 + h2,
      h5 + h3,
      h5 + h4,
      h5 + h5,
      h5 + h6,
      h5 + img,
      h5 + video,
      h6 + h1,
      h6 + h2,
      h6 + h3,
      h6 + h4,
      h6 + h5,
      h6 + h6,
      h6 + img,
      h6 + video,
      ul + h1,
      ul + h2,
      ul + h3,
      ul + h4,
      ul + h5,
      ul + h6,
      ul + img,
      ul + video,
      ol + h1,
      ol + h2,
      ol + h3,
      ol + h4,
      ol + h5,
      ol + h6,
      ol + img,
      ol + video {
        margin-top: 16px;
      }
    }

    .blog-try-card {
      margin: 32px 0 24px;
      padding: 16px 16px 12px;
      border-radius: 8px;
    }

    .try-card-bg {
      border-radius: 8px;
    }

    .try-card-title {
      margin: 0 20px 8px;
      font-weight: 600;
      font-size: 18px;
      font-family: Poppins-SemiBold;
      font-style: normal;
      line-height: 24px;
      text-align: center;
      -webkit-text-stroke: 2.5px transparent;
    }

    .try-card-desc {
      margin-bottom: 12px;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }

    .try-card-btn {
      padding: 5px 10px;
      color: #894ce3;
      font-weight: 600;
      font-size: 14px;
      line-height: 14px;
      text-align: center;
      border-radius: 4px;
    }

    .recommend-title {
      margin-bottom: 16px;
      color: #303133;
      font-size: 20px;
      line-height: 24px;
    }
  }
}
