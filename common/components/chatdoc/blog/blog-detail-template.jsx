import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import { Container } from 'react-bootstrap';
import BreadCrumbNav from '../breadCrumb/bread-crumb-nav';
import BlogCard from './blog-card';
import ProjectButton from '../button/project-button';
import BlogDirectory from './blog-directory';
import BlogRegGuideModal from './blog-reg-guide-modal';
import {
  PAGE_CRUMB_DATA,
  CHATDOC_BLOG_SOURCE,
} from '../../../data/chatdoc/data';
import { getEnvVariables } from '../../../utils/env';
import { getCookie, setCookie } from '../../../utils/cookie';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import TimeSvg from '../../../assets/icons/chatdoc/time.svg';
import dayjs from 'dayjs';
import classnames from 'classnames';
import './blog-detail-template.less';
import _ from 'lodash';

const LAST_OPEN_REG_GUIDE_MODAL_DATE = 'last_open_reg_guide_modal_date';
const BLOG_DETAIL_STAY_TIMEOUT = 3e4;

const BlogDetailTemplate = ({
  isIframe,
  blogArticle,
  blogTreeData,
  blogRecommendArticles,
}) => {
  const {
    hideBlogTryCard,
    blogCardBg,
    blogDetailTryCardData,
    hideBlogRegGuideModal,
  } = useGlobalContext();
  const version = useGetUrlQueryData('version');
  const category = useGetUrlQueryData('category');
  const isMobile = useMobilePageContext();
  const { currentProduct } = getEnvVariables();
  const blogDetailStayTimer = useRef();
  const [showBlogRegGuideModal, setShowBlogRegGuideModal] = useState(false);
  const BLOG_DETAIL_CRUMBS = [
    PAGE_CRUMB_DATA.BLOG,
    { label: blogArticle.title },
  ];

  const getBlogItemHref = (blogItem) => {
    if (isIframe) {
      let params = {
        id: blogItem.id,
        source: CHATDOC_BLOG_SOURCE[currentProduct.toUpperCase()],
      };
      if (version) {
        params.version = version;
        if (category) {
          params.category = category;
        }
      }
      return `/blogDetailPreview${isMobile ? '/m/' : '/'}?${new URLSearchParams(
        params,
      ).toString()}`;
    }
    return `/blog/${blogItem.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')}/`;
  };

  const handleCloseBlogRegGuideModal = () => {
    setShowBlogRegGuideModal(false);
  };

  const handleOpenBlogRegGuideModal = () => {
    setShowBlogRegGuideModal(true);
  };

  const checkIsOpenBlogRegGuideModal = useCallback(() => {
    const lastOpenRegGuideModalDate = getCookie(LAST_OPEN_REG_GUIDE_MODAL_DATE);
    const currentDate = new Date().toLocaleDateString();
    if (lastOpenRegGuideModalDate !== currentDate && !isIframe) {
      handleOpenBlogRegGuideModal();
      setCookie(LAST_OPEN_REG_GUIDE_MODAL_DATE, currentDate, 24);
    }
  }, [isIframe]);

  const openBlogRegGuideModalWhenStay = useCallback(() => {
    clearTimeout(blogDetailStayTimer.current);
    blogDetailStayTimer.current = setTimeout(() => {
      checkIsOpenBlogRegGuideModal();
    }, BLOG_DETAIL_STAY_TIMEOUT);
  }, [checkIsOpenBlogRegGuideModal]);

  const openBlogRegGuideModalAtHalfPosition = useCallback(() => {
    const scrollPositionPercentage =
      (document.documentElement.scrollTop + window.innerHeight) /
      document.documentElement.scrollHeight;
    if (scrollPositionPercentage * 100 >= 50) {
      checkIsOpenBlogRegGuideModal();
    }
  }, [checkIsOpenBlogRegGuideModal]);

  const handleScroll = useCallback(() => {
    if (!hideBlogRegGuideModal) {
      openBlogRegGuideModalAtHalfPosition();
      openBlogRegGuideModalWhenStay();
    }
  }, [
    hideBlogRegGuideModal,
    openBlogRegGuideModalAtHalfPosition,
    openBlogRegGuideModalWhenStay,
  ]);

  useEffect(() => {
    handleScroll();
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    return () => clearTimeout(blogDetailStayTimer.current);
  }, []);

  return (
    <>
      <BreadCrumbNav crumbs={BLOG_DETAIL_CRUMBS} />
      <div className="blog-detail-wrapper">
        <Container className="blog-detail-container">
          {!_.isEmpty(blogTreeData) && (
            <div className="blog-detail-left">
              <BlogDirectory
                treeData={blogTreeData}
                title={blogArticle.title}
              />
            </div>
          )}
          <div className="blog-detail-right">
            <div>
              <h1 className="blog-detail-title">{blogArticle.title}</h1>
              <div className="blog-updated-time">
                <TimeSvg className="blog-updated-time-icon" />
                <span className="blog-updated-time-text">
                  {dayjs.unix(blogArticle.updated_at).format('MMM DD, YYYY')}
                </span>
              </div>
              <div
                className="blog-detail-content"
                dangerouslySetInnerHTML={{ __html: blogArticle.contentHtml }}
              />
            </div>
            {!hideBlogTryCard && (
              <div className="blog-try-card">
                <GatsbyImage
                  image={blogCardBg.childImageSharp.gatsbyImageData}
                  alt="Blog Card Background"
                  className="try-card-bg"
                />
                <div className="try-card-content">
                  <h2
                    className="try-card-title"
                    data-text={blogDetailTryCardData.title}>
                    {blogDetailTryCardData.title}
                  </h2>
                  <p className="try-card-desc">{blogDetailTryCardData.desc}</p>
                  <ProjectButton text="Try for Free" className="try-card-btn" />
                </div>
              </div>
            )}
            {blogRecommendArticles.length > 0 && (
              <div className="blog-detail-recommend">
                <p className="recommend-title">Related Articles</p>
                <div
                  className={classnames({
                    'recommend-list': true,
                    'recommend-list-left': blogRecommendArticles.length < 3,
                  })}>
                  {blogRecommendArticles.map((article) => (
                    <BlogCard
                      key={article.id}
                      isIframe={isIframe}
                      blogCardData={article}
                      href={getBlogItemHref(article)}
                      isRecommendBlogCard
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </Container>
      </div>
      {!hideBlogRegGuideModal && (
        <BlogRegGuideModal
          show={showBlogRegGuideModal}
          handleClose={handleCloseBlogRegGuideModal}
        />
      )}
    </>
  );
};

BlogDetailTemplate.defaultProps = {
  isIframe: false,
};

BlogDetailTemplate.propTypes = {
  isIframe: PropTypes.bool,
  blogArticle: PropTypes.object,
  blogTreeData: PropTypes.array,
  blogRecommendArticles: PropTypes.array,
};

export default BlogDetailTemplate;
