html {
  scroll-padding-top: 80px;
}

@media (max-width: @mini-screen-size) {
  html {
    scroll-padding-top: 187px;
  }
}

.blog-detail-main {
  position: relative;
  min-height: calc(100vh - var(--header-height));
  background-color: #fbfbfb;
}

@media (max-width: @mini-screen-size) {
  .blog-detail-main {
    .bread-crumb-nav {
      position: sticky;
      top: var(--header-height);
      background-color: #fbfbfb;
    }
  }
}
