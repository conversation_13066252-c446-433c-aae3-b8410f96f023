.blog-list-wrapper {
  position: relative;
  z-index: 1;
  padding: 40px 0;

  .blog-list-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .blog-list-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 710px;
    height: 54px;
    background-color: rgba(255, 255, 255, 46%);
    border: 1px solid #fff;
    border-radius: 27px;
  }

  .search-input {
    flex: 1;
    height: 100%;
    padding: 4px 20px;
    color: #7c7c7c;
    font-size: 20px;
    line-height: 24px;
    background-color: transparent;
    border: none;

    ::placeholder {
      color: #b2b4be;
    }

    &:focus {
      box-shadow: unset;
    }

    &:focus-visible {
      outline: none;
    }
  }

  .search-button {
    width: 116px;
    height: 54px;
    color: #fff;
    font-size: 20px;
    line-height: 24px;
    background-color: #5a65ea;
    border: none;
    border-radius: 27px;
  }

  .blog-list-tabs {
    width: 100%;

    .ant-tabs-nav {
      margin-bottom: 0;

      &::before {
        border: none;
      }

      .ant-tabs-nav-list {
        display: flex;
        width: fit-content;
        margin: 50px auto 69px;

        &::after,
        .ant-tabs-ink-bar {
          bottom: -6px;
          height: 6px;
          border-radius: 34px;
        }

        &::after {
          position: absolute;
          width: 100%;
          background-color: #fff;
          content: '';
        }

        .ant-tabs-ink-bar {
          z-index: 1;
          background-color: #5a65ea;
          box-shadow: 0 0 30px #5a65eacc;
        }

        .ant-tabs-tab {
          padding: 18px;

          + .ant-tabs-tab {
            margin: 0 0 0 32px;
          }
        }

        .ant-tabs-tab-btn {
          color: #303133;
          font-weight: 400;
          font-size: 20px;
          line-height: 18px;
        }

        .ant-tabs-tab-active {
          padding: 16px;

          .ant-tabs-tab-btn {
            color: #5a65ea;
            font-weight: 600;
          }
        }
      }
    }
  }

  .blog-list-pagination {
    padding: 40px 0 60px;

    .active {
      color: #5a65ea;
    }

    .page,
    .prev,
    .next {
      font-size: 18px;

      &:hover {
        color: #5a65ea;
      }
    }
  }
}

@media (max-width: @mini-screen-size) {
  .blog-list-wrapper {
    padding: 20px 0;

    .blog-list-search {
      height: 36px;
    }

    .search-input {
      width: 0;
      font-size: 14px;
      line-height: 24px;
    }

    .search-button {
      width: 82px;
      height: 36px;
      font-size: 14px;
      line-height: 24px;
    }

    .blog-list-tabs {
      .ant-tabs-nav {
        width: 100%;
        margin: 0 auto;

        .ant-tabs-nav-list {
          margin: 32px 0 36px;

          .ant-tabs-tab-btn {
            font-size: 14px;
          }

          .ant-tabs-tab-active,
          .ant-tabs-tab {
            padding: 8px 6px;
          }

          .ant-tabs-tab + .ant-tabs-tab {
            margin: 0 0 0 20px;
          }
        }

        .ant-tabs-nav-wrap {
          display: flex;
          justify-content: center;
        }
      }
    }

    .blog-list-pagination {
      padding: 15px 0 0;

      .page,
      .prev,
      .next {
        font-size: 14px;
      }
    }
  }
}
