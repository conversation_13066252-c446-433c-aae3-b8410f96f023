import React, {
  useRef,
  useEffect,
  useCallback,
  useState,
  useMemo,
} from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import SwipeableViews from 'react-swipeable-views';
import { Container } from 'react-bootstrap';
import { Tabs } from 'antd';
import BreadCrumbNav from '../breadCrumb/bread-crumb-nav';
import BlogCard from './blog-card';
import BlogEmpty from './blog-empty';
import CustomPagination from '../../pagination/custom-pagination';
import {
  PAGE_CRUMB_DATA,
  CHATDOC_BLOG_SOURCE,
} from '../../../data/chatdoc/data';
import { getBlogList } from '../../../utils/chatdoc/blog';
import { getEnvVariables } from '../../../utils/env';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useHandleResize } from '../../../hooks/useHandleResizeHook';
import './blog-list.less';

const ALL_BLOG_CATEGORY = 'ALL';
const BLOG_LIST_SIZE = 10;

const BlogList = ({ isIframe, blogArticles, blogCategory, blogList }) => {
  const isMobile = useMobilePageContext();
  const version = useGetUrlQueryData('version');
  const { blogBg, blogBgMini } = useGlobalContext();
  const { currentProduct } = getEnvVariables();

  const swipeableViewsRef = useRef();
  const [searchTitleValue, setSearchTitleValue] = useState('');
  const [currentBlogList, setCurrentBlogList] = useState(blogList);
  const [currentCategory, setCurrentCategory] = useState(ALL_BLOG_CATEGORY);
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [currentPageNums, setCurrentPageNums] = useState(1);
  const [isAnimateHeight, setIsAnimateHeight] = useState(false);

  const totalPageNums = useMemo(() => {
    return Math.ceil(
      currentBlogList[currentCategoryIndex].length / BLOG_LIST_SIZE,
    );
  }, [currentBlogList, currentCategoryIndex]);

  const blogCategoryArr = useMemo(() => {
    return [{ category: ALL_BLOG_CATEGORY }, ...blogCategory];
  }, [blogCategory]);

  const currentBlogListData = useMemo(() => {
    return currentBlogList.map((array) =>
      array.slice(
        (currentPageNums - 1) * BLOG_LIST_SIZE,
        currentPageNums * BLOG_LIST_SIZE,
      ),
    );
  }, [currentBlogList, currentPageNums]);

  const updateBlogList = useCallback(() => {
    const searchBlogList = blogArticles.items.filter((item) => {
      if (searchTitleValue) {
        return item.title
          .toLowerCase()
          .includes(searchTitleValue.toLowerCase());
      }
      return true;
    });
    const currentSearchBlogList = getBlogList(searchBlogList, blogCategory);
    setCurrentBlogList(currentSearchBlogList);
  }, [blogCategory, blogArticles, searchTitleValue]);

  const scrollToTop = () => {
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);
  };

  const updateSwipeableViewsHeight = () => {
    setTimeout(() => {
      swipeableViewsRef.current.updateHeight();
    });
  };

  const onChangePage = useCallback((page) => {
    setCurrentPageNums(page);
    updateSwipeableViewsHeight();
    scrollToTop();
  }, []);

  const changeBlogAndResetPager = useCallback(() => {
    setCurrentPageNums(1);
    updateBlogList();
    updateSwipeableViewsHeight();
  }, [updateBlogList]);

  const getBlogItemHref = (blogItem) => {
    if (isIframe) {
      let params = {
        id: blogItem.id,
        source: CHATDOC_BLOG_SOURCE[currentProduct.toUpperCase()],
      };
      if (version) {
        params.version = version;
        if (currentCategory !== ALL_BLOG_CATEGORY) {
          params.category = currentCategory;
        }
      }
      return `/blogDetailPreview${isMobile ? '/m/' : '/'}?${new URLSearchParams(
        params,
      ).toString()}`;
    }
    return `/blog/${blogItem.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')}/`;
  };

  const handleInpytKeyPress = (e) => {
    if (e.key === 'Enter') {
      changeBlogAndResetPager();
    }
  };

  const handleTabChange = (index) => {
    setCurrentCategoryIndex(index);
    setCurrentPageNums(1);
    updateSwipeableViewsHeight();
  };

  const handleSwipeChangeIndex = (index) => {
    handleTabChange(index);
    scrollToTop();
  };

  useHandleResize(() => {
    updateSwipeableViewsHeight();
  });

  useEffect(() => {
    setIsAnimateHeight(true);
  }, []);

  useEffect(() => {
    if (blogCategoryArr.length > 0) {
      setCurrentCategory(blogCategoryArr[currentCategoryIndex].category);
    }
  }, [blogCategoryArr, currentCategoryIndex]);

  return (
    <>
      <GatsbyImage
        image={blogBg.childImageSharp.gatsbyImageData}
        alt="Blog Background"
        className="blog-main-bg"
      />
      <GatsbyImage
        image={blogBgMini.childImageSharp.gatsbyImageData}
        alt="Blog Background Mini"
        className="blog-main-bg-mini"
      />
      <BreadCrumbNav crumbs={[PAGE_CRUMB_DATA.BLOG]} />
      <div className="blog-list-wrapper">
        <Container className="blog-list-container">
          <div className="blog-list-content">
            <span className="blog-list-search">
              <input
                value={searchTitleValue}
                className="search-input"
                placeholder="Search by title"
                type="text"
                onChange={(e) => setSearchTitleValue(e.target.value)}
                onKeyPress={handleInpytKeyPress}
              />
              <button
                type="button"
                className="search-button"
                onClick={changeBlogAndResetPager}>
                <span>Search</span>
              </button>
            </span>
            <Tabs
              className="blog-list-tabs"
              activeKey={currentCategoryIndex}
              onChange={handleTabChange}
              onTabClick={handleTabChange}
              items={blogCategoryArr.map((item, index) => {
                return {
                  label: item.category,
                  key: index,
                  children: <></>,
                };
              })}
            />
            <SwipeableViews
              className="blog-swipeable-views"
              ref={swipeableViewsRef}
              animateHeight={isAnimateHeight}
              style={{ width: '100%' }}
              index={currentCategoryIndex}
              onChangeIndex={handleSwipeChangeIndex}>
              {currentBlogListData.map((item, index) => (
                <div key={index} className="blog-cards">
                  {item.length > 0 ? (
                    item.map((item) => (
                      <BlogCard
                        isIframe={isIframe}
                        blogCardData={item}
                        href={getBlogItemHref(item)}
                        key={item.strapi_id}
                      />
                    ))
                  ) : (
                    <BlogEmpty />
                  )}
                </div>
              ))}
            </SwipeableViews>

            {totalPageNums > 1 && (
              <CustomPagination
                className="blog-list-pagination"
                current={currentPageNums}
                total={totalPageNums}
                onChange={onChangePage}
              />
            )}
          </div>
        </Container>
      </div>
    </>
  );
};

BlogList.defaultProps = {
  isIframe: false,
};

BlogList.propTypes = {
  isIframe: PropTypes.bool,
  blogArticles: PropTypes.object,
  blogCategory: PropTypes.array,
  blogList: PropTypes.array,
};

export default BlogList;
