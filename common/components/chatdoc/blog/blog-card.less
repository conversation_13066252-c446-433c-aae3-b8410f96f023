.blog-card-link {
  .blog-card {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 24px;
    background-color: #fff;
    border: 1px solid #c2c2c2;
    border-radius: 6px;

    &:hover {
      background-color: #5a65ea0d;
      border-color: #5a65ea;
      cursor: pointer;

      .card-title {
        color: #5a65ea;
      }
    }
  }

  .blog-card-mobile {
    &:hover {
      background-color: #fff;
      border-color: #c2c2c2;
    }
  }

  .blog-card-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px 30px 24px;
  }

  .card-content {
    display: flex;
    flex-direction: column;
  }

  .card-title,
  .card-abstract p {
    display: -webkit-box;
    flex: 1;
    width: 100%;
    overflow: hidden;
    -webkit-box-orient: vertical;
  }

  .card-title {
    margin-bottom: 15px;
    color: #303133;
    font-weight: 600;
    font-size: 24px;
    line-height: 31px;
    -webkit-line-clamp: 2;
  }

  .card-abstract p {
    color: #606266;
    font-weight: 400;
    -webkit-line-clamp: 3;
    font-size: 18px;
    line-height: 24px;
  }

  .card-image {
    flex-shrink: 0;
    width: 426px;
    height: 240px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .card-image-small {
    display: none;
  }

  .card-category,
  .card-updated-time {
    display: inline-block;
    width: fit-content;
    padding: 6px 9px;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    border-radius: 4px;
  }

  .card-category {
    margin-right: 12px;
    color: #5a65ea;
    background-color: #5a65ea1a;
  }

  .card-updated-time {
    color: #606266;
    background-color: #7f7f7f1a;
  }
}

.blog-card-link-recommend {
  .blog-card {
    flex-direction: column-reverse;
    justify-content: flex-end;
    max-width: 400px;
    height: 100%;
    margin-bottom: 0;
  }

  .blog-card-left {
    padding: 24px 16px 64px;
  }

  .card-image {
    width: 100%;
    height: 100%;
    border-radius: 6px 6px 0 0;
  }

  .card-category,
  .card-updated-time {
    display: none;
  }
}

@media (max-width: @middle-screen-size) {
  .blog-card-link {
    .blog-card-left {
      padding: 25px 25px 20px;
    }

    .card-title {
      margin-bottom: 10px;
      font-size: 18px;
      line-height: 22px;
    }

    .card-abstract p {
      font-size: 14px;
      line-height: 21px;
    }

    .card-image {
      width: 340px;
      height: 192px;
    }

    .card-category,
    .card-updated-time {
      padding: 2px 8px;
    }
  }

  .blog-card-link-recommend {
    .blog-card-left {
      padding: 20px 16px 30px;
    }

    .card-image {
      width: 100%;
      height: 100%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .blog-card-link-recommend {
    .blog-card {
      width: 100%;
      max-width: unset;
      margin-bottom: 12px;
    }

    .blog-card-left {
      padding: 20px 16px;
    }

    .card-content-abstract {
      display: flex;
      justify-content: space-between;
    }

    .card-image {
      width: 112px;
      height: 64px;
      margin-left: 4px;
      border-radius: 4px;
    }

    .card-image-small {
      display: block;
    }

    .card-image-large {
      display: none;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .blog-card-link {
    .blog-card {
      margin-bottom: 12px;
    }

    .blog-card-left {
      padding: 12px;
    }

    .blog-card-left {
      width: 100%;
    }

    .card-title {
      margin-bottom: 8px;
      font-size: 16px;
      line-height: 20px;
    }

    .card-abstract p {
      font-size: 13px;
      line-height: 20px;
    }

    .card-content-abstract {
      display: flex;
      justify-content: space-between;
    }

    .card-image {
      width: 112px;
      height: 64px;
      margin-left: 4px;
      border-radius: 4px;
    }

    .card-image-small {
      display: block;
    }

    .card-image-large {
      display: none;
    }

    .card-category,
    .card-updated-time {
      margin-top: 3px;
    }

    .card-category {
      margin-right: 8px;
      font-size: 13px;
    }

    .card-updated-time {
      font-size: 12px;
    }
  }
}
