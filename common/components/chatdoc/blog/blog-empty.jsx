import React from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './blog-empty.less';

const BlogEmpty = ({ loading }) => {
  const { blogEmptyImage } = useGlobalContext();

  return (
    <>
      {loading ? (
        <div className="blog-empty-loading">
          <div className="loading-icons">
            <i className="loading-spin"></i>
            <i className="loading-chatdoc"></i>
          </div>
          <span>Loading,please wait...</span>
        </div>
      ) : (
        <div className="blog-empty">
          <GatsbyImage
            placeholder="blurred"
            className="empty-image"
            alt="Empty Image"
            image={blogEmptyImage.childImageSharp.gatsbyImageData}
          />
          <p className="empty-info">No data available.</p>
        </div>
      )}
    </>
  );
};

BlogEmpty.propTypes = {
  loading: PropTypes.bool,
};

export default BlogEmpty;
