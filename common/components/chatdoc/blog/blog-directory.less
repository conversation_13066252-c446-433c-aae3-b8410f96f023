.blog-directory {
  .directory-name {
    display: flex;
    align-items: center;
    width: fit-content;
    margin-bottom: 25px;
  }

  .directory-icon {
    width: 20px;
    height: 20px;
  }

  .directory-name-text {
    margin-left: 10px;
    color: #303133;
    font-size: 18px;
    font-family: Poppins-Medium;
    line-height: 22px;
    white-space: nowrap;
  }

  .directory-tree {
    display: block;
    font-size: 16px;
    background-color: #fff;
  }

  .directory-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 16px;
    font-size: 16px;
    font-family: Poppins-Medium;
    line-height: 24px;
    letter-spacing: -0.5px;
    column-gap: 10px;

    svg {
      flex-shrink: 0;
    }
  }

  .directory-navs {
    max-height: calc(100vh - var(--header-height) - 200px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cfcfd1 transparent;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #cfcfd1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
      border-radius: 10px;
    }

    > :not(.accordion) {
      .directory-node-container {
        border-top: 1px solid #eee;
      }
    }

    .accordion {
      > div:first-child {
        .directory-node-container {
          border-top: 1px solid #eee;
        }

        .accordion-body {
          .directory-node-container {
            border-top: none;
          }
        }
      }
    }

    .accordion-item {
      border: none;
      border-radius: 0;
    }

    .accordion-button {
      padding: 0;
      border: none;
      border-radius: 0;
      box-shadow: none;

      &::after {
        position: absolute;
        right: 6px;
        background-size: 16px;
        transform: rotate(-90deg);
      }

      &.collapsed {
        border-radius: 0;
      }

      &:not(.collapsed) {
        background-color: transparent;
        box-shadow: none;

        &::after {
          transform: none;
        }
      }
    }

    .accordion-body {
      padding: 0;

      .directory-node-container {
        padding: 16px 11px;
      }

      .accordion-body {
        padding: 0;

        .directory-node-container {
          padding: 16px 27px;
        }
      }
    }
  }

  .directory-node {
    width: 100%;
    border-left: 3px solid #fff;

    &:hover {
      background-color: #fbfbfb;
      border-left-color: #fbfbfb;
    }

    &.active {
      background-color: #f1f3ff;
      border-left-color: #5a65ea;

      .directory-node-link {
        color: #5a65ea;
        font-weight: 600;
      }
    }
  }

  .directory-node-container {
    margin: 0 25px 0 18px;
    padding: 16px 3px;
    border-bottom: 1px solid #eee;
  }

  .directory-node-link {
    display: block;
    color: #000;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: -0.5px;

    &:hover,
    &:target,
    &:focus {
      color: #5a65ea;
    }
  }

  .directory-node-text {
    display: inline-block;
  }
}

@media (max-width: @middle-screen-size) {
  .blog-directory {
    .directory-title {
      padding: 12px;
    }

    .directory-node-container {
      margin: 0 25px 0 10px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .blog-directory {
    .directory-name {
      margin: 0;
      padding: 20px 0 25px;
    }

    .directory-navs {
      max-height: calc(100vh - var(--header-height) - 300px);
    }

    .directory-tree-hide {
      display: none;
    }
  }
}
