import React, { useState, useCallback, useEffect } from 'react';
import BlogDetailTemplate from './blog-detail-template';
import BlogEmpty from './blog-empty';
import {
  getDirectoryTree,
  getRecommendArticles,
} from '../../../utils/chatdoc/blog';
import {
  replaceLinkWithTargetBlank,
  markDownToHtml,
  markDownToHtmlHasAnchor,
} from '../../../utils/chatdoc/markdown';
import { fetchBlogArticles } from '../../../api/crm';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import './blog-detail-main.less';
import _ from 'lodash';

const BlogDetailPreviewMain = () => {
  const id = useGetUrlQueryData('id');
  const source = useGetUrlQueryData('source');
  const version = useGetUrlQueryData('version');
  const category = useGetUrlQueryData('category');
  const [blogArticle, setBlogArticle] = useState({});
  const [blogTreeData, setBlogTreeData] = useState();
  const [blogRecommendArticles, setBlogRecommendArticles] = useState([]);

  const getBlogArticles = useCallback(async () => {
    const params = {
      source,
      page: 1,
      size: 999,
    };
    if (version) {
      params.version = version;
    }
    if (category) {
      params.category = category;
    }
    try {
      const currentBlogArticles = await fetchBlogArticles(params);
      currentBlogArticles.items.map((item) => {
        item.abstractHtml = replaceLinkWithTargetBlank(
          markDownToHtml(item.abstract),
        );
        item.contentHtml = replaceLinkWithTargetBlank(
          markDownToHtmlHasAnchor(item.content),
        );
      });
      const currentArticle = currentBlogArticles.items.find(
        (item) => item.id === Number(id),
      );
      setBlogArticle(currentArticle);
      setBlogTreeData(getDirectoryTree(currentArticle.content));
      setBlogRecommendArticles(
        getRecommendArticles(currentBlogArticles.items, currentArticle),
      );
    } catch (error) {
      console.error(error);
    }
  }, [source, version, category, id]);

  useEffect(() => {
    if (!_.isNil(id) && !_.isNil(source)) {
      getBlogArticles();
    }
  }, [id, source, getBlogArticles]);

  return (
    <main className="blog-detail-main blog-detail-preview-main">
      {blogTreeData ? (
        <BlogDetailTemplate
          isIframe
          blogArticle={blogArticle}
          blogTreeData={blogTreeData}
          blogRecommendArticles={blogRecommendArticles}
        />
      ) : (
        <BlogEmpty loading />
      )}
    </main>
  );
};

export default BlogDetailPreviewMain;
