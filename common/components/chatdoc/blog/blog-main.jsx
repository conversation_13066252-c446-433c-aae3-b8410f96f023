import React from 'react';
import BlogList from './blog-list';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './blog-main.less';

const BlogMain = () => {
  const { blogArticles, blogCategory, blogList } = useGlobalContext();

  return (
    <main className="blog-main">
      <BlogList
        blogArticles={blogArticles}
        blogCategory={blogCategory}
        blogList={blogList}
      />
    </main>
  );
};

export default BlogMain;
