import React, { useState, useCallback, useEffect } from 'react';
import BlogList from './blog-list';
import BlogEmpty from './blog-empty';
import { CHATDOC_BLOG_SOURCE } from '../../../data/chatdoc/data';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { getEnvVariables } from '../../../utils/env';
import { getBlogList } from '../../../utils/chatdoc/blog';
import {
  replaceLinkWithTargetBlank,
  markDownToHtml,
  markDownToHtmlHasAnchor,
} from '../../../utils/chatdoc/markdown';
import { fetchBlogArticles, fetchBlogArticleCategory } from '../../../api/crm';
import './blog-main.less';
import _ from 'lodash';

const BlogPreviewMain = () => {
  const { currentProduct } = getEnvVariables();
  const version = useGetUrlQueryData('version');
  const [blogCategory, setBlogCategory] = useState([]);
  const [blogArticles, setBlogArticles] = useState([]);
  const [blogList, setBlogList] = useState([]);

  const getBlogCategory = useCallback(async () => {
    if (version === undefined) {
      return;
    }
    let params = {
      source: CHATDOC_BLOG_SOURCE[currentProduct.toUpperCase()],
    };
    if (version) {
      params.version = version;
    }
    try {
      const currentBlogCategory = await fetchBlogArticleCategory(params);
      setBlogCategory(currentBlogCategory);
    } catch (error) {
      console.error(error);
    }
  }, [version, currentProduct]);

  const getBlogArticles = useCallback(async () => {
    if (version === undefined) {
      return;
    }
    const params = {
      source: CHATDOC_BLOG_SOURCE[currentProduct.toUpperCase()],
      page: 1,
      size: 999,
    };
    if (version) {
      params.version = version;
    }
    try {
      const currentBlogArticles = await fetchBlogArticles(params);
      currentBlogArticles.items.forEach((item) => {
        item.abstractHtml = replaceLinkWithTargetBlank(
          markDownToHtml(item.abstract),
        );
        item.contentHtml = replaceLinkWithTargetBlank(
          markDownToHtmlHasAnchor(item.content),
        );
      });
      const currentBlogList = getBlogList(
        currentBlogArticles.items,
        blogCategory,
      );
      window.parent.postMessage(
        { updateTime: currentBlogArticles.items[0]?.triggered_at },
        '*',
      );
      setBlogArticles(currentBlogArticles);
      setBlogList(currentBlogList);
    } catch (error) {
      console.error(error);
    }
  }, [blogCategory, version, currentProduct]);

  useEffect(() => {
    getBlogCategory();
  }, [getBlogCategory]);

  useEffect(() => {
    if (!_.isEmpty(blogCategory)) {
      getBlogArticles();
    }
  }, [blogCategory, getBlogArticles]);

  return (
    <main className="blog-main blog-preview-main">
      {!_.isEmpty(blogList) ? (
        <BlogList
          isIframe
          blogArticles={blogArticles}
          blogCategory={blogCategory}
          blogList={blogList}
        />
      ) : (
        <BlogEmpty loading />
      )}
    </main>
  );
};

export default BlogPreviewMain;
