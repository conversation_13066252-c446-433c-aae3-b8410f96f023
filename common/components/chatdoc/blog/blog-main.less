.blog-main {
  position: relative;
  z-index: 0;
  min-height: calc(100vh - var(--header-height));
  overflow: hidden;
  background-color: #fbfbfb;

  .blog-main-bg,
  .blog-main-bg-mini {
    position: absolute;
    top: 0;
    z-index: -1;
    width: 100%;
  }

  .blog-main-bg-mini {
    display: none;
  }
}

@media (max-width: @middle-screen-size) {
  .blog-main {
    .blog-main-bg {
      display: none;
    }

    .blog-main-bg-mini {
      display: block;
    }
  }
}
