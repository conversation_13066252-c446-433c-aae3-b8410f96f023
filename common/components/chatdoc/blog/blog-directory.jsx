import React, { useCallback, useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Accordion } from 'react-bootstrap';
import DirectoryIcon from '../../../assets/icons/chatdoc/directory.svg';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { useCheckScreenSize } from '../../../hooks/useCheckScreenSizeHook';
import classnames from 'classnames';
import './blog-directory.less';

const DirectoryNode = ({ node, clickNodeId, nodeClick }) => {
  return (
    <div
      className={classnames({
        'directory-node': true,
        active: clickNodeId === node.id,
      })}>
      <div className="directory-node-container">
        <a
          className="directory-node-link"
          href={node.href}
          onClick={(event) => nodeClick(event, node)}>
          <p
            className="directory-node-text"
            dangerouslySetInnerHTML={{ __html: node.title }}
          />
        </a>
      </div>
    </div>
  );
};

const findNodeById = (nodes, href, parentIds = [], parentNodes = []) => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];

    if (node.href === href) {
      return {
        node,
        parentIds,
        parentNodes,
      };
    }

    if (node.children) {
      const result = findNodeById(
        node.children,
        href,
        [...parentIds, node.id],
        [...parentNodes, node],
      );
      if (result) {
        return result;
      }
    }
  }

  return null;
};

const getClickNodeId = (treeData, anchor) => {
  const findNodeByIdResult = findNodeById(treeData, anchor);
  if (findNodeByIdResult) {
    const { node } = findNodeByIdResult;
    return node.id;
  } else if (treeData.length > 0) {
    return treeData[0].id;
  }
};

const getAccordionActiveKey = (treeData, anchor) => {
  const findNodeByIdResult = findNodeById(treeData, anchor);
  if (findNodeByIdResult) {
    const { parentIds } = findNodeByIdResult;
    return parentIds;
  }
};

const getPossibleNodes = (treeData, anchor) => {
  const findNodeByIdResult = findNodeById(treeData, anchor);
  if (findNodeByIdResult) {
    const { node, parentNodes } = findNodeByIdResult;
    const needActiveNodes = [...parentNodes, node];
    return needActiveNodes;
  }
};

const BlogDirectory = ({ treeData, title }) => {
  const isMobile = useMobilePageContext();
  const { isMiniScreenSize } = useCheckScreenSize();
  const anchor = typeof window !== 'undefined' && window.location.hash;
  const directoryNavsRef = useRef();
  const observerInstance = useRef(null);
  const mouseInDirectoryTree = useRef(true);
  const [showDirectoryTree, setShowDirectoryTree] = useState(false);
  const [isActiveNode, setIsActiveNode] = useState(true);
  const [possibleNodes, setPossibleNodes] = useState(
    getPossibleNodes(treeData, anchor) || [],
  );
  const [clickNodeId, setClickNodeId] = useState(
    getClickNodeId(treeData, anchor) || null,
  );
  const [accordionActiveKey, setAccordionActiveKey] = useState(
    getAccordionActiveKey(treeData, anchor) || [],
  );

  const getActiveNodeId = useCallback(() => {
    if (isActiveNode) {
      for (let i = 0; i < possibleNodes.length; i++) {
        const id = possibleNodes[i].id;
        const href = possibleNodes[i].href;
        if (!accordionActiveKey.includes(id)) {
          setClickNodeId(id);
          if (!isMobile && href !== window.location.hash) {
            window.history.pushState({}, '', href);
          }
          return;
        }
      }
    }
  }, [
    possibleNodes,
    isMobile,
    isActiveNode,
    accordionActiveKey,
    setClickNodeId,
  ]);

  useEffect(() => {
    getActiveNodeId();

    const eventName = isMobile ? 'touchmove' : 'wheel';
    document.addEventListener(eventName, () => {
      mouseInDirectoryTree.current = false;
    });
    return () => {
      document.removeEventListener(eventName, () => {
        mouseInDirectoryTree.current = false;
      });
    };
  }, [getActiveNodeId, isMobile]);

  const scrollEventListener = _.throttle(
    () => {
      let elements = Array.from(
        document.querySelectorAll(
          '.blog-detail-content h1, .blog-detail-content h2, .blog-detail-content h3',
        ),
      ).map((item, index) => {
        let itemRectTop = item.getBoundingClientRect().top;
        return {
          item,
          top: itemRectTop,
        };
      });

      if (elements.length && !mouseInDirectoryTree.current) {
        elements.sort((a, b) => a.top - b.top);

        let index = elements.findIndex((item) => Math.ceil(item.top) >= 80);
        if (index === -1) {
          index = elements.length - 1;
        } else if (index > 0) {
          if (elements[index].top > (window.innerHeight - 80) / 2) {
            index = index - 1;
          }
        }
        const item = elements[index];
        const href = `#${item.item.id}`;
        if (!isMobile && href !== window.location.hash) {
          window.history.pushState({}, '', href);
        }
        setIsActiveNode(true);
        setPossibleNodes(getPossibleNodes(treeData, href));
      }
    },
    1000 / 60,
    { leading: false, trailing: true },
  );

  useEffect(() => {
    document.addEventListener('scroll', scrollEventListener);
    return () => {
      document.removeEventListener('scroll', scrollEventListener);
    };
  }, [scrollEventListener]);

  const handleAccordionChange = useCallback((newActiveKey) => {
    setIsActiveNode(false);
    setAccordionActiveKey(newActiveKey);
  }, []);

  const onClickNodeId = useCallback(() => {
    if (isMobile) {
      setShowDirectoryTree(false);
    }
  }, [isMobile]);

  const clickDirectoryName = useCallback(() => {
    setShowDirectoryTree(!showDirectoryTree);
  }, [showDirectoryTree]);

  const nodeClick = useCallback(
    (event, node) => {
      event.stopPropagation();
      mouseInDirectoryTree.current = true;
      setClickNodeId(node.id);
      setPossibleNodes(getPossibleNodes(treeData, node.href));
      onClickNodeId();
    },
    [treeData, setClickNodeId, onClickNodeId],
  );

  const handleMouseEnter = () => {
    mouseInDirectoryTree.current = true;
  };

  const handleMouseLeave = () => {
    mouseInDirectoryTree.current = false;
  };

  useEffect(() => {
    function handleClickDom(e) {
      const fixedElement = document.querySelector('.directory-tree');
      const directoryTreeNameElement =
        document.querySelector('.directory-name');
      const targetElement = e.target;
      if (
        !fixedElement.contains(targetElement) &&
        !directoryTreeNameElement.contains(targetElement)
      ) {
        setShowDirectoryTree(false);
      }
    }

    document
      .querySelector('.blog-detail-page')
      .addEventListener('click', handleClickDom);

    return () => {
      document
        .querySelector('.blog-detail-page')
        .removeEventListener('click', handleClickDom);
    };
  }, [showDirectoryTree]);

  useEffect(() => {
    if (isMobile) {
      if (showDirectoryTree && isMiniScreenSize) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'auto';
      }
    }
  }, [showDirectoryTree, isMobile, isMiniScreenSize]);

  const getDirectoryNavsScrollTop = () => {
    const directoryActiveNode = document.querySelector(
      '.directory-node.active',
    );
    if (!directoryActiveNode) return;
    const directoryActiveAccordionHeader =
      directoryActiveNode.closest('.accordion-header');

    const calculateScrollTop = (element) => {
      return (
        element.offsetTop -
        element.getBoundingClientRect().height -
        directoryNavsRef.current.getBoundingClientRect().height / 2
      );
    };
    const directoryNavsScrollTop = directoryActiveAccordionHeader
      ? calculateScrollTop(directoryActiveAccordionHeader)
      : calculateScrollTop(directoryActiveNode);

    directoryNavsRef.current.scrollTop = directoryNavsScrollTop;
  };

  useEffect(() => {
    if (clickNodeId) {
      getDirectoryNavsScrollTop();
    }
  }, [clickNodeId]);

  const renderTree = (nodes) => {
    return (
      nodes.length > 0 &&
      nodes.map((node) => {
        return node.children.length > 0 ? (
          <div key={node.id}>
            <Accordion.Item eventKey={node.id}>
              <Accordion.Header className="accordion-header">
                <DirectoryNode
                  key={node.id}
                  node={node}
                  clickNodeId={clickNodeId}
                  nodeClick={nodeClick}
                />
              </Accordion.Header>
              <Accordion.Body>
                {node.children.length > 0 && renderTree(node.children)}
              </Accordion.Body>
            </Accordion.Item>
          </div>
        ) : (
          <DirectoryNode
            key={node.id}
            node={node}
            clickNodeId={clickNodeId}
            nodeClick={nodeClick}
          />
        );
      })
    );
  };

  return (
    <div className="blog-directory">
      <div className="directory-name" onClick={clickDirectoryName}>
        <DirectoryIcon className="directory-icon" />
        <span className="directory-name-text">Table of Contents</span>
      </div>
      <div
        data-active={showDirectoryTree}
        className={classnames({
          'directory-tree': true,
          'directory-tree-hide': !showDirectoryTree,
        })}>
        <div className="directory-title">{title}</div>
        <div className="directory-navs" ref={directoryNavsRef}>
          {treeData.length > 1 || treeData[0].children?.length > 1 ? (
            <Accordion
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              activeKey={accordionActiveKey}
              alwaysOpen
              onSelect={handleAccordionChange}>
              {renderTree(treeData)}
            </Accordion>
          ) : (
            <DirectoryNode node={treeData[0]} nodeClick={nodeClick} />
          )}
        </div>
      </div>
    </div>
  );
};

BlogDirectory.defaultProps = {
  treeData: [],
};

BlogDirectory.propTypes = {
  title: PropTypes.string,
  treeData: PropTypes.array,
};

export default BlogDirectory;
