import React from 'react';
import PropTypes from 'prop-types';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import classnames from 'classnames';
import dayjs from 'dayjs';
import './blog-card.less';

const BlogCard = ({ href, blogCardData, isIframe, isRecommendBlogCard }) => {
  const isMobile = useMobilePageContext();

  return (
    <a
      href={href}
      target={isIframe ? '_self' : '_blank'}
      className={classnames({
        'blog-card-link': true,
        'blog-card-link-recommend': isRecommendBlogCard,
      })}
      rel="noopener noreferrer">
      <div
        className={classnames({
          'blog-card': true,
          'blog-card-mobile': isMobile,
        })}>
        <div className="blog-card-left">
          <div className="card-content">
            <h3 className="card-title" title={blogCardData.title}>
              {blogCardData.title}
            </h3>
            <div className="card-content-abstract">
              <div className="card-abstract">
                <p
                  className="abstract-text"
                  title={blogCardData.abstract}
                  dangerouslySetInnerHTML={{
                    __html: blogCardData.abstractHtml,
                  }}
                />
              </div>
              <img
                loading="lazy"
                className="card-image card-image-small"
                src={blogCardData.cover_photos[0]}
                alt=""
              />
            </div>
          </div>
          <div>
            <span className="card-category">{blogCardData.category}</span>
            <span className="card-updated-time">
              {dayjs.unix(blogCardData.updated_at).format('MMM DD, YYYY')}
            </span>
          </div>
        </div>
        <div className="blog-card-right">
          <img
            loading="lazy"
            className="card-image card-image-large"
            src={blogCardData.cover_photos[0]}
            alt=""
          />
        </div>
      </div>
    </a>
  );
};

BlogCard.propTypes = {
  blogCardData: PropTypes.object.isRequired,
  href: PropTypes.string.isRequired,
  isIframe: PropTypes.bool,
  isRecommendBlogCard: PropTypes.bool,
};

export default BlogCard;
