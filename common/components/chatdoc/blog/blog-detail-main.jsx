import React from 'react';
import BlogDetailTemplate from './blog-detail-template';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './blog-detail-main.less';

const BlogDetailMain = () => {
  const { blogArticle, blogRecommendArticles, blogTreeData } =
    useGlobalContext();

  return (
    <main className="blog-detail-main">
      <BlogDetailTemplate
        blogArticle={blogArticle}
        blogTreeData={blogTreeData}
        blogRecommendArticles={blogRecommendArticles}
      />
    </main>
  );
};

export default BlogDetailMain;
