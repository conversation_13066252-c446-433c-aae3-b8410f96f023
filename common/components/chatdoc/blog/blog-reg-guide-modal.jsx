import React from 'react';
import PropTypes from 'prop-types';
import { GatsbyImage } from 'gatsby-plugin-image';
import { Modal } from 'react-bootstrap';
import ProjectButton from '../button/project-button';
import { getUrlWithProduct } from '../../../urls';
import { getEnvVariables } from '../../../utils/env';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import './blog-reg-guide-modal.less';

const BlogRegGuideModal = ({ show, handleClose }) => {
  const { blogRegGuideModalData, blogRegGuideModalBg, logo, defaultSrc } =
    useGlobalContext();
  const { currentProduct } = getEnvVariables();
  const buttonHref = getUrlWithProduct(
    blogRegGuideModalData.url.product,
    blogRegGuideModalData.url.pathName,
  );

  return (
    <>
      <Modal
        centered
        show={show}
        onHide={handleClose}
        className="blog-reg-guide-modal">
        <GatsbyImage
          image={blogRegGuideModalBg.childImageSharp.gatsbyImageData}
          alt="Blog Reg Guide Modal Background"
          className="blog-reg-guide-modal-bg"
        />
        <Modal.Header closeButton />
        <Modal.Body>
          <img
            src={logo}
            alt={`${currentProduct} Logo`}
            className="modal-logo"
          />
          <div className="modal-card">
            <h4 className="modal-title">{blogRegGuideModalData.title}</h4>
            <p className="modal-desc">{blogRegGuideModalData.desc}</p>
            <ProjectButton
              className="modal-button"
              text="Try for Free"
              href={getChatDOCLink(buttonHref, { src: defaultSrc })}
            />
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

BlogRegGuideModal.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
};

export default BlogRegGuideModal;
