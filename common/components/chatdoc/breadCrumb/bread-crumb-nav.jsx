import React from 'react';
import PropTypes from 'prop-types';
import { Container, Breadcrumb } from 'react-bootstrap';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { getEnvVariables } from '../../../utils/env';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import './bread-crumb-nav.less';

const BreadCrumbNav = ({ crumbs }) => {
  const src = useGetUrlQueryData('src');
  const { defaultSrc, headerProductName } = useGlobalContext();
  const { currentProduct } = getEnvVariables();

  return (
    <div className="bread-crumb-nav">
      <Container>
        <Breadcrumb>
          <Breadcrumb.Item
            href={getChatDOCLink('/', { src: src || defaultSrc })}>
            {headerProductName || currentProduct}
          </Breadcrumb.Item>
          {crumbs.map((crumbItem, crumbIndex) => (
            <Breadcrumb.Item
              key={crumbIndex}
              href={crumbItem.link}
              active={crumbIndex === crumbs.length - 1}>
              {crumbItem.label}
            </Breadcrumb.Item>
          ))}
        </Breadcrumb>
      </Container>
    </div>
  );
};

BreadCrumbNav.propTypes = {
  crumbs: PropTypes.array,
};

export default BreadCrumbNav;
