import React from 'react';
import PropTypes from 'prop-types';
import { Navbar } from 'react-bootstrap';
import ChatDOCLogo from '../../../assets/logo/chatdoc.png';
import CrossIcon from '../../../assets/icons/cross.svg';
import { getEnvVariables } from '../../../utils/env';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import classnames from 'classnames';
import './nav-brand.less';

const NavBrandContent = ({ name, logo }) => {
  return (
    <span>
      <img src={logo} alt={`${name} Logo`} className="brand-logo" />
      <span className="brand-name">{name}</span>
    </span>
  );
};

const NavBrand = ({ href }) => {
  const { currentProduct } = getEnvVariables();
  const { isCooperateWithChatDOC, logo } = useGlobalContext();

  return (
    <Navbar.Brand
      href={href}
      className={classnames({
        'nav-brand': true,
        'nav-brand-cooperate': isCooperateWithChatDOC,
      })}>
      <NavBrandContent name={currentProduct} logo={logo} />
      {isCooperateWithChatDOC && (
        <>
          <CrossIcon className="cross-icon" />
          <NavBrandContent />
        </>
      )}
    </Navbar.Brand>
  );
};

NavBrandContent.propTypes = {
  name: PropTypes.string,
  logo: PropTypes.string,
};

NavBrandContent.defaultProps = {
  name: 'ChatDOC',
  logo: ChatDOCLogo,
};

NavBrand.propTypes = {
  href: PropTypes.string,
};

export default NavBrand;
