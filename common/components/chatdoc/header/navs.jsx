import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Nav } from 'react-bootstrap';
import APIDropDown from './api-dropdown';
import { ScrollNav } from '../../scroll/pageScroll/page-scroll';
import { getUrlWithProduct } from '../../../urls';
import { getEnvVariables } from '../../../utils/env';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import { listToTree } from '../../../utils';
import { buildUrl } from '../../../utils/chatdoc/guide';

const Navs = ({ handleCloseCollapse, scrollToElement }) => {
  const src = useGetUrlQueryData('src');
  const isMobile = useMobilePageContext();
  const { product } = getEnvVariables();
  const { guideData } = useGlobalContext();

  const chatpaperProjectHref = getUrlWithProduct('chatpaper', 'project');
  const blogHref = getUrlWithProduct(product, 'blog');
  const logHref = getUrlWithProduct(product, 'changelog');

  const guideTreeData = useMemo(() => {
    const list = (guideData || []).map((i) => {
      const obj = {
        ...i,
        parent_id: i.parents?.id || -1,
      };
      const { content, parents, seo, slug, ...rest } = obj;
      return rest;
    });
    return listToTree(list, -1, 'id', 'parent_id');
  }, [guideData]);

  const guideHref = useMemo(() => {
    if (guideTreeData.length) {
      return buildUrl(guideTreeData[0], guideData);
    } else {
      return getUrlWithProduct(product, 'guide');
    }
  }, [guideData, guideTreeData, product]);

  const {
    defaultSrc,
    hidePaperInHeader,
    hideAPIInHeader,
    hidePricingInHeader,
    hideGuideInHeader,
    hideBlogInHeader,
    hideLogInHeader,
  } = useGlobalContext();

  const handlePricingNav = useCallback(() => {
    let scrollPath = '/';
    if (src) {
      scrollPath += `?src=${src}`;
    }
    scrollToElement('pricing', scrollPath);
    handleCloseCollapse();
  }, [handleCloseCollapse, scrollToElement, src]);

  return (
    <Nav className="nav-right">
      {!hidePaperInHeader && (
        <Nav.Link
          href={chatpaperProjectHref}
          onClick={handleCloseCollapse}
          target="_blank"
          className="container">
          ChatPaper
        </Nav.Link>
      )}
      {!hideAPIInHeader && !isMobile && (
        <APIDropDown handleCloseCollapse={handleCloseCollapse} />
      )}
      {!hidePricingInHeader && (
        <Nav.Link className="container" onClick={() => handlePricingNav()}>
          Pricing
        </Nav.Link>
      )}
      {!hideGuideInHeader && (
        <Nav.Link
          href={getChatDOCLink(guideHref, { src: src || defaultSrc })}
          onClick={handleCloseCollapse}
          target="_blank"
          className="container">
          Guide
        </Nav.Link>
      )}
      {!hideBlogInHeader && (
        <Nav.Link
          href={getChatDOCLink(blogHref, { src: src || defaultSrc })}
          onClick={handleCloseCollapse}
          target="_blank"
          className="container">
          Blog
        </Nav.Link>
      )}
      {!hideLogInHeader && (
        <Nav.Link
          href={getChatDOCLink(logHref, { src: src || defaultSrc })}
          onClick={handleCloseCollapse}
          target="_blank"
          className="container">
          Changelog
        </Nav.Link>
      )}
    </Nav>
  );
};

Navs.propTypes = {
  handleCloseCollapse: PropTypes.func,
  scrollToElement: PropTypes.func,
};

export default ScrollNav(Navs);
