import React from 'react';
import PropTypes from 'prop-types';
import { NavDropdown } from 'react-bootstrap';
import { getUrlWithProduct } from '../../../urls';
import { getChatDOCLink } from '../../../utils/chatdoc/chatdoc-link';
import { useGetUrlQueryData } from '../../../hooks/useUrlQueryHook';
import { useGlobalContext } from '../../../hooks/useGlobalHook';
import './api-dropdown.less';

const API_MENU_DATA = [
  {
    name: 'ChatDOC API',
    url: { product: 'chatdoc', pathName: 'api' },
  },
  {
    name: 'ChatDOC PDF Parser',
    url: { product: 'pdfparser', pathName: 'index' },
  },
];

const APIDropDown = ({ handleCloseCollapse }) => {
  const src = useGetUrlQueryData('src');
  const { defaultSrc } = useGlobalContext();

  return (
    <NavDropdown
      title="API"
      className="api-dropdown container nav-link"
      renderMenuOnMount>
      <div className="menu-content">
        {API_MENU_DATA.map((item, index) => (
          <a
            key={index}
            target="_blank"
            rel="noreferrer"
            className="menu-item"
            href={getChatDOCLink(
              getUrlWithProduct(item.url.product, item.url.pathName),
              { src: src || defaultSrc },
            )}
            onClick={handleCloseCollapse}>
            {item.name}
          </a>
        ))}
      </div>
    </NavDropdown>
  );
};

APIDropDown.propTypes = {
  handleCloseCollapse: PropTypes.func,
};

export default APIDropDown;
