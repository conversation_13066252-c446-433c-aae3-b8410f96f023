import React, {
  useState,
  useCallback,
  useEffect,
  useImperativeHandle,
} from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import classnames from 'classnames';
import theme from '../../styles/theme.js';
import './agreement.less';

const AGREEMENT_CONTENT_SERIAL_NUMBER = ['一', '二', '三', '四', '五', '六'];

const getAgreementContent = (usageTimeLimit, hasDisclaimer) => {
  let agreementContent = [
    {
      title: '产品购买',
      desc: `您可以通过商务联系完成购买或申请${
        usageTimeLimit ? '一定数量的' : ''
      }试用${
        usageTimeLimit ? '次数' : ''
      }（联系电话: 010-58426539，邮箱: <EMAIL>）；`,
    },
    {
      title: '服务方式',
      desc: '网上登录访问；',
    },
    {
      title: '使用次数限制',
      desc: '网上登录访问方式会限制使用分析次数，次数使用完请联系您的客户经理；',
    },
    {
      title: '用户声明与承诺',
      desc: '您登录后将视为您本人所为，不能将账号提供给任何第三方人员、机构使用，由此产生的法律问题，将由您承担后果；',
    },
    {
      title: '隐私保护',
      desc: '未经您同意，我们不会向第三方共享您的个人信息，文档信息；',
    },
  ];

  const disclaimerContent = {
    title: '免责声明',
    desc: '请您确保上传的数据，符合国家法律法规和社会道德要求；相关责任和系统无关；',
  };

  if (hasDisclaimer) {
    agreementContent.push(disclaimerContent);
  }

  if (!usageTimeLimit) {
    agreementContent = agreementContent.filter(
      (item) => item.title !== '使用次数限制',
    );
  }

  return agreementContent;
};

const Agreement = React.forwardRef(
  (
    {
      productName,
      themeColor,
      className,
      labelClassName,
      modalClassName,
      agreementData,
      usageTimeLimit,
      hasDisclaimer,
      hideContentTitle,
      setIsAgreementChecked,
    },
    ref,
  ) => {
    const agreementContent =
      agreementData || getAgreementContent(usageTimeLimit, hasDisclaimer);
    const [isCheckedAgreement, setIsCheckedAgreement] = useState(true);
    const [showAgreementModal, setShowAgreementModal] = useState(false);

    const handleCheckAgreement = useCallback(() => {
      setIsCheckedAgreement(!isCheckedAgreement);
    }, [isCheckedAgreement]);

    const handleShowAgreementModal = () => {
      setShowAgreementModal(true);
    };

    const handleCloseAgreementModal = () => {
      setShowAgreementModal(false);
    };

    // 校验是否勾选用户协议
    const validateAgreementChecked = useCallback(() => {
      if (!isCheckedAgreement) {
        return '请阅读并勾选同意《用户协议》';
      }
      return;
    }, [isCheckedAgreement]);

    useImperativeHandle(ref, () => ({
      validateAgreementChecked,
    }));

    useEffect(() => {
      setIsAgreementChecked(isCheckedAgreement);
    }, [isCheckedAgreement, setIsAgreementChecked]);

    useEffect(() => {
      document.documentElement.style.setProperty('--theme-color', themeColor);
    }, [themeColor]);

    return (
      <div
        className={classnames({
          'agreement-container': true,
          [className]: className,
        })}>
        <div className="agreement-checkbox">
          <input
            className="agreement-checkbox-input"
            type="checkbox"
            defaultChecked={isCheckedAgreement}
            onClick={handleCheckAgreement}
          />
          <label
            className={classnames({
              'agreement-checkbox-label': true,
              [labelClassName]: labelClassName,
            })}>
            <span>同意</span>
            <span
              className="agreement-checkbox-desc"
              onClick={handleShowAgreementModal}>
              《用户协议》
            </span>
          </label>
        </div>

        <Modal
          show={showAgreementModal}
          onHide={handleCloseAgreementModal}
          dialogClassName={classnames({
            'agreement-modal': true,
            [modalClassName]: modalClassName,
          })}>
          <Modal.Header closeButton>
            <Modal.Title className="agreement-modal-title">
              用户协议
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="agreement-modal-content">
            {!hideContentTitle && (
              <p className="content-title">
                感谢您使用&nbsp;{productName}
                ，为了保障您的使用权益，请您认真阅读以下内容：
              </p>
            )}
            {agreementContent.map((item, index) => (
              <section key={index} className="content-section">
                <h6 className="content-label">
                  {AGREEMENT_CONTENT_SERIAL_NUMBER[index]}、{item.title}
                </h6>
                <p className="content-desc">{item.desc}</p>
              </section>
            ))}
          </Modal.Body>
        </Modal>
      </div>
    );
  },
);

Agreement.defaultProps = {
  themeColor: theme['primary-color'],
  productName: '',
  usageTimeLimit: false,
};

Agreement.propTypes = {
  themeColor: PropTypes.string,
  productName: PropTypes.string,
  className: PropTypes.string,
  labelClassName: PropTypes.string,
  modalClassName: PropTypes.string,
  agreementData: PropTypes.array,
  usageTimeLimit: PropTypes.bool,
  hasDisclaimer: PropTypes.bool,
  hideContentTitle: PropTypes.bool,
  setIsAgreementChecked: PropTypes.func,
};

export default Agreement;
