:root {
  --theme-color: @primary-color;
}

.agreement-container {
  text-align: right;

  .agreement-checkbox {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: @dark-text-color;
  }

  .agreement-checkbox-input {
    position: relative;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    visibility: hidden;
    cursor: pointer;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f6f8f9;
      border: 1px solid #d8dce6;
      border-radius: 4px;
      visibility: visible;
      content: '';
    }

    &:checked::after {
      background-color: var(--theme-color);
      background-image: url('../../assets/images/checkbox.svg');
      border-color: var(--theme-color);
      content: '';
    }
  }

  .agreement-checkbox-label {
    font-size: 18px;
    line-height: 1;
  }

  .agreement-checkbox-desc {
    color: var(--theme-color);
    cursor: pointer;
  }
}

.agreement-modal {
  width: 50%;
  max-width: none;
  margin-right: auto;
  margin-left: auto;

  .modal-header {
    padding: 30px;

    .btn-close {
      &:focus {
        box-shadow: unset;
      }
    }
  }

  .modal-body {
    padding: 30px 60px 40px;
  }

  .agreement-modal-title {
    font-size: 20px;
  }

  .content-title {
    font-size: 16px;
  }

  .content-section {
    margin-top: 24px;
  }

  .content-label,
  .content-desc {
    font-size: 14px;
    line-height: 22px;
  }

  .content-label {
    margin-bottom: 6px;
    font-weight: bold;
  }

  .content-desc {
    text-indent: 2em;
  }
}

@media (max-width: 1440px) {
  .agreement-modal {
    width: 50%;

    .modal-header {
      padding: 14px;
    }

    .modal-body {
      padding: 14px 30px 24px;
    }

    .agreement-modal-title {
      font-size: 18px;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .agreement-container {
    .agreement-checkbox-input {
      width: 16px;
      height: 16px;
      margin-right: 5px;
    }

    .agreement-checkbox-label {
      font-size: 16px;
    }
  }

  .agreement-modal {
    width: 60%;
  }
}

@media (max-width: @mini-screen-size) {
  .agreement-modal {
    width: 90%;
  }
}
