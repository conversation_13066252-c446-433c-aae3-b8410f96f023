import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'gatsby';
import { Navbar, Nav } from 'react-bootstrap';
import classnames from 'classnames';

const OperationNav = ({ brand, navList, className }) => {
  return (
    <div
      className={classnames({
        'guide-navbar': true,
        [className]: className,
      })}>
      <Navbar expand="lg">
        <Navbar.Brand className="d-xl-none d-lg-none">{brand}</Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="flex-column guide-nav">
            {navList.map((item, index) => (
              <Link
                key={index}
                to={`/guide/${item.path}`}
                className="nav-link guide-nav-link"
                activeClassName="guide-nav-active"
                partiallyActive={true}>
                {item.title}
              </Link>
            ))}
          </Nav>
        </Navbar.Collapse>
      </Navbar>
    </div>
  );
};

OperationNav.propTypes = {
  brand: PropTypes.string,
  navList: PropTypes.arrayOf(PropTypes.object),
  className: PropTypes.string,
};

export default OperationNav;
