import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import handleViewport from 'react-in-viewport';
import classnames from 'classnames';
import { useMobilePageContext } from '../../hooks/useMobilePageContext';

const Block = ({ inViewport, forwardedRef, src, className, poster }) => {
  const videoClass = classnames({
    [className]: className,
  });
  const isMobile = useMobilePageContext();

  useEffect(() => {
    if (isMobile) {
      return;
    }

    const videoDom = forwardedRef.current;
    let playPromise = videoDom.play();
    if (playPromise) {
      playPromise
        .then(() => {
          if (inViewport) {
            setTimeout(() => {
              videoDom.currentTime = 0;
              videoDom.play();
            }, 0);
            // videoDom.play()
          } else {
            setTimeout(() => videoDom.pause(), 0);
            // videoDom.pause()
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [forwardedRef, inViewport, isMobile]);

  return (
    <video
      className={videoClass}
      ref={forwardedRef}
      src={src}
      poster={poster}
      loop
      muted
      preload="true"
      controls={isMobile}>
      您的浏览器不支持 video 标签
    </video>
  );
};

const VideoInViewport = handleViewport(Block, { threshold: 0.8 });

VideoInViewport.propTypes = {
  src: PropTypes.string,
  poster: PropTypes.string,
  className: PropTypes.string,
};

export default VideoInViewport;
