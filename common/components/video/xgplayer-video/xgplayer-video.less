.xgplayer-container {
  position: relative;
  width: 100%;
  height: 100%;
  border: @border-base;

  .poster {
    z-index: 0;
    width: 100%;
    height: 100%;
    background-size: 100%;

    img {
      visibility: hidden;
    }
  }

  .xgplayer {
    position: absolute;
    z-index: 1;
    height: 100% !important;
    background: transparent;

    .trigger {
      .gradient {
        background-image: none;
      }

      .time-preview {
        .xg-cur {
          color: #6d82fa99;
        }

        .xg-curbar {
          background-color: #6d82fa99;
        }
      }
    }

    .xgplayer-controls {
      background-image: linear-gradient(180deg, #6d82fa00 0%, #6d82fa99 100%);
    }

    .xgplayer-time {
      white-space: nowrap;
    }

    .xgplayer-start {
      width: 60px;
      height: 60px;

      .xg-icon-play {
        width: 100%;
        height: 100%;
      }
    }

    .xgplayer-progress {
      .xgplayer-progress-played {
        background: #6d82fa99;
      }

      .xgplayer-progress-btn {
        background: #6d82fa33;
        box-shadow: 0 0 1px #6d82fa33;

        &.active {
          &::before {
            box-shadow: 0 0 1px #6d82fa33;
          }
        }
      }
    }

    .xgplayer-playbackrate {
      .icon-text {
        min-width: 42px;
        height: 20px;
        font-size: 12px;
        line-height: 20px;
      }
    }

    .xg-right-grid {
      xg-icon {
        margin-right: 6px;

        &:first-child {
          margin-right: 0;
        }
      }
    }
  }
}
