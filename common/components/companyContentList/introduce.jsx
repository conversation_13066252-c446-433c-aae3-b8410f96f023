import React, { Fragment } from 'react';
import {
  Con<PERSON><PERSON>,
  Col,
  OverlayTrigger,
  Tooltip,
  NavDropdown,
} from 'react-bootstrap';
import { INTRODUCE_DATA } from '../../data/introduce';
import { getUrlWithProduct } from '../../urls';
import { useFormatMessage } from '../../hooks/useFormatMessageHook';
import classnames from 'classnames';
import qrcodeImg from '../../assets/images/qrcode.jpg';
import logoImg from '../../assets/images/logo-no-bg.png';
import './introduce.less';

const Introduce = () => {
  const { getFormatMessage, isEnLocale } = useFormatMessage();

  const IntroduceContent = ({ contentData, qrcode }) => {
    return (
      <>
        <h6 className="introduce-category">
          {getFormatMessage(contentData.title)}
        </h6>
        {contentData &&
          contentData.dataList.map((dataItem, index) =>
            dataItem.url ? (
              <a
                href={getUrlWithProduct(
                  dataItem.url?.product,
                  dataItem.url?.pathName,
                )}
                className="introduce-link"
                key={index}
                target="_blank"
                rel="noreferrer">
                <p
                  className={classnames({
                    'introduce-name': true,
                    'introduce-name-ellipsis':
                      dataItem.isEllipsisText && !isEnLocale,
                  })}
                  title={getFormatMessage(dataItem.name)}>
                  {getFormatMessage(dataItem.name)}
                </p>
              </a>
            ) : contentData.hasUrl ? (
              <span
                className={contentData.hasUrl && 'introduce-online-soon'}
                key={index}>
                <OverlayTrigger
                  placement="bottom"
                  overlay={
                    <Tooltip className="introduce-tooltip">
                      {getFormatMessage('即将上线，敬请期待')}
                    </Tooltip>
                  }>
                  <p className="introduce-name">
                    {getFormatMessage(dataItem.name)}
                  </p>
                </OverlayTrigger>
              </span>
            ) : (
              <span
                key={index}
                className={classnames({
                  'introduce-connect': true,
                  'introduce-connect-address': dataItem.isAddress,
                })}>
                <p
                  className={classnames({
                    'introduce-name': true,
                    wx: dataItem.isWx,
                  })}>
                  {dataItem.isWx
                    ? getFormatMessage(dataItem.name)
                    : `${getFormatMessage(dataItem.name)}${getFormatMessage(
                        '冒号符号',
                      )}`}
                </p>
                <p className="introduce-desc">
                  {dataItem.desc &&
                    (dataItem.isAddress
                      ? dataItem.desc.map((addressItem) => (
                          <Fragment key={addressItem}>
                            {getFormatMessage(addressItem)}
                            <br />
                          </Fragment>
                        ))
                      : getFormatMessage(dataItem.desc))}
                </p>
                {dataItem.icon && (
                  <>
                    <img
                      className="introduce-icon-wx"
                      src={dataItem.icon}
                      alt={dataItem.iconName}
                    />
                    {dataItem.iconName === 'wx-icon' && (
                      <div className="introduce-img-wx">
                        <img
                          src={qrcode}
                          alt="wx-qrcode"
                          style={{ display: 'inline-block' }}
                        />
                      </div>
                    )}
                  </>
                )}
              </span>
            ),
          )}
      </>
    );
  };

  return (
    <div className="introduce-wrapper">
      <Container className="introduce-container">
        <div className="introduce-left">
          <img
            className="introduce-logo"
            src={logoImg}
            alt="北京庖丁科技有限公司"
          />
          <h4 className="introduce-title">
            {getFormatMessage('引领文档智能新基建')}
          </h4>
        </div>
        <div className="introduce-right">
          {INTRODUCE_DATA.map((item, index) => (
            <Col key={index} className="introduce-item">
              <NavDropdown
                className="introduce-content-mobile dropdown-has-arrow"
                title={getFormatMessage(item.title)}
                renderMenuOnMount>
                <div className="introduce-content-box">
                  <IntroduceContent contentData={item} qrcode={qrcodeImg} />
                </div>
              </NavDropdown>
              <div className="introduce-content-pc">
                <IntroduceContent contentData={item} qrcode={qrcodeImg} />
              </div>
            </Col>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default Introduce;
