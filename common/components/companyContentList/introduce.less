.introduce-wrapper {
  padding: 80px 0;
  background-color: #f6f6f7;

  .introduce-container {
    display: flex;
  }

  .introduce-left {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-right: 90px;
    row-gap: 10px;
  }

  .introduce-logo {
    width: 146px;
    height: auto;
  }

  .introduce-title {
    width: 150px;
    line-height: 32px;
  }

  .introduce-right {
    display: flex;
    width: 100%;
    padding-left: 90px;
    border-left: @border-base;
  }

  .introduce-category {
    margin-bottom: 30px;
  }

  .introduce-link {
    color: @dark-text-color;

    > .introduce-name {
      &:hover {
        color: @primary-color;
      }
    }
  }

  .introduce-connect {
    position: relative;
    display: flex;

    .wx {
      color: @primary-color;
      cursor: pointer;
    }

    .introduce-img-wx img {
      width: 150px;
      height: auto;
    }

    &:hover {
      .introduce-img-wx {
        display: block;
      }
    }
  }

  .introduce-online-soon {
    &:hover {
      color: @primary-color;
      cursor: pointer;
    }
  }

  .introduce-icon-wx {
    margin-left: 5px;
    cursor: pointer;
  }

  .introduce-img-wx {
    position: absolute;
    bottom: 20px;
    left: 25%;
    display: none;
    width: 130px;
    transform: translateX(-50%);
  }

  .introduce-link,
  .introduce-online-soon {
    display: block;
    width: fit-content;
  }

  .introduce-name,
  .introduce-desc {
    width: fit-content;
    font-size: 14px;
    line-height: 30px;
    white-space: nowrap;
  }

  .introduce-name-ellipsis {
    width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .introduce-content-mobile {
    display: none;

    .introduce-category {
      display: none;
    }

    .dropdown-menu {
      background-color: #f6f6f7;
    }
  }
}

.introduce-tooltip {
  top: -5px !important;
}

@media (max-width: @large-screen-size) {
  .introduce-wrapper {
    .introduce-name {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .introduce-wrapper {
    .introduce-left {
      padding: 0 20px;
    }

    .introduce-right {
      padding-left: 20px;
      column-gap: 2%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .introduce-wrapper {
    .introduce-container {
      flex-direction: column;
    }

    .introduce-left {
      padding: 0;
    }

    .introduce-title {
      width: auto;
    }

    .introduce-right {
      padding: 0;
      border: none;
    }

    .introduce-category {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .introduce-wrapper {
    padding: 50px 0;

    .introduce-logo {
      width: 146px;
      height: auto;
    }

    .introduce-title {
      font-size: 24px;
    }

    .introduce-right {
      flex-direction: column;
      padding: 0;
      border-bottom: @border-base;
    }

    .introduce-link {
      width: fit-content;
      max-width: 100%;
    }

    .introduce-name {
      max-width: unset;
      color: @dark-text-color;
      font-size: 14px;
      letter-spacing: -0.166667px;
    }

    .introduce-item {
      .dropdown {
        position: relative;
      }

      .dropdown-toggle {
        display: flex;
        align-items: center;
        margin: 0;
        padding: 20px 5px;
        color: @dark-text-color;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        letter-spacing: -0.166667px;
        border-top: @border-base;
      }

      .dropdown-menu {
        position: static !important;
        padding: 10px 5px 20px;
        border: none;
        transform: none !important;
      }

      &:nth-child(1),
      &:nth-child(3) {
        .introduce-name {
          width: 100%;
        }
      }
    }

    .introduce-content-box {
      display: flex;
      flex-direction: column;
    }

    .introduce-content-mobile {
      display: block;
    }

    .introduce-content-pc {
      display: none;
    }
  }
}
