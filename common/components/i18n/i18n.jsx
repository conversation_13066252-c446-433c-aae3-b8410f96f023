import React from 'react';
import PropTypes from 'prop-types';
import { IntlProvider } from 'react-intl';
import commonI18nEn from './en.json';
import commonI18nZh from './zh.json';

const COMMON_LANGUAGE = {
  zh: commonI18nZh,
  en: commonI18nEn,
};

const I18n = ({ children, locale, i18nMessages }) => {
  return (
    <IntlProvider
      locale={locale}
      messages={{ ...COMMON_LANGUAGE[locale], ...i18nMessages }}>
      {children}
    </IntlProvider>
  );
};

I18n.propTypes = {
  locale: PropTypes.string,
  i18nMessages: PropTypes.object,
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

I18n.defaultProps = {
  locale: 'zh',
};

export default I18n;
