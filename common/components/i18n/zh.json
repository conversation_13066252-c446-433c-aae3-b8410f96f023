{"庖丁科技": "庖丁科技", "北京庖丁科技有限公司": "北京庖丁科技有限公司", "庖丁科技icpLink": "https://beian.miit.gov.cn", "庖丁科技联系电话": "010-58426539", "庖丁科技联系电话2": "010-58426539", "庖丁科技联系邮箱": "<EMAIL>", "庖丁科技联系总部地址": "北京朝阳区中国铁建广场A座704（总部)", "庖丁科技联系子公司地址": "上海 深圳 西安 成都（子公司)", "电话": "电话", "邮箱": "邮箱", "邮箱2": "邮箱", "联系电话": "联系电话", "联系电话2": "联系电话", "地址": "地址", "微信公众号": "微信公众号", "扫码即用": "扫码即用", "Windows": "Windows", "macOS Intel 芯片": "macOS Intel 芯片", "macOS Apple 芯片": "macOS Apple 芯片", "产品": "产品", "ChatDOC": "ChatDOC", "庖丁解文": "庖丁解文", "PDFlux": "PDFlux", "PDFlux SDK": "PDFlux SDK", "Scriber": "<PERSON><PERSON><PERSON>", "Hunter": "<PERSON>", "AutoDoc": "AutoDoc", "Calliper": "Calliper", "Grater": "Grater", "Glazer": "G<PERSON>zer", "Semanmeter": "Semanmeter", "Foundry": "Foundry", "Corrector": "Corrector", "FinOCR": "FinOCR", "意表": "意表", "MetalMesh": "MetalMesh", "金融文档智能审核 AutoDoc": "金融文档智能审核 AutoDoc", "智能抽取 Scriber": "智能抽取 Scriber", "智能搜索 Hunter": "智能搜索 Hunter", "智能撰写 Glazer": "智能撰写 Glazer", "智能比对 Calliper": "智能比对 Calliper", "财报智能提取 MetalMesh": "财报智能提取 MetalMesh", "智能核查银行流水 Grater": "智能核查银行流水 Grater", "文档智能问答": "文档智能问答", "表格智能识别": "表格智能识别", "PDF 数据提取 PDFlux": "PDF 数据提取 PDFlux", "文档智能工具箱 PDFlux SDK": "文档智能工具箱 PDFlux SDK", "自然语言语义理解平台 Foundry": "自然语言语义理解平台 Foundry", "AI 开放平台": "AI 开放平台", "公司名称": "公司名称", "您的姓名": "您的姓名", "职务名称": "您的职务", "企业邮箱": "电子邮箱（请输入您的工作邮箱）", "您的需求": "请输入您的需求", "验证码": "验证码", "申请试用": "申请试用", "VIP登录": "VIP登录", "免费试用": "免费试用", "试用产品": "试用产品", "联系商务": "联系商务", "联系试用": "联系试用", "请输入公司名称": "请输入公司名称", "请输入您的姓名": "请输入您的姓名", "请输入职务名称": "请输入职务名称", "请输入联系电话": "请输入联系电话", "请输入正确的联系电话": "请输入正确的联系电话", "请输入电子邮箱": "请输入电子邮箱", "请输入正确的电子邮箱": "请输入正确的电子邮箱", "请输入您的需求": "请输入您的需求", "需求描述过短": "需求描述过短", "请输入验证码": "请输入验证码", "刷新": "刷新", "刷新过于频繁": "刷新过于频繁，请稍后再试", "请输入您的工作邮箱": "请输入您的工作邮箱", "验证码错误": "验证码错误", "基座1": "基座", "基座2": "基座", "审核1": "审核", "审核2": "审核", "撰写1": "撰写", "撰写2": "撰写", "开发工具": "开发工具", "客户案例": "客户案例", "行业": "行业", "监管": "监管", "银行": "银行", "券商": "券商", "基金": "基金", "其他类型机构": "其他类型机构", "成功故事": "成功故事", "合作与生态": "合作与生态", "行业合作": "行业合作", "行业应用": "行业应用", "提出您的文档智能需求1": "提出您的文档智能需求", "在校学生": "在校学生", "申请免费使用产品": "申请免费使用产品", "高校教师": "高校教师", "申请学术科研合作": "申请学术科研合作", "关于我们": "关于我们", "联系我们1": "联系我们", "联系我们2": "联系我们", "联系我们3": "联系我们", "已服务": "已服务", "大型机构": "大型机构", "识别提取更方便，审核协同皆可用": "识别提取更方便，审核协同皆可用", "基于AI与文档对话，重塑信息处理": "基于AI与文档对话，重塑信息处理", "AI 智能比对 PDF / Word / 扫描件，无惧超长文档和复杂排版": "AI 智能比对 PDF / Word / 扫描件，无惧超长文档和复杂排版", "文档信息智能认知平台, 让机器读懂业务文档": "文档信息智能认知平台, 让机器读懂业务文档", "精通业务流程的金融文档智能撰写平台": "精通业务流程的金融文档智能撰写平台", "专业知识 AI 问答助手1": "专业知识 AI 问答助手", "专业知识 AI 问答助手2": "专业知识 AI 问答助手", "PDF 数据提取神器": "PDF 数据提取神器", "让AI处理你的专属文档": "让AI处理你的专属文档", "智能文档比对": "智能文档比对", "文档信息抽取平台": "文档信息抽取平台", "智能语义搜索引擎": "智能语义搜索引擎", "截屏识别表格": "截屏识别表格", "文档智能审核系统": "文档智能审核系统", "文档内容对比神器": "文档内容对比神器", "银行流水智能识别核查1": "银行流水智能识别核查", "银行流水智能识别核查2": "银行流水智能识别核查", "错别字校正": "错别字校正", "庖丁智能撰写": "庖丁智能撰写", "庖丁智能撰写 Glazer": "庖丁智能撰写 Glazer", "开箱即用的 PDF 工具包1": "开箱即用的 PDF 工具包", "开箱即用的 PDF 工具包2": "开箱即用的 PDF 工具包", "自然语言语义理解平台": "自然语言语义理解平台", "可靠的基础 AI 能力": "可靠的基础 AI 能力", "提供可靠的基础 AI 能力": "提供可靠的基础 AI 能力", "数据智能平台": "数据智能平台", "数智视一体化解决方案": "数智视一体化解决方案", "财报智能提取": "财报智能提取", "个人免费使用": "个人免费使用", "个人版使用": "个人版使用", "内测体验": "内测体验", "企业版咨询": "企业版咨询", "了解更多": "了解更多", "网页版": "网页版", "客户端下载": "客户端下载", "API 接入": "API 接入", "请在电脑端使用或下载": "请在电脑端使用或下载", "表格识别": "表格识别", "文字识别": "文字识别", "目录结构识别": "目录结构识别", "文档全景结构识别": "文档全景结构识别", "技术文档": "技术文档", "冒号符号": "：", "港交所": "港交所", "中国银行": "中国银行", "华泰联合证券": "华泰联合证券", "银河证券": "银河证券", "港交所描述": "用庖丁科技 Jura 系统审核年报，提效 80%", "中国银行描述": "用庖丁科技智能文档审核平台，助推金融数字化转型", "华泰联合证券描述": "携手庖丁科技打造金融科技新生态", "银河证券描述": "债券募集说明书撰写，从2天到1小时", "深受客户喜爱与信赖": "深受客户喜爱与信赖", "可定制拓展更多文件类型": "可定制拓展更多文件类型", "推荐阅读": "推荐阅读", "新闻中心": "新闻中心", "相关产品": "相关产品", "引领文档智能新基建": "引领文档智能新基建", "监管解决方案": "监管解决方案", "银行解决方案": "银行解决方案", "券商解决方案": "券商解决方案", "基金解决方案": "基金解决方案", "其他行业解决方案": "其他行业解决方案", "什么是文档智能": "什么是文档智能", "表格识别技术比较": "表格识别技术比较", "利用大语言模型": "利用大语言模型实现可靠的企业级知识问答应用", "通过增强 PDF 结构识别": "通过增强 PDF 结构识别，革新检索增强生成技术(RAG)", "即将上线，敬请期待": "即将上线，敬请期待", "领先机构 长期信赖": "领先机构 长期信赖"}