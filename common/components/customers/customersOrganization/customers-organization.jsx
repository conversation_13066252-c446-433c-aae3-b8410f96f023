import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import CommonSwiper from '../../swiper/common-swiper';
import './customers-organization.less';
import _ from 'lodash';

const CustomersOrganization = ({
  organizationData,
  organizationOrder,
  organizationTitle,
}) => {
  const currentOrganizationData = _.sortBy(
    organizationData.filter((item) => organizationOrder.includes(item.name)),
    (item) => organizationOrder.indexOf(item.name),
  );

  return (
    <div className="customers-organization-wrapper">
      <Container className="organization-container">
        <p className="organization-title">{organizationTitle}</p>
        <div className="organization-banner">
          {currentOrganizationData.map((item, index) => (
            <GatsbyImage
              className="organization-logo"
              image={item.childImageSharp.gatsbyImageData}
              alt={item.name}
              key={index}
            />
          ))}
        </div>
        <div className="organization-banner-swiper">
          <CommonSwiper
            slidesPerView={2}
            slidesPerGroup={2}
            swiperData={currentOrganizationData}
            swiperSlideChildren={(item) => (
              <GatsbyImage
                className="organization-logo"
                image={item.childImageSharp.gatsbyImageData}
                alt={item.name}
              />
            )}
          />
        </div>
      </Container>
    </div>
  );
};

CustomersOrganization.propTypes = {
  organizationData: PropTypes.array,
  organizationOrder: PropTypes.array,
  organizationTitle: PropTypes.oneOfType([PropTypes.element, PropTypes.string]),
};

export default CustomersOrganization;
