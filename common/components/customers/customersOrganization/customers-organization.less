.customers-organization-wrapper {
  margin: 40px 0;

  .organization-title {
    display: flex;
    flex-direction: row;
    margin-bottom: 20px;
    color: #9ea0a5;
    font-size: 18px;
    line-height: 26px;

    &::before,
    &::after {
      flex: 1 1;
      margin: auto;
      border: @dashed-border-base;
      content: '';
    }
  }

  .organization-banner {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    width: 80%;
    margin: 0 auto;
  }

  .organization-banner-swiper {
    display: none;
  }

  .organization-logo {
    width: 20%;
  }
}

@media (max-width: @small-screen-size) {
  .customers-organization-wrapper {
    margin: 30px 0;

    .organization-title {
      margin-bottom: 15px;
    }

    .organization-banner {
      width: 90%;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .customers-organization-wrapper {
    .organization-title {
      margin-bottom: 0;
      font-size: 14px;
    }

    .organization-banner {
      display: none;
    }

    .organization-banner-swiper {
      display: block;
      width: 94%;

      .swiper-wrapper {
        margin-bottom: 20px;
      }

      .swiper-pagination {
        position: absolute;
        margin-top: 0;
      }

      .swiper-pagination-bullet {
        background-color: #ccc;
        opacity: 1;
      }

      .swiper-pagination-bullet-active {
        background-color: #3478f6;
      }
    }

    .organization-logo {
      width: 100%;
    }
  }
}
