.customers-wrapper {
  position: relative;
  padding-bottom: 120px;
  background-color: #000;

  .customer-trapezoid-bottom {
    position: absolute;
    bottom: 0;
    z-index: -1;
    width: 100%;
    height: 663px;
    overflow: hidden;

    &::after {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #000;
      transform: skewY(-7deg);
      transform-origin: 100% 0;
      content: '';
    }
  }

  .container {
    padding-right: 0;
    padding-left: 0;
  }

  .customer-type {
    position: relative;
    flex-wrap: nowrap;
    margin: 60px 0;
    overflow-x: hidden;

    .swiper-slide {
      flex: none;
      flex-shrink: 0;
      width: 20%;
      padding: 0;
      color: #fff;
      font-weight: 400;
      font-size: 24px;
      line-height: 34px;
      letter-spacing: 2px;
      text-align: center;
      opacity: 0.6;
      user-select: none;
    }

    .swiper-slide-active {
      font-weight: 500;
      opacity: 1;
    }

    .swiper-button-prev,
    .swiper-button-next {
      width: 14px;
      color: #fff;
      user-select: none;

      &::after {
        font-size: 20px;
        user-select: none;
      }
    }

    .swiper-button-prev {
      left: calc(50% - 130px);
    }

    .swiper-button-next {
      right: calc(50% - 130px);
    }
  }

  .customer-list {
    width: 88%;
    height: 100px;
    margin-left: 6%;

    .swiper-slide {
      display: flex;
      flex-wrap: wrap;
      place-content: flex-start center;
    }
  }

  .customer-item {
    flex: none;
    width: 20%;
    padding: 0;
    user-select: none;
  }
}

.customers-trapezoid-wrapper {
  background-color: transparent;
}

.index-customers-wrapper {
  top: -150px;
  z-index: 1;
}

@media (max-width: @max-screen-size) {
  .customers-wrapper {
    padding-bottom: 90px;

    .customer-trapezoid-bottom {
      height: 150%;
    }
  }

  .customers-hide-title-wrapper {
    .customer-trapezoid-bottom {
      height: 200%;
    }
  }
}

@media (max-width: @large-screen-size) {
  .customers-wrapper {
    padding-bottom: 80px;

    .customer-trapezoid-bottom {
      height: 145%;
    }

    .customer-type {
      .swiper-slide {
        font-size: 20px;
        line-height: 30px;
      }

      .swiper-button-prev,
      .swiper-button-next {
        &::after {
          font-size: 18px;
        }
      }

      .swiper-button-prev {
        left: calc(50% - 110px);
      }

      .swiper-button-next {
        right: calc(50% - 110px);
      }
    }
  }

  .customers-hide-title-wrapper {
    .customer-trapezoid-bottom {
      height: 200%;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .customers-wrapper {
    padding-bottom: 50px;

    .customer-trapezoid-bottom {
      height: 140%;
    }
  }

  .customers-hide-title-wrapper {
    .customer-trapezoid-bottom {
      height: 200%;
    }
  }
}

@media (max-width: @small-screen-size) {
  .customers-wrapper {
    padding-bottom: 35px;

    .customer-trapezoid-bottom {
      height: 125%;
    }

    .customer-type {
      margin: 40px 0;
    }

    .customer-list {
      height: auto;
    }

    .customer-item {
      width: 50%;
    }
  }

  .customers-hide-title-wrapper {
    .customer-trapezoid-bottom {
      height: 135%;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .customers-wrapper {
    .customer-trapezoid-bottom {
      height: 120%;
    }

    .container {
      padding-right: 20px;
      padding-left: 20px;
    }

    .customer-type {
      margin: 32px 0 40px;

      .swiper-slide {
        font-size: 14px;
      }

      .swiper-button-prev,
      .swiper-button-next {
        &::after {
          font-size: 14px;
        }
      }

      .swiper-button-prev {
        left: calc(50% - 90px);
      }

      .swiper-button-next {
        right: calc(50% - 90px);
      }
    }

    .customer-list {
      width: 100%;
      margin-left: 0;
    }
  }

  .customers-hide-title-wrapper {
    .customer-trapezoid-bottom {
      height: 130%;
    }
  }
}
