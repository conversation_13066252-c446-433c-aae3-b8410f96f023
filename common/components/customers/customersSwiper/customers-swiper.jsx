import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import CustomersTitle from '../customersTitle/customers-title';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Navigation, Controller } from 'swiper';
import { useHandleResize } from '../../../hooks/useHandleResizeHook';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import { SCREEN_SIZE_WIDTH } from '../../../data/constant';
import classnames from 'classnames';
import 'swiper/css';
import 'swiper/css/navigation';
import _ from 'lodash';
import './customers-swiper.less';

const CUSTOMERS_ORDER = [
  {
    type: '监管',
    customers: ['上交所', '深交所', '中金所'],
  },
  {
    type: '银行',
    customers: ['中国银行', '中国工商银行', '中信银行', '宁波银行', '兴业银行'],
  },
  {
    type: '券商',
    customers: ['中信证券', '中信建投', '中金', '华泰联合证券', '海通证券'],
  },
  {
    type: '基金',
    customers: ['易方达', '汇添富基金', '华夏基金', '广发基金', '工银瑞信'],
  },
  {
    type: '其他类型机构',
    customers: ['pwc', '中诚信国际'],
  },
];

const Customers = ({
  className,
  showCustomersCaseTitle,
  hideTrapezoidBackground,
  data,
}) => {
  const [slidesPerView, setSlidesPerView] = useState(5);
  const [typeSwiper, setTypeSwiper] = useState(null);
  const [customersSwiper, setCustomersSwiper] = useState(null);

  const {
    swiperData: { nodes: imagesNodes },
  } = data;
  const { getFormatMessage } = useFormatMessage();

  const getSlidesPerView = () => {
    const windowWidth = window.innerWidth;
    if (windowWidth > SCREEN_SIZE_WIDTH.SMALL_SCREEN_SIZE) {
      setSlidesPerView(5);
    } else if (windowWidth > SCREEN_SIZE_WIDTH.MINI_SCREEN_SIZE) {
      setSlidesPerView(3);
    } else {
      setSlidesPerView(1);
    }
  };

  useHandleResize(() => {
    getSlidesPerView();
  });

  const handleCustomerSwiperTouchStart = () => {
    typeSwiper.autoplay.stop();
  };

  const handleCustomerSwiperTouchEnd = () => {
    typeSwiper.autoplay.start();
  };

  return (
    <div
      className={classnames({
        'customers-wrapper': true,
        'customers-trapezoid-wrapper': !hideTrapezoidBackground,
        'customers-hide-title-wrapper': !showCustomersCaseTitle,
        [className]: className,
      })}>
      {!hideTrapezoidBackground && (
        <div className="customer-trapezoid-bottom" />
      )}
      <Container>
        {showCustomersCaseTitle && <CustomersTitle />}
        <Swiper
          modules={[Autoplay, Navigation, Controller]}
          initialSlide={0}
          slidesPerView={slidesPerView}
          centeredSlides
          loop
          autoplay={{
            disableOnInteraction: false,
          }}
          navigation
          slideToClickedSlide
          className="customer-type"
          onSwiper={setTypeSwiper}
          controller={{ control: customersSwiper }}>
          {CUSTOMERS_ORDER.map((item) => (
            <SwiperSlide key={item.type}>
              {getFormatMessage(item.type)}
            </SwiperSlide>
          ))}
        </Swiper>
        <Swiper
          modules={[Autoplay, Controller]}
          initialSlide={0}
          loop
          className="customer-list"
          loopAdditionalSlides={slidesPerView - 1}
          onSwiper={setCustomersSwiper}
          onTouchStart={handleCustomerSwiperTouchStart}
          onTouchEnd={handleCustomerSwiperTouchEnd}
          controller={{ control: typeSwiper }}>
          {CUSTOMERS_ORDER.map((item, index) => (
            <SwiperSlide key={index}>
              {_.sortBy(
                imagesNodes.filter((node) =>
                  item.customers.includes(node.name),
                ),
                [(node) => item.customers.indexOf(node.name)],
              ).map((node, index) => (
                <div key={index} className="customer-item">
                  <GatsbyImage
                    className="customer-icon"
                    image={node.childImageSharp.gatsbyImageData}
                    alt={node.name}
                  />
                </div>
              ))}
            </SwiperSlide>
          ))}
        </Swiper>
      </Container>
    </div>
  );
};

Customers.propTypes = {
  className: PropTypes.string,
  showCustomersCaseTitle: PropTypes.bool,
  hideTrapezoidBackground: PropTypes.bool,
  data: PropTypes.object,
};

export default Customers;
