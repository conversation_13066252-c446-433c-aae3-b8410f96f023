import React from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import { Container, Col, Row } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { ScrollToElement } from '../../scroll/pageScroll/page-scroll';
import _ from 'lodash';
import './customers.less';

const CUSTOMERS_ORDER = [
  '上交所',
  '深交所',
  '中金所',
  '北金所',
  '证通股份',
  '中信证券',
  '中信建投',
  '中金',
  '华泰联合证券',
  '海通证券',
  '国泰君安',
  '银河证券',
  '招商证券',
  '光大证券',
  '国信证券',
  '中泰证券',
  '兴业证券',
  '长江证券',
  '东兴证券',
  '财通证券',
  '广发证券',
  '开源证券',
  '东北证券',
  '民生证券',
  '平安证券',
  '中银国际证券',
  '西南证券',
  '中德证券',
  '华安证券',
  '中国银行',
  '中国农业银行',
  '中信银行',
  '宁波银行',
  '杭州银行',
  '青岛银行',
  '华夏基金',
  '工银瑞信',
  '易方达',
  '富国基金',
  '交银施罗德',
  '中诚信国际',
  'pwc',
];

const Customers = ({ xs, md, lg, data }) => {
  const {
    customersData: { nodes: imagesNodes },
  } = data;

  return (
    <Container className="customers">
      <h2>我们服务的客户</h2>
      <Row>
        {_.sortBy(
          imagesNodes.filter((node) => CUSTOMERS_ORDER.includes(node.name)),
          [(node) => CUSTOMERS_ORDER.indexOf(node.name)],
        ).map((node, index) => (
          <Col key={index} xs={xs} md={md} lg={lg}>
            <GatsbyImage
              image={node.childImageSharp.gatsbyImageData}
              alt={node.name}
            />
          </Col>
        ))}
      </Row>
    </Container>
  );
};

export default ScrollToElement(Customers, 'customers');

Customers.defaultProps = {
  xs: 6,
  md: 6,
  lg: 2,
};

Customers.propTypes = {
  xs: PropTypes.number,
  md: PropTypes.number,
  lg: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};
