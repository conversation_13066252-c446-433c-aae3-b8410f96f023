import React from 'react';
import NumberCounter from '../../number/numberCounter/number-counter';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import { CUSTOMERS_NUMBER } from '../../../data/constant';
import classnames from 'classnames';
import './customers-title.less';

const CustomersTitle = () => {
  const { isEnLocale, getFormatMessage } = useFormatMessage();

  return (
    <h3
      className={classnames({
        'common-title': true,
        'customers-title': true,
        'customers-title-en': isEnLocale,
      })}>
      <div className="customers-title-number">
        {getFormatMessage('已服务')}
        <div className="customers-number">
          <NumberCounter value={CUSTOMERS_NUMBER} duration={1000} />
        </div>
        +&nbsp;
      </div>
      <br />
      <div>{getFormatMessage('大型机构')}</div>
    </h3>
  );
};

export default CustomersTitle;
