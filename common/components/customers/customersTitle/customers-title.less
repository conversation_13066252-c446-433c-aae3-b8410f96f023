.customers-title {
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff !important;
  font-size: 36px;
  line-height: 50px;
  letter-spacing: 2px;
  white-space: nowrap;

  .customers-title-number {
    display: flex;
    align-items: center;
  }

  .customers-number {
    display: inline-flex;
    justify-content: space-around;
    width: 106px;
    height: 56px;
    margin: 0 10px;

    .number-counter {
      .odometer {
        border: 2px solid rgba(255, 255, 255, 50%);
        border-radius: 8px;
      }

      .odometer-digit {
        border-right: 2px solid rgba(255, 255, 255, 50%);

        &:last-child {
          border-right: unset;
        }
      }
    }
  }
}

@media (max-width: @middle-screen-size) {
  .customers-title {
    font-size: 30px;

    .customers-number {
      width: 80px;
      height: 45px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .customers-title {
    font-size: 24px;
    line-height: 34px;

    .customers-number {
      width: 70px;
      height: 37px;
    }
  }

  .customers-title-en {
    display: block;
    padding-bottom: 10px;
    line-height: 20px !important;
    text-align: center;

    .customers-title-number {
      justify-content: center;
    }
  }
}
