import React from 'react';
import './tags.less';
import { useFormatMessage } from '../../hooks/useFormatMessageHook';

export const HotTag = () => {
  return <span className="tag hot-tag">HOT</span>;
};

export const ScanUseTag = () => {
  const { getFormatMessage } = useFormatMessage();
  return (
    <span className="tag scan-use-tag">{getFormatMessage('扫码即用')}</span>
  );
};

export const NewTag = () => {
  return <span className="tag new-tag">NEW</span>;
};
