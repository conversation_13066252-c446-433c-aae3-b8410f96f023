import React from 'react';
import PropTypes from 'prop-types';

const SEO = ({ description, meta, title, children, image, icon }) => {
  return (
    <>
      <title>{title}</title>
      <meta name="description" content={description} />
      {icon && <link rel="icon" href={icon} type="image/x-icon" />}
      {meta.map((item) => (
        <meta key={item.name} name={item.name} content={item.content} />
      ))}
      {image && (
        <meta
          property="og:image"
          content={`https://www.paodingai.com${image}`}
        />
      )}
      {children}
    </>
  );
};

SEO.defaultProps = {
  meta: [],
  description: ``,
};

SEO.propTypes = {
  description: PropTypes.string,
  meta: PropTypes.arrayOf(PropTypes.object),
  title: PropTypes.string.isRequired,
  icon: PropTypes.string,
};

export default SEO;
