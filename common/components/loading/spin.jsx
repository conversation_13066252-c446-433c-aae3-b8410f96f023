import React from 'react';
import { Spinner } from 'react-bootstrap';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import './index.less';

const Spin = ({ children, loading, mode, animation, variant }) => {
  if (mode === 'hide') {
    const spinningClass = classnames({
      'spin-spinning': true,
      loading: !loading,
    });

    const wrapperClass = classnames({
      'spin-wrapper': true,
      loading: loading,
    });
    return (
      <div className="spin">
        <div className={spinningClass}>
          <Spinner
            className="spin-dot"
            animation={animation}
            variant={variant}
          />
        </div>
        <div className={wrapperClass}>{children}</div>
      </div>
    );
  } else if (mode === 'render') {
    return (
      <div className="spin">
        {loading ? (
          <div className="spin-spinning">
            <Spinner
              className="spin-dot"
              animation={animation}
              variant={variant}
            />
          </div>
        ) : (
          <div className="spin-wrapper">{children}</div>
        )}
      </div>
    );
  } else {
    return null;
  }
};

export default Spin;

Spin.defaultProps = {
  loading: true,
  mode: 'hide',
  animation: 'grow',
  variant: 'primary',
};

Spin.propTypes = {
  mode: PropTypes.oneOf(['render', 'hide']),
  animation: PropTypes.oneOf(['border', 'grow']),
  variant: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'danger',
    'warning',
    'info',
    'light',
    'dark',
  ]),
};
