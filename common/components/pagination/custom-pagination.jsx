import React from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import './index.less';

const CustomPagination = ({ className, current, total, onChange }) => {
  const getPageNumbers = () => {
    const pages = [];
    let startPage, endPage;

    if (total <= 5) {
      startPage = 1;
      endPage = total;
    } else {
      if (current <= 3) {
        startPage = 1;
        endPage = 5;
      } else if (current + 2 >= total) {
        startPage = total - 4;
        endPage = total;
      } else {
        startPage = current - 2;
        endPage = current + 2;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const prevClick = () => {
    if (current > 1) {
      onChange(current - 1);
    }
  };

  const nextClick = () => {
    if (current < total) {
      onChange(current + 1);
    }
  };

  return (
    <div
      className={classnames({
        'custom-pagination': true,
        [className]: className,
      })}>
      <span
        className={current === 1 ? 'disabled prev' : 'prev'}
        onClick={prevClick}>
        &lt;
      </span>
      {getPageNumbers().map((page) => (
        <span
          key={page}
          onClick={() => onChange(page)}
          className={page === current ? 'page active' : 'page'}>
          {page}
        </span>
      ))}

      <span
        className={current === total ? 'disabled next' : 'next'}
        onClick={nextClick}>
        &gt;
      </span>
    </div>
  );
};

CustomPagination.propTypes = {
  className: PropTypes.string,
  current: PropTypes.number,
  total: PropTypes.number,
  onChange: PropTypes.func,
};

export default CustomPagination;
