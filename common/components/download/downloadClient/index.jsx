import React from 'react';
import PropTypes from 'prop-types';
import downloadSvg from '../../../assets/images/download2.svg';
import downloadClientSvg from '../../../assets/images/download-client.svg';
import { getEnvVariables } from '../../../utils/env';
import { downloadClient } from '../../../utils/download';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import { isWindows } from '../../../utils';
import './index.less';

const DownloadClientText = ({ arrowDownloadIcon, downloadTitle }) => (
  <>
    <img
      src={arrowDownloadIcon}
      alt="arrow-download"
      className="arrow-download"
    />
    <span>{downloadTitle}</span>
  </>
);

const DownloadClient = ({
  productName,
  downloadText,
  downloadTitle,
  downloadUrl,
  arrowDownloadIcon,
}) => {
  const downloadVersionList = [
    ['Windows', ` /downloads/${productName}-latest.exe`],
    ['macOS Intel 芯片', ` /downloads/${productName}-latest.dmg`],
    ['macOS Apple 芯片', ` /downloads/${productName}-latest-arm64.dmg`],
  ];
  const isMobile = useMobilePageContext();
  const { getFormatMessage } = useFormatMessage();

  return (
    <div className="download-client">
      {isMobile ? (
        <button className="default-btn download-mobile">
          <span>{getFormatMessage(downloadText)}</span>
        </button>
      ) : isWindows() ? (
        <a href={downloadUrl} download className="default-btn download-trigger">
          <DownloadClientText
            arrowDownloadIcon={arrowDownloadIcon}
            downloadTitle={getFormatMessage(downloadTitle)}
          />
        </a>
      ) : (
        <button className="default-btn download-trigger">
          <DownloadClientText
            arrowDownloadIcon={arrowDownloadIcon}
            downloadTitle={getFormatMessage(downloadTitle)}
          />
        </button>
      )}

      <div className="more-version">
        <div className="version-product">
          {downloadVersionList.map((item) => (
            <a
              className="download-button"
              href={item[1]}
              download
              key={item[0]}>
              {getFormatMessage(item[0])}
              <img
                src={downloadClientSvg}
                className="download-icon"
                alt="download-client"
              />
            </a>
          ))}
        </div>
      </div>
    </div>
  );
};

DownloadClient.defaultProps = {
  productName: getEnvVariables().currentProduct,
  downloadText: '请在电脑端下载',
  downloadTitle: '免费下载',
  arrowDownloadIcon: downloadSvg,
  downloadUrl: downloadClient(),
};

DownloadClient.propTypes = {
  productName: PropTypes.string.isRequired,
  downloadText: PropTypes.string,
  downloadTitle: PropTypes.string,
  downloadUrl: PropTypes.string,
  arrowDownloadIcon: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),
};

export default DownloadClient;
