.download-client {
  position: relative;

  .download-trigger {
    &:hover {
      & + .more-version {
        display: block;
      }
    }
  }

  .more-version {
    position: absolute;
    top: 65px;
    left: -60px;
    z-index: 2;
    display: none;
    padding-top: 16px;
    background: transparent;

    .version-product {
      position: relative;
      width: 260px;
      padding: 8px 12px;
      background: #fff;
      border: 1px solid #eaedf3;
      border-radius: @border-radius-base;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 20%);

      &::before {
        position: absolute;
        top: -7px;
        left: 120px;
        display: block;
        width: 10px;
        height: 10px;
        background: #fff;
        border: none;
        border-top: 1px solid #eaedf3;
        border-right: 1px solid #eaedf3;
        transform: rotate(-45deg);
        content: '';
      }
    }

    &:hover {
      display: block;
    }
  }

  .download-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 10px;
    color: @text-color;
    font-weight: 500;

    &:hover {
      background-color: #eee;
      border-radius: @border-radius-base;
    }

    .download-icon {
      width: 14px;
      height: 14px;
    }
  }
}
