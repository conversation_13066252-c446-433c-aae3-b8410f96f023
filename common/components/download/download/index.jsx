import React from 'react';
import PropTypes from 'prop-types';
import { useMobilePageContext } from '../../../hooks/useMobilePageContext';
import classnames from 'classnames';
import './index.less';

const Download = ({
  className,
  downloadText,
  downloadMobileText,
  downloadUrl,
}) => {
  const isMobile = useMobilePageContext();

  return (
    <div
      className={classnames({
        'download-container': true,
        [className]: className,
      })}>
      {isMobile ? (
        <button className="default-btn download-btn-mobile">
          <span className="download-text">{downloadMobileText}</span>
        </button>
      ) : (
        <a download href={downloadUrl} className="default-btn download-btn">
          <span className="download-text">{downloadText}</span>
        </a>
      )}
    </div>
  );
};

Download.defaultProps = {
  downloadText: '立即下载',
  downloadMobileText: '请在电脑端使用或下载',
};

Download.propTypes = {
  className: PropTypes.string,
  downloadText: PropTypes.string,
  downloadMobileText: PropTypes.string,
  downloadUrl: PropTypes.string,
};

export default Download;
