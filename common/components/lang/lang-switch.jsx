import React from 'react';
import PropTypes from 'prop-types';
import { NavDropdown } from 'react-bootstrap';
import classNames from 'classnames';

const langOptions = [
  {
    name: '中文',
    value: 'zh',
  },
  {
    name: 'English',
    value: 'en',
  },
];

const LangSwitch = ({ lang, setLang, isShowCollapse }) => {
  if (isShowCollapse) {
    return (
      <div className="lang-dropdown">
        <div className="lang-icon"></div>
        {langOptions.map((type) => (
          <span
            key={type.value}
            className={classNames({
              'lang-link': true,
              active: type.value === lang,
            })}
            onClick={() => {
              setLang(type.value);
            }}>
            {type.name}
          </span>
        ))}
      </div>
    );
  }
  return (
    <NavDropdown
      title={
        <>
          <div className="lang-icon"></div>
          <span>{langOptions.find((type) => type.value === lang).name}</span>
        </>
      }
      bsPrefix="lang-dropdown-toggle"
      className="lang-dropdown"
      id="lang-dropdown"
      renderMenuOnMount>
      {langOptions.map((type) => (
        <NavDropdown.Item
          key={type.value}
          className="lang-link"
          onClick={() => {
            setLang(type.value);
          }}>
          {type.name}
        </NavDropdown.Item>
      ))}
    </NavDropdown>
  );
};

LangSwitch.propTypes = {
  lang: PropTypes.string,
  setLang: PropTypes.func,
};

export default LangSwitch;
