import React from 'react';
import PropTypes from 'prop-types';
import { getConnectHref } from '../../../utils/connect';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import classnames from 'classnames';
import './connect-btn.less';

const ConnectBtn = ({ productName, buttonTitle, buttonIcon, className }) => {
  const { getFormatMessage } = useFormatMessage();
  const connectHref = getConnectHref({ source: productName });

  return (
    <a
      href={connectHref}
      target="_blank"
      title={getFormatMessage(buttonTitle) || buttonTitle}
      className={classnames({
        'default-btn': true,
        'connect-btn': true,
        [className]: className,
      })}
      rel="noreferrer">
      {getFormatMessage(buttonTitle) || buttonTitle}
      {buttonIcon}
    </a>
  );
};

ConnectBtn.defaultProps = {
  buttonTitle: '联系我们1',
};

ConnectBtn.propTypes = {
  productName: PropTypes.string,
  buttonTitle: PropTypes.string,
  buttonIcon: PropTypes.element,
  className: PropTypes.string,
};

export default ConnectBtn;
