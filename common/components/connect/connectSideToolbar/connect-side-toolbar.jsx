import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Card, OverlayTrigger, Popover } from 'react-bootstrap';
import ConsultSvg from '../../../assets/icons/consult.svg';
import ConnectSvg from '../../../assets/icons/connect.svg';
import { getConnectHref } from '../../../utils/connect';
import { CONNECT_PHONE_DATA } from '../../../data/constant';
import classnames from 'classnames';
import './connect-side-toolbar.less';

const ConnectSideToolbar = ({
  className,
  hidePhoneConnect,
  businessConsultText,
  phoneConnectText,
  consultSvg,
  connectSvg,
}) => {
  const businessConsultHref = getConnectHref();

  const [showConnectPopover, setShowConnectPopover] = useState(false);

  const handleOnMouseEnter = () => {
    setShowConnectPopover(true);
  };

  const handleOnMouseLeave = () => {
    setShowConnectPopover(false);
  };

  return (
    <Card
      className={classnames({
        [className]: className,
        'connect-side-toolbar': true,
        'hide-phone-connect': hidePhoneConnect,
      })}>
      <a
        href={businessConsultHref}
        target="_blank"
        rel="noreferrer"
        className="side-toolbar-link consult-link">
        {consultSvg}
        <span className="side-toolbar-text">
          {businessConsultText || '咨询'}
        </span>
      </a>
      {!hidePhoneConnect && (
        <>
          <div className="side-toolbar-separate-line"></div>
          <OverlayTrigger
            trigger={['hover', 'focus']}
            placement="left"
            show={showConnectPopover}
            overlay={
              <Popover
                className="connect-popover"
                onMouseEnter={handleOnMouseEnter}
                onMouseLeave={handleOnMouseLeave}>
                <div className="connect-popover-box">
                  <span className="phone-number">
                    {CONNECT_PHONE_DATA.PHONE_NUMBER}
                  </span>
                  <p className="connect-time">
                    {CONNECT_PHONE_DATA.CONNECT_TIME}
                  </p>
                </div>
              </Popover>
            }>
            <div
              className={classnames({
                'side-toolbar-link': true,
                'connect-link': true,
                active: showConnectPopover,
              })}
              onMouseEnter={handleOnMouseEnter}
              onMouseLeave={handleOnMouseLeave}>
              {connectSvg}
              <span className="side-toolbar-text">
                {phoneConnectText || '联系'}
              </span>
            </div>
          </OverlayTrigger>
        </>
      )}
    </Card>
  );
};

ConnectSideToolbar.defaultProps = {
  consultSvg: <ConsultSvg />,
  connectSvg: <ConnectSvg />,
  hidePhoneConnect: false,
};

ConnectSideToolbar.propTypes = {
  className: PropTypes.string,
  hidePhoneConnect: PropTypes.bool,
  businessConsultText: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  phoneConnectText: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  consultSvg: PropTypes.element,
  connectSvg: PropTypes.element,
};

export default ConnectSideToolbar;
