.connect-side-toolbar {
  position: fixed;
  right: 28px;
  bottom: 43px;
  z-index: 700;
  align-items: center;
  padding: 3px;
  background: #fff;
  border: 1px solid #eaedf3;
  border-radius: 28px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 20%);

  .side-toolbar-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    row-gap: 4px;

    svg {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
    }
  }

  .consult-link {
    padding: 13px 11px 4px;

    &:hover {
      background-color: #6758e51a;
      border-radius: 28px 28px 0 0;
    }
  }

  .connect-link {
    padding: 4px 11px 13px;

    &.active {
      background-color: #6758e51a;
      border-radius: 0 0 28px 28px;
    }
  }

  .side-toolbar-text {
    max-width: 28px;
    color: @dark-title-color;
    font-size: 14px;
    line-height: 20px;
  }

  .side-toolbar-separate-line {
    width: 80%;
    height: 1px;
    margin: 2px 0;
    background-color: #eaedf3;
  }

  &.hide-phone-connect {
    .side-toolbar-link {
      padding: 31px 11px 30px;
      row-gap: 10px;
    }

    .consult-link {
      &:hover {
        background-color: unset;
        border-radius: unset;
      }
    }
  }
}

.connect-popover {
  right: 10px !important;
  padding: 20px 30px;
  border: 1px solid #d8dce6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 12%);

  .phone-number {
    margin-bottom: 10px;
    color: @primary-color;
    font-weight: 500;
    font-size: 24px;
    line-height: 34px;
    letter-spacing: 0.667px;
  }

  .connect-time {
    color: #9ea0a5;
    font-size: 16px;
    line-height: 22px;
    text-align: justify;
    opacity: 0.9;
  }
}

@media (max-width: @small-screen-size) {
  .connect-side-toolbar {
    .side-toolbar-link {
      svg {
        width: 20px;
        height: 20px;
      }
    }

    .consult-link {
      padding: 10px 9px 4px;
    }

    .connect-link {
      padding: 4px 9px 10px;
    }

    .side-toolbar-text {
      max-width: 24px;
      font-size: 12px;
      line-height: 14px;
    }

    &.hide-phone-connect {
      .side-toolbar-link {
        padding: 22px 9px;
      }
    }
  }

  .connect-popover {
    right: 5px !important;
    padding: 15px 20px;

    .phone-number {
      font-size: 20px;
      line-height: 28px;
    }

    .connect-time {
      font-size: 14px;
      line-height: 18px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .connect-side-toolbar {
    right: 20px;
    bottom: 50px;

    .consult-link {
      padding: 8px 4px 4px;
    }

    .connect-link {
      padding: 4px 4px 8px;
    }

    &.hide-phone-connect {
      .side-toolbar-link {
        row-gap: 4px;
        padding: 17px 4px;
      }
    }
  }

  .connect-popover {
    right: 3px !important;
    padding: 10px 15px;

    .phone-number {
      font-size: 16px;
      line-height: 22px;
    }

    .connect-time {
      font-size: 12px;
      line-height: 14px;
    }
  }
}
