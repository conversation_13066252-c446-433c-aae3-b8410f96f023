.connect-wrapper {
  padding: 120px 0 90px;

  .connect-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .connect-logo {
    display: none;
  }

  .connect-title {
    color: #fff;
    font-size: 36px;
    line-height: 50px;
    letter-spacing: 2px;
    white-space: pre;
    text-align: center;
  }

  .connect-br {
    display: none;
  }

  .connect-subtitle {
    margin: 12px 0 90px;
    color: #fff;
    font-size: 24px;
    line-height: 34px;
    text-align: center;
    opacity: 0.9;
  }

  .connect-button-box {
    display: flex;
    column-gap: 30px;
  }

  .btn-personal,
  .btn-enterprise {
    color: inherit;
    background-color: #fff;
    border: none;
    box-shadow: 0 1px 2px rgba(62, 63, 66, 38%);

    &:hover {
      color: inherit;
    }
  }
}

@media (max-width: @middle-screen-size) {
  .connect-wrapper {
    padding: 90px 0 70px;

    .connect-subtitle {
      margin: 12px 0 60px;
    }
  }
}

@media (max-width: @small-screen-size) {
  .connect-wrapper {
    padding: 80px 0 60px;

    .connect-title {
      font-size: 30px;
    }

    .connect-subtitle {
      margin-bottom: 50px;
      font-size: 20px;
      line-height: 30px;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .connect-wrapper {
    padding-top: 60px;

    .connect-logo {
      display: block;
      width: 48px;
      height: 48px;
      margin-bottom: 20px;
    }

    .connect-title {
      font-size: 24px;
      line-height: 34px;
    }

    .connect-subtitle {
      margin-bottom: 90px;
      font-size: 18px;
    }

    .connect-button-box {
      column-gap: 20px;
    }
  }
}

@media (max-width: 400px) {
  .connect-wrapper {
    .connect-separate {
      display: none;
    }

    .connect-br {
      display: block;
    }
  }
}

@media (max-width: 375px) {
  .connect-wrapper {
    .connect-button-box {
      column-gap: 10px;
    }
  }
}
