import React from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { ScrollToElement } from '../../scroll/pageScroll/page-scroll';
import { getUrlWithProduct } from '../../../urls';
import { getEnvVariables } from '../../../utils/env';
import { getConnectHref } from '../../../utils/connect';
import { useFormatMessage } from '../../../hooks/useFormatMessageHook';
import { useCheckScreenSize } from '../../../hooks/useCheckScreenSizeHook';
import './connect-wrapper.less';

const ConnectWrapper = ({ connectData, hidePersonalButton }) => {
  const { currentProduct } = getEnvVariables();
  const { getFormatMessage } = useFormatMessage();
  const { isMiniScreenSize } = useCheckScreenSize();

  const personalHref = getUrlWithProduct(
    connectData.personalUrl?.product,
    connectData.personalUrl?.pathName,
  );
  const enterpriseHref = getUrlWithProduct(
    connectData.enterpriseUrl?.product,
    connectData.enterpriseUrl?.pathName,
  );
  const connectHref = getConnectHref({
    product: connectData.enterpriseProduct,
  });

  return (
    <div
      className="connect-wrapper"
      style={{
        backgroundImage: `${connectData.backgroundImage}`,
      }}>
      <Container className="connect-container">
        {connectData.logo && (
          <img src={connectData.logo} className="connect-logo" alt="logo" />
        )}
        <h2 className="connect-title">
          {connectData.name && (
            <>
              {getFormatMessage(connectData.name)}
              <span className="connect-separate"> - </span>
              <br className="connect-br" />
            </>
          )}
          {isMiniScreenSize && connectData.isDeleteTitleComma
            ? getFormatMessage(connectData.title).replace(/,/g, '\n')
            : getFormatMessage(connectData.title)}
        </h2>
        {connectData.subtitle && (
          <p className="connect-subtitle">
            {getFormatMessage(connectData.subtitle)}
          </p>
        )}
        <div
          className="connect-button-box"
          style={{
            color: `${connectData.fontColor}`,
          }}>
          {!hidePersonalButton && (
            <a
              href={personalHref ? personalHref : '#'}
              target={personalHref ? '_blank' : '_self'}
              rel="noreferrer"
              className="default-btn btn-personal">
              {getFormatMessage(connectData.personalText)}
            </a>
          )}
          <a
            className="default-btn btn-enterprise"
            href={enterpriseHref || connectHref}
            target="_blank"
            rel="noreferrer">
            {getFormatMessage(connectData.enterpriseText)}
          </a>
        </div>
      </Container>
    </div>
  );
};

ConnectWrapper.propTypes = {
  connectData: PropTypes.object.isRequired,
  hidePersonalButton: PropTypes.bool,
};

export default ScrollToElement(ConnectWrapper, 'connect-wrapper');
