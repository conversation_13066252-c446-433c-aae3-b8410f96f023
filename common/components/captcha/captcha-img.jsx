import React, { Component } from 'react';
import PropTypes from 'prop-types';
import classnames from 'classnames';
import './index.less';

export default class CaptchaImg extends Component {
  state = {
    timestamp: Date.now(),
    autoRefreshTimer: null,
    clickRefreshTimer: null,
    count: 0,
    errMsg: '',
  };

  componentDidMount() {
    const autoRefresh = this.props.autoRefresh;
    if (autoRefresh) {
      this.autoRefresh(autoRefresh);
    }
  }

  componentWillUnmount() {
    if (this.state.autoRefreshTimer !== null) {
      clearInterval(this.state.autoRefreshTimer);
    }
    if (this.state.clickRefreshTimer !== null) {
      clearInterval(this.state.clickRefreshTimer);
    }
  }

  autoRefresh = (time) => {
    this.state.autoRefreshTimer = setInterval(() => {
      this.refresh();
    }, time);
  };

  refresh = () => {
    this.setState({ timestamp: Date.now() });
  };

  clickRefresh = () => {
    const { autoRefresh, restrict, maxCount, errMsg, interval } = this.props;
    if (restrict) {
      if (this.state.count >= maxCount) {
        this.setState({ errMsg });
        this.state.clickRefreshTimer = setTimeout(() => {
          this.setState({ count: 0, errMsg: '' });
        }, interval);
        return;
      }
      this.state.count++;
    }
    this.refresh();
    if (autoRefresh) {
      clearInterval(this.state.autoRefreshTimer);
      this.autoRefresh(autoRefresh);
    }
  };

  generateButton = (refreshButton) => {
    return (
      <span className="refresh-btn" onClick={this.clickRefresh}>
        {refreshButton}
      </span>
    );
  };

  render() {
    const {
      captchaUrl,
      className,
      refreshButton,
      restrict,
      source,
      operateType,
    } = this.props;
    const { timestamp, errMsg } = this.state;
    let captchaTimeUrl = `/crm${captchaUrl}?t=${timestamp}`;
    if (source > -2 && operateType) {
      captchaTimeUrl += `&source=${source}&operate_type=${operateType}`;
    }
    const captchaClass = classnames({
      'pdow-captcha': true,
      [className]: className,
    });
    return (
      <div className={captchaClass}>
        <img className="captcha-img" src={captchaTimeUrl} />
        {restrict && errMsg ? (
          <span className="error-message">{errMsg}</span>
        ) : (
          this.generateButton(refreshButton)
        )}
      </div>
    );
  }
}

CaptchaImg.propTypes = {
  captchaUrl: PropTypes.string.isRequired,
  className: PropTypes.string,
  refreshButton: PropTypes.any,
  autoRefresh: PropTypes.number,
  restrict: PropTypes.bool,
  maxCount: PropTypes.number,
  interval: PropTypes.number,
  errMsg: PropTypes.string,
  source: PropTypes.number,
  operateType: PropTypes.number,
};

CaptchaImg.defaultProps = {
  refreshButton: '刷新',
  restrict: false,
  maxCount: 5,
  interval: 60000,
  errMsg: '刷新过于频繁，请稍后再试',
};
