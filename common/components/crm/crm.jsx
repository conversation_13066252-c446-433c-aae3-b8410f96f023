import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import '@paoding/customer-service/plugin-dist/style.css';
import CustomerService from '@paoding/customer-service';

const Crm = ({ currentProduct }) => {
  useEffect(() => {
    const customerService = new CustomerService({
      target: document.getElementById('customer-service'),
      props: {
        loginUrl: null,
        apiPrefix: 'crm',
        currentProduct,
      },
    });
  }, [currentProduct]);
  return <div id="customer-service"></div>;
};

Crm.propTypes = {
  currentProduct: PropTypes.string.isRequired,
};

export default Crm;
