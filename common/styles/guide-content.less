.guide-content {
  min-height: calc(100vh - 196px);
  padding: 40px 0 100px 40px;

  @media (max-width: @small-screen-size) {
    padding: 40px 0 0;
  }

  h3,
  h4 {
    padding-bottom: 30px;
    color: @dark-color;
    font-weight: 500;
    font-size: 16px;
    line-height: 28px;
  }

  h4 {
    font-size: 15px;
    line-height: 24px;
  }

  p {
    padding-bottom: 30px;
    color: @text-color;
    font-size: 16px;
    line-height: 35px;
  }

  > ol {
    padding-left: 17px;
    list-style: decimal;

    li {
      margin: 10px 0;
      color: @text-color;
    }
  }

  .charge-page-body {
    .charge-desc {
      padding-left: 16px !important;
      color: #1665d8;

      li {
        list-style-type: circle;
      }

      p {
        margin: 0;
        padding: 0 !important;
        color: @text-color;
        font-size: 16px;
        line-height: 30px;
      }
    }
  }

  .sub-title {
    display: flex;
    padding: 30px 0 20px;

    span {
      padding-left: 20px;
      color: @dark-color;
      font-size: 20px;
    }
  }

  .price-table {
    border: 1px solid #eaedf3;

    thead {
      background-color: #fbfbfd;

      th {
        color: @text-color;
        font-weight: normal;
        font-size: 12px;
        border-bottom: none;

        &:first-child {
          text-align: left;
          text-indent: 10px;
        }
      }
    }

    .td-content {
      display: flex;
      align-items: center;
      color: @text-color;
      font-size: 14px;
      line-height: 30px;

      img {
        width: 20px;
        margin: 0 10px;

        &.export-option {
          width: 18px;
          height: 18px;
          margin: 0 0 0 5px;
        }
      }

      svg {
        margin: 0 5px;
      }
    }
  }
}
