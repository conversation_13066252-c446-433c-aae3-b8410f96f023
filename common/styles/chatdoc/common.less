:root {
  --header-height: 80px;
}

.container {
  padding-right: 0;
  padding-left: 0;
}

.common-title {
  margin-bottom: 30px;
  color: @dark-title-color;
  font-weight: 600;
  font-size: 36px;
  font-family: Poppins-Medium;
  line-height: 40px;
  letter-spacing: 2px;
  text-align: center;
}

.common-subtitle {
  margin-bottom: 60px;
  color: @dark-text-color;
  font-size: 20px;
  line-height: 36px;
  text-align: center;
}

.suggest-browser-tip {
  .tip-content {
    font-size: 16px !important;
    line-height: 20px !important;
  }
}

@media (min-width: @max-screen-size) {
  .container {
    max-width: 1460px !important;
  }
}

@media (max-width: @mini-screen-size) {
  :root {
    --header-height: 65px;
  }

  .container {
    padding-right: 20px;
    padding-left: 20px;
  }

  .common-title {
    margin-bottom: 10px;
    font-size: 24px;
    line-height: 36px;
  }

  .common-subtitle {
    margin-bottom: 30px;
    font-size: 14px;
  }

  .suggest-browser-tip {
    .tip-content {
      font-size: 12px !important;
      line-height: 16px !important;
    }
  }
}
