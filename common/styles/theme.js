module.exports = {
  'least-screen-size': '500px', // phone
  'mini-screen-size': '768px', // phone
  'small-screen-size': '992px', // pad
  'middle-screen-size': '1200px', // pc
  'large-screen-size': '1450px', // large screen
  'max-screen-size': '1600px', // large screen
  'primary-color': '#6758e5', // 主色
  'dark-color': '#3e3f42', // 标题颜色
  'text-color': '#6b6c6f', // 文字颜色
  'dark-text-color': '#3d434e', // 深色文字颜色
  'dark-title-color': '#142132', // 深色标题颜色
  'content-background-color': 'rgba(103, 88, 229, 0.1)', // 内容背景色
  'navbar-collapse-bg': 'rgba(19, 24, 32, 0.6)', //header折叠时collapse背景色
  'box-shadow-base':
    '0px 2px 6px rgba(0, 0, 0, 0.08),0px 1px 3px rgba(0, 0, 0, 0.04)', // 阴影
  'border-radius-base': '4px', // 圆角
  'border-color-base': '#dedede', // 边框色
  'border-base': '1px solid #dedede', // 边框
  'dashed-border-base': '1px dashed #dedede', // 虚线边框
  'border-radius-feature': '8px', // AI开放平台标签页圆角
  'border-feature-disactive': ' 1px solid #c4c4c4', // AI开放平台未激活标签页边框
};
