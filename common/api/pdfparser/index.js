import http from './http';
import { UID_KEY } from '../../utils/pdfparser/uid';

export const getConfig = () => http.get(`/config?t=${Date.now()}`);

export const getDocListApi = () => http.get(`/documents/samples`);

export const ossLinkApi = (data) =>
  http.post('/documents/oss-link', data, {
    headers: {
      [UID_KEY]: localStorage.getItem(UID_KEY),
    },
  });

export const ossUploadApi = (data) =>
  http.post('/documents/oss-upload', data, {
    headers: {
      [UID_KEY]: localStorage.getItem(UID_KEY),
    },
  });
