import http, { baseURL, buildFullURL } from './http';
import { dataEncrypt } from './encrypt';
import { hasGetConfig } from './http';
import { getConfig } from './index';

export const updateActivityStatisticApi = async (
  data,
  params,
  useBeacon = false,
) => {
  if (!hasGetConfig) {
    await getConfig();
  }
  if (useBeacon) {
    const url = `${baseURL}/statistics`;
    const completeUrl = `${window.location.origin}${url}`;
    const fetchUrl = buildFullURL(completeUrl, params);

    const { data: encryptData, key } = dataEncrypt(data);
    const body = key ? encryptData : JSON.stringify(data);

    if (window.navigator.sendBeacon) {
      const res = window.navigator.sendBeacon(fetchUrl, body);
      if (data.duration) {
        window.localStorage.setItem(
          'activity_statistic_officialweb',
          JSON.stringify({ result: res, ...data }),
        );
      }
      return res;
    } else {
      return http.post(`/statistics`, data, { params });
    }
  } else {
    return http.post(`/statistics`, data, { params });
  }
};
