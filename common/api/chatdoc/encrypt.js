import Crypt from 'cryptjs';
import base64js from 'base64-js';

const SECRET_KEY = '0b168d3bb0828b5f6242cb3a9f144a23';

let binaryKey = '';
export function fromXBinaryKey(base64BinaryKey) {
  const encryptBinaryKey = base64js.toByteArray(base64BinaryKey);
  const keyEncrypt = new Crypt(SECRET_KEY);
  binaryKey = keyEncrypt.decrypt(encryptBinaryKey);
  return new Crypt(binaryKey);
}

export function dataEncrypt(data) {
  // const xKeyEncrypt = new Crypt(SECRET_KEY);
  // console.log(xKeyEncrypt);
  // const binaryKey = xKeyEncrypt.decrypt(
  //   new Uint8Array(
  //     base64js.toByteArray(sessionStorage.getItem('x-binary-key')),
  //   ),
  // );
  if (binaryKey) {
    const secretKey = new Crypt(binaryKey);
    data = secretKey.encryptJson(data);
  }

  return {
    key: binaryKey,
    data,
  };
}
