import http from './http';

export const postConnectBusiness = (params) =>
  http.post('/cooperation/contact-us', params);

export const postAcademicCooperation = (params) =>
  http.post('/cooperation/academic', params);

export const postExploreBusiness = (params) =>
  http.post('/cooperation/explore', params);

export const postObtainMaterials = (params) =>
  http.post('/cooperation/obtain-materials', params);

export const postAccessRequests = (params) =>
  http.post('/cooperation/access-requests', params, {
    headers: { 'X-PAI-ACCEPT-LANGUAGE': 'en' },
  });

export const getStudentsVerificationCode = (params) =>
  http.post('/email/verify-code', params);

export const sendRedeemCode = (params) => http.post('/redeem-code', params);

export const fetchBlogArticles = (params) =>
  http.get(`/blog/articles`, { params });

export const fetchBlogArticleCategory = (params) =>
  http.get(`/blog/article/categories`, { params });

export const fetchWxBlogArticles = (params) =>
  http.get(`/blog/wx/articles`, { params });

export const fetchWxArticlesCategories = (params) =>
  http.get(`/blog/wx/articles/categories`);

export const fetchWxArticlesById = (id) => http.get(`/blog/wx/articles/${id}`);
