import axios from 'axios';
export const baseURL = '/crm/api/v1';

const http = axios.create({
  baseURL,
  timeout: 60e3,
});

http.interceptors.request.use(
  (config) => {
    return config;
  },
  (err) => {
    console.log(err);
  },
);

http.interceptors.response.use(
  function resolve(result) {
    const { data } = result;
    if (result.status === 200) {
      return data;
    }
    return Promise.reject({
      message: result.detail || '获取数据失败',
      status: result.status,
    });
  },
  function reject(error) {
    const { response } = error;
    if (!response) {
      return Promise.reject({ type: 'http', message: '未知错误' });
    }

    return Promise.reject({
      type: 'http',
      message: response.data.detail || '未知错误',
    });
  },
);

export default http;
