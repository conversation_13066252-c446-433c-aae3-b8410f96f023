import axios from 'axios';
import { splitPdf, loadPdf } from './pdf';

export const validateFile = (file, uploadAccept) => {
  const ext = file.name.split('.').at(-1).toLowerCase();
  if (!uploadAccept.includes(ext)) {
    throw new Error(`'${ext}' file format is not supported.`);
  }
};

export const validatePdfFile = async (file, maxPages = 1) => {
  if (!file) {
    return;
  }
  const pdfDoc = await loadPdf(file);
  const pageCount = pdfDoc.getPageCount();

  if (pageCount === 0) {
    throw new Error('Your file has no pages.');
  }

  for (let i = 0; i < pageCount && i < 100; i++) {
    const page = pdfDoc.getPage(i);
    const { width, height } = page.getSize();
    if (width > 4096 || height > 4096 || height / width < 0.3) {
      throw new Error('Token limit exceeded.');
    }
  }

  if (pageCount > maxPages) {
    file = await splitPdf(file, maxPages);
  }

  return {
    file,
    pageCount,
  };
};

export const uploadFileToOSS = async (
  url,
  file,
  fields,
  signal = null,
  progressHandler = () => {},
) => {
  const formData = new FormData();
  // 字段顺序不可变
  formData.append('key', fields.key);
  formData.append('policy', fields.policy);
  formData.append('x-amz-algorithm', fields['x-amz-algorithm']);
  formData.append('x-amz-credential', fields['x-amz-credential']);
  formData.append('x-amz-date', fields['x-amz-date']);
  formData.append('x-amz-signature', fields['x-amz-signature']);
  formData.append('file', file);
  let retry = 5;
  while (retry > 0) {
    try {
      await axios.post(url, formData, {
        signal,
        onUploadProgress: progressHandler,
      });
      break;
    } finally {
      retry -= 1;
    }
  }
};
