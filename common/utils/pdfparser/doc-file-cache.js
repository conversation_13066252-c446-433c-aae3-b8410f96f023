import { UAParser } from 'ua-parser-js';

let CacheService;

const loadCacheService = async () => {
  if (!CacheService) {
    const PdfViewer = await import('pdf-document-viewer');
    CacheService = PdfViewer.CacheService;
  }
};

if (typeof window !== 'undefined') {
  loadCacheService();
}

export const setFileCache = async (file, byteData) => {
  const ua = new UAParser(window.navigator.userAgent);
  const OSInfo = ua.getOS();
  if (OSInfo.name === 'IOS' || OSInfo.name === 'Android') {
    return;
  }
  // TODO, the chunk record data by directly cache File object is wrong.
  return CacheService.cacheFile(file, byteData);
};

export const removeFileCache = (id) => {
  return CacheService.deleteFileCache(id);
};

export const getFileCache = (id) => {
  return CacheService.getFileCache(id);
};

export const getChunksCache = (id) => {
  return CacheService.getChunksCache(id);
};

export const clearCache = () => {
  return CacheService.clear(function (record) {
    return !record.anonymous;
  });
};

export const removeCache = (id) => {
  return CacheService.deleteFileCache(id);
};
