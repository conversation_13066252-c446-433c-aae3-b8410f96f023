export function pageScroll(path) {
  if (path === '/') return;
  const ele = document.getElementById(path);
  ele.scrollIntoView({ behavior: 'smooth' });
}

export const isMac = () => {
  return (
    typeof navigator !== 'undefined' &&
    /macintosh|mac os x/i.test(navigator.userAgent)
  );
};

export const isWindows = () => {
  return (
    typeof navigator !== 'undefined' && /windows/i.test(navigator.userAgent)
  );
};

export const getQueryValue = (key) => {
  return new URLSearchParams(document.location.search).get(key);
};

/**
 * 轮询
 * @param callback {() => Promise<any>}
 * @param time {number}
 * @param config {{ leading?: boolean, queue?: boolean }}
 * @returns {(function(): void)|*} 关闭轮询
 */
export const polling = (callback, time, config = {}) => {
  let closed = false;
  let timer = null;
  config = {
    leading: config.leading ?? true,
    queue: config.queue ?? true,
  };

  const tick = async () => {
    if (closed) {
      return;
    }
    try {
      if (config.leading || timer) {
        if (config.queue) {
          await callback();
        } else {
          callback().catch(console.error);
        }
      }
    } catch (e) {
      console.error(e);
    } finally {
      timer = setTimeout(() => {
        tick();
      }, time);
    }
  };

  tick();

  return function close() {
    closed = true;
  };
};

export const formatPrice = (price = 0) => {
  return Number(price)
    .toFixed(2)
    .replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
};

export const arrayBufferToFile = (arrayBuffer, fileName, mimeType) => {
  const blob = new Blob([arrayBuffer], { type: mimeType });

  return new File([blob], fileName, { type: mimeType });
};

export const fileToArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);

    reader.onload = () => resolve(reader.result);
    reader.onerror = () => reject(reader.error);
  });
};

export const openWindow = (url, openInNewTab = true) => {
  if (!url) return;
  const element = document.createElement('a');
  element.setAttribute('href', url);
  if (openInNewTab) {
    element.setAttribute('target', '_blank');
    element.setAttribute('rel', 'noopener noreferrer');
  }
  element.click();
};

export const listToTree = (
  list = [],
  pid = -1,
  id = 'id',
  parentId = 'parentId',
  children = 'children',
) => {
  const tree = [];
  const map = {};
  list.forEach((item) => {
    map[item[id]] = { ...item, [children]: [] };
  });
  list.forEach((item) => {
    if (item[parentId] !== pid) {
      map[item[parentId]][children].push(map[item[id]]);
    } else {
      tree.push(map[item[id]]);
    }
  });
  return tree;
};

export const findFirstTreeItem = (tree = []) => {
  const stack = [...tree];
  while (stack.length > 0) {
    const currentItem = stack.shift();

    if (currentItem.children && currentItem.children.length > 0) {
      stack.unshift(...currentItem.children);
    } else {
      return currentItem;
    }
  }
  return null;
};

export const cleanTreeData = (
  tree = [],
  key = 'children',
  children = 'children',
) => {
  const stack = [...tree];
  while (stack.length > 0) {
    const node = stack.shift();
    if (node[key]?.length === 0) {
      delete node[key];
    }
    if (node[children]?.length > 0) {
      stack.unshift(...node[children]);
    }
  }
  return tree;
};

export const findTreeItemParentIds = (tree = [], id) => {
  const stack = [];
  stack.push({ children: tree, path: [] });

  while (stack.length > 0) {
    const { children, path: currentPath } = stack.pop();
    for (let child of children) {
      const newPath = [...currentPath, child.id];
      if (child.id === id) {
        return newPath;
      }
      if (child.children?.length > 0) {
        stack.push({ children: child.children, path: newPath });
      }
    }
  }

  return [];
};

export const addCustomPropertyToTree = (
  tree = [],
  key,
  children = 'children',
  valueGenerator,
) => {
  const stack = [...tree];

  while (stack.length > 0) {
    const item = stack.shift();

    item[key] = valueGenerator(item);

    if (item[children]?.length > 0) {
      for (let i = item[children].length - 1; i >= 0; i--) {
        stack.unshift(item[children][i]);
      }
    }
  }
};
