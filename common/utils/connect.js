import { getUrlWithProduct } from '../urls';
import { getEnvVariables } from './env';

export const getConnectHref = (props) => {
  const href = getUrlWithProduct('paodingai', 'connect');
  const source = props?.source || getEnvVariables().currentProduct;
  const product = props?.product || source;

  if (source) {
    return `${href}?product=${encodeURIComponent(
      product,
    )}&source=${encodeURIComponent(source)}`;
  }
  return href;
};
