import { encode, decode } from 'js-base64';

export function setCookie(name, value, hours, path) {
  var exp = new Date();
  exp.setTime(exp.getTime() + hours * 60 * 60 * 1000);
  if (!path) {
    path = '/';
  }
  document.cookie =
    name +
    '=' +
    encode(value) +
    ';expires=' +
    exp.toGMTString() +
    ';path=' +
    path;
}
export function getCookie(name) {
  var arr,
    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
  if ((arr = document.cookie.match(reg))) {
    return decode(arr[2]);
  } else {
    return null;
  }
}
export function delCookie(name) {
  setCookie(name, '', -1);
}
