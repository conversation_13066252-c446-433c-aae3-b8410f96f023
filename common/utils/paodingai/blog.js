const getRecommendArticles = (allArticles, article) => {
  const filteredArticles = allArticles.filter(
    (item) => item.title !== article.title,
  );
  return filteredArticles.slice(0, 3);
};

const getBlogList = (allBlogList, blogCategory) => {
  const blogListByCategory = blogCategory.map((categoryItem) => {
    const articles = allBlogList.filter(
      (article) => article.category === categoryItem.category,
    );
    return articles.length > 0 ? articles : [];
  });
  const blogList = [allBlogList, ...blogListByCategory];
  return blogList;
};

module.exports = {
  getBlogList: getBlogList,
  getRecommendArticles: getRecommendArticles,
};
