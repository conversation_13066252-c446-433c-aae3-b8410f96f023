import aesjs from 'aes-js';
import pako from 'pako';
import base64js from 'base64-js';
import Crypt from 'cryptjs';

function makePaddingBytes(str) {
  let strBytes = str;
  if (typeof strBytes === 'string') {
    strBytes = aesjs.utils.utf8.toBytes(str);
  }
  let bytesLength = strBytes.length;
  if (strBytes.length % 16 !== 0) {
    bytesLength = strBytes.length - (strBytes.length % 16) + 16;
  }
  let bytesArray = new Uint8Array(bytesLength);
  for (let i = 0; i < bytesLength; ++i) {
    if (i < strBytes.length) {
      bytesArray[i] = strBytes[i];
    }
  }
  return bytesArray;
}

export class PackageEncrypt {
  constructor(key) {
    this.secretKey = makePaddingBytes(key.substr(0, 16));
    this.ivKey = makePaddingBytes(
      key.split('').reverse().join('').substr(0, 16),
    );
  }

  encrypt(data) {
    let strBytes = aesjs.utils.utf8.toBytes(data);
    data = pako.deflate(strBytes);
    let dataWithPaddingSize = new Uint8Array(data.length + 1);
    dataWithPaddingSize[0] = 0;
    for (let i = 1; i < dataWithPaddingSize.length; ++i) {
      dataWithPaddingSize[i] = data[i - 1];
    }
    let aes = new aesjs.ModeOfOperation.cbc(this.secretKey, this.ivKey); // eslint-disable-line
    let dataBytes = makePaddingBytes(dataWithPaddingSize);
    dataBytes[0] = aesjs.utils.utf8.toBytes(
      (dataBytes.length - dataWithPaddingSize.length).toString(16),
    )[0];
    let encryptedBytes = aes.encrypt(dataBytes);
    return encryptedBytes;
  }

  encryptJson(jsonData) {
    let data = JSON.stringify(jsonData);
    return this.encrypt(data);
  }

  decrypt(data) {
    let aes = new aesjs.ModeOfOperation.cbc(this.secretKey, this.ivKey);
    let decryptDataWithPadding = aes.decrypt(data);
    let paddingSizeStr = aesjs.utils.utf8.fromBytes(
      decryptDataWithPadding.subarray(0, 1),
    );
    let paddingSize = parseInt(paddingSizeStr, 16);
    let dataLength = decryptDataWithPadding.length;
    let decryptData = decryptDataWithPadding.subarray(
      1,
      dataLength - paddingSize,
    );
    decryptData = pako.ungzip(decryptData);
    return aesjs.utils.utf8.fromBytes(decryptData);
  }

  decryptJson(data) {
    return JSON.parse(this.decrypt(data));
  }
}

export function fromXBinaryKey(base64BinaryKey) {
  let encryptBinaryKey = base64js.toByteArray(base64BinaryKey);
  let keyEncrypt = new PackageEncrypt('0b168d3bb0828b5f6242cb3a9f144a23');
  let binaryKey = keyEncrypt.decrypt(encryptBinaryKey);
  return new PackageEncrypt(binaryKey);
}

export function bufferToUnit8Array(buffer, binary_key) {
  let dataView = new DataView(buffer);
  let ints = new Uint8Array(buffer.byteLength);
  for (let i = 0; i < ints.length; i++) {
    ints[i] = dataView.getUint8(i);
  }
  if (binary_key) {
    let packageEncrypt = new PackageEncrypt(binary_key);
    return packageEncrypt.decryptJson(ints);
  } else {
    return handshakeEncrypt.decryptJson(ints);
  }
}

export let handshakeEncrypt = new PackageEncrypt(
  '0b168d3bb0828b5f6242cb3a9f144a23',
);

export function dataEncrypt(data, key) {
  const secretKey = new PackageEncrypt(key);
  const encryptData = secretKey.encryptJson(data);
  return encryptData;
}

export function requestDataEncrypt(data) {
  const secretKey = new Crypt('hkx85vMOBSM7M7W');
  const encryptData = secretKey.encryptJson(data);
  return encryptData;
}

export function getXBinaryKey(key) {
  const keyEncrypt = new Crypt(key);
  const xKeyEncrypt = keyEncrypt.encrypt('hkx85vMOBSM7M7W');
  const xBinaryKey = base64js.fromByteArray(xKeyEncrypt);
  return xBinaryKey;
}

export function decryptResponseData(data) {
  const secretKey = new Crypt('hkx85vMOBSM7M7W');
  return secretKey.decryptJson(new Uint8Array(data));
}
