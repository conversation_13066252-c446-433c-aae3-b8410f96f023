import { updateActivityStatisticApi } from '../../api/chatdoc/activity';
import { STATISTIC_TYPE } from '../../data/chatdoc/data';

export const handleUpdateActivityStatistic = async (orgCode) => {
  if (!orgCode) {
    return;
  }
  try {
    const params = {
      statistics_type: STATISTIC_TYPE.VIEW,
    };
    const data = {
      org_code: orgCode,
    };
    await updateActivityStatisticApi(data, params);
  } catch (e) {
    console.error(e);
  }
};
