const formatTitle = (title) => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

const buildUrl = (item, list = []) => {
  let url = `${formatTitle(item.title)}/`;
  let parent = item.parents;

  while (parent) {
    const parentItem = list.find((i) => i.id === parent.id);
    if (parentItem) {
      url = formatTitle(parentItem.title) + '/' + url;
      parent = parentItem.parents;
    } else {
      break;
    }
  }

  return `/guide/${url}`;
};

module.exports = {
  buildUrl: buildUrl,
};
