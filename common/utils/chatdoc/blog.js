const { markDownToHtmlHasAnchor } = require('./markdown');

const buildDirectoryTree = (data) => {
  const tree = [];
  const nodeMap = {};
  if (data.length <= 1) {
    return data;
  }
  const minLevel = Math.min(...data.map((item) => item.level));
  for (let i = 0; i < data.length; i++) {
    const { level, title, href } = data[i];
    const node = {
      id: i + 1,
      title,
      href,
      children: [],
    };

    if (level === minLevel) {
      tree.push(node);
    } else {
      const parentLevel = level - 1;
      const parentNode = nodeMap[parentLevel];
      if (parentNode) {
        parentNode.children.push(node);
      } else {
        tree.push(node);
      }
    }

    nodeMap[level] = node;
  }
  return tree;
};

const getDirectoryTree = (content, allLevel = false) => {
  if (content) {
    const tocList = [];
    const htmlContent = markDownToHtmlHasAnchor(content, allLevel);
    const headingRegex = allLevel
      ? /<h([1-6])[^>]*>(.*?)<\/h\1>/gi
      : /<h([1-3])[^>]*>(.*?)<\/h\1>/gi;
    const matches = htmlContent.match(headingRegex);

    if (matches) {
      matches.forEach((match) => {
        const regex = allLevel
          ? /<h([1-6])[^>]*id="([^"]+)"[^>]*>(.*?)<\/h\1>/i
          : /<h([1-3])[^>]*id="([^"]+)"[^>]*>(.*?)<\/h\1>/i;
        const levelMatch = match.match(regex);

        if (levelMatch) {
          tocList.push({
            level: Number(levelMatch[1]),
            href: `#${levelMatch[2]}`,
            title: levelMatch[3],
          });
        }
      });
    }
    return buildDirectoryTree(tocList);
  }
};

const getRecommendArticles = (allArticles, article) => {
  const categoryArticles = allArticles.filter(
    (item) => item.category === article.category,
  );
  const currentArticleIndex = categoryArticles.findIndex(
    (item) => item.strapi_id === article.strapi_id,
  );
  const filteredArticles = categoryArticles.filter(
    (_, index) => index !== currentArticleIndex,
  );
  if (currentArticleIndex === 0) {
    return filteredArticles.slice(0, 3);
  } else if (currentArticleIndex >= categoryArticles.length - 2) {
    return filteredArticles.slice(-3);
  } else {
    return filteredArticles.slice(
      currentArticleIndex - 1,
      currentArticleIndex + 2,
    );
  }
};

const getBlogList = (allBlogList, blogCategory) => {
  const blogListByCategory = blogCategory.map((categoryItem) => {
    const articles = allBlogList.filter(
      (article) => article.category === categoryItem.category,
    );
    return articles.length > 0 ? articles : [];
  });
  const blogList = [allBlogList, ...blogListByCategory];
  return blogList;
};

module.exports = {
  getBlogList: getBlogList,
  getDirectoryTree: getDirectoryTree,
  getRecommendArticles: getRecommendArticles,
};
