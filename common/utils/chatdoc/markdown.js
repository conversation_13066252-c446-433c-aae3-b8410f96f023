const MarkdownIt = require('markdown-it');
const anchor = require('markdown-it-anchor').default;
const md = new MarkdownIt('default', { html: true, breaks: true });
const slugify = (s) =>
  encodeURIComponent(String(s).trim().toLowerCase().replace(/\s+/g, '-'));

const replaceLinkWithTargetBlank = (content) => {
  return content.replace(
    /(<a\s+(?:[^>]*?\s+)?href="([^"]*)"[^>]*>)(.*?)(<\/a>)/gi,
    '<a href="$2" target="_blank">$3</a>',
  );
};

const replaceLinkToVideo = (htmlContent) => {
  // 将视频类型<a>标签替换为<video>标签
  return htmlContent.replace(
    /<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1[^>]*?>.*?\.(mp4|avi|mov|wmv|flv|mkv)<\/a>/gi,
    '<video loop autoplay muted src="$2">Your browser does not support video tags</video>',
  );
};

const markDownToHtml = (content) => {
  if (content) {
    let htmlContent = md.render(content);
    htmlContent = replaceLinkToVideo(htmlContent);
    return md.render(htmlContent);
  }
  return '';
};

const markDownToHtmlHasAnchor = (content, allLevel = false) => {
  if (content) {
    const level = allLevel ? [1, 2, 3, 4, 5, 6] : [1, 2, 3];
    md.use(anchor, {
      slugify,
      tabIndex: false,
      level,
    });
    return markDownToHtml(content);
  }
};

module.exports = {
  slugify: slugify,
  markDownToHtml: markDownToHtml,
  markDownToHtmlHasAnchor: markDownToHtmlHasAnchor,
  replaceLinkWithTargetBlank: replaceLinkWithTargetBlank,
};
