import { getEnvVariables } from '../env';

const { currentProduct } = getEnvVariables();

export const getBlogPageSEO = () => ({
  title: `${currentProduct} Blog - Chat with PDF documents`,
  description: `${currentProduct} Blog,stay updated on the latest developments,tips,news,FAQ and API about ${currentProduct}.`,
  keywords: [
    `${currentProduct}`,
    'news',
    'Blog',
    'FAQ',
    'ChatPDF',
    'PDF reader',
    'GPT',
    'AI',
    'tool',
    'online',
    'tips',
    'prompt',
    'API',
  ],
});

export const getLogPageSEO = () => ({
  title: `${currentProduct} - Changelog`,
  description: `This is ${currentProduct}'s Changelog, where you can find details about product updates.`,
  keywords: [`${currentProduct}`, 'Changelog', 'update'],
});

export const getGuidePageSEO = () => ({
  title: `${currentProduct} User Guide - AI Chat with PDF`,
  description: `${currentProduct} User Center offers comprehensive guides. Upload various docs (PDF, Word, etc.) and ask questions for instant answers. Enjoy features like summarizing, extracting, analyzing. Unique functions such as formula recognition, pop-up magic, and browser extension enhance the experience. Importantly, it has a powerful verifying function to ensure the accuracy of information by tracing back to the original document. Ideal for all users.`,
  keywords: [
    currentProduct,
    `${currentProduct} PDF`,
    'ChatPaper',
    'PDF Parser',
    `${currentProduct} API`,
    'ChatPDF',
    'arXiv',
    'PDF RAG',
    'PDF AI',
  ],
});
