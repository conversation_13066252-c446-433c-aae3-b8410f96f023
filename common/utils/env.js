const { PRODUCTS_MAP } = require('../data/source');

const getEnvVariables = () => {
  const envVariables = {
    env: process.env.GATSBY_SITE_ENV,
    product: process.env.GATSBY_PRODUCT_NAME,
    currentProduct: PRODUCTS_MAP[process.env.GATSBY_PRODUCT_NAME],
    isDevEnv: process.env.NODE_ENV === 'development',
    isTestEnv: process.env.GATSBY_SITE_ENV === 'test',
    isStagingEnv: process.env.GATSBY_SITE_ENV === 'staging',
    isProdEnv: process.env.GATSBY_SITE_ENV === 'prod',
    isVipSite: process.env.GATSBY_PRODUCT_TYPE === 'vip',
    isNextSite: process.env.GATSBY_PRODUCT_TYPE === 'next',
    isVipMode:
      process.env.GATSBY_PRODUCT_TYPE === 'vip' ||
      process.env.GATSBY_PRODUCT_TYPE === 'next',
    productUrl: process.env.GATSBY_PRODUCT_URL,
    baiduId: process.env.BAIDU_ID,
    clarityId: process.env.CLARITY_ID,
    googleAdsId: process.env.GOOGLE_ADS_ID,
    googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID,
    googleTagmanagerId: process.env.GOOGLE_TAGMANAGER_ID,
    secretKey: process.env.GATSBY_SECRET_KEY,
  };

  return envVariables;
};

module.exports = { getEnvVariables: getEnvVariables };
