<svg width="111" height="110" viewBox="0 0 111 110" fill="none" xmlns="http://www.w3.org/2000/svg">
	<g filter="url(#filter0_if_0_884)">
		<rect x="3.5" y="3.5" width="104" height="103" rx="51.5" stroke="url(#paint0_linear_0_884)" />
	</g>
	<defs>
		<filter id="filter0_if_0_884" x="0" y="0" width="111" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="2" dy="2" />
			<feGaussianBlur stdDeviation="0.5" />
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
			<feColorMatrix type="matrix" values="0 0 0 0 0.553861 0 0 0 0 0.63921 0 0 0 0 1 0 0 0 1 0" />
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_884" />
			<feGaussianBlur stdDeviation="1.5" result="effect2_foregroundBlur_0_884" />
		</filter>
		<linearGradient id="paint0_linear_0_884" x1="55.5" y1="81.5537" x2="55.5" y2="106" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0" />
			<stop offset="1" stop-color="white" />
		</linearGradient>
	</defs>
</svg>
