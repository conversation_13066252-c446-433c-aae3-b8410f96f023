const ZH_PHONE_REGEXP =
  /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;

const HK_PHONE_REGEXP = /^[4-9]\d{7}$/;

const MACAO_PHONE_REGEXP = /^[68]\d{7}$/;

const TAIWAN_PHONE_REGEXP = /^0?9\d{8}$/;

const EMAIL_REGEXP = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;

const SCHOOL_EMAIL_REGEXP =
  /^([A-Za-z0-9]+[.-_])*[A-Za-z0-9]+@([A-Za-z0-9-]+.)+(edu|edu.(cn|hk)|ac.[a-z]{2,3}|.hk)$/;

const COMPANY_EMAIL_REGEXP =
  /^([\w.-]+)@(\[(\d{1,3}\.){3}|(?!qq|gmail|hotmail|icloud|outlook|163|126|sina|139)(([a-zA-Z\d-]+\.)+))([a-zA-Z]{2,4}|\d{1,3})(\]?)$/;

const PAODINGAI_EMAIL_REGEXP = /^[a-zA-Z0-9._%+-]+@paodingai\.com$/;

const TEST_EMAIL_REGEXP = new RegExp(
  `(${SCHOOL_EMAIL_REGEXP.source})|(${PAODINGAI_EMAIL_REGEXP.source})`,
);

const PASSWORD_VALIDATE_REGEXP = /^[a-zA-Z0-9._-]{6,18}$/;

const SMS_CODE_REGEXP = /^\d{6}$/;

const EMAIL_CODE_REGEXP = /^[a-zA-Z0-9]{6}$/;

const GLAZER_PHONE_REGEXP = /^1[3-9]\d{9}$/;

export const REGEXP_LIST = {
  ZH_PHONE: ZH_PHONE_REGEXP,
  HK_PHONE: HK_PHONE_REGEXP,
  MACAO_PHONE: MACAO_PHONE_REGEXP,
  TAIWAN_PHONE: TAIWAN_PHONE_REGEXP,
  AUTODOC_PHONE: ZH_PHONE_REGEXP,
  EMAIL: EMAIL_REGEXP,
  COMPANY_EMAIL: COMPANY_EMAIL_REGEXP,
  SCHOOL_EMAIL: SCHOOL_EMAIL_REGEXP,
  PAODINGAI_EMAIL: PAODINGAI_EMAIL_REGEXP,
  TEST_EMAIL: TEST_EMAIL_REGEXP,
  PASSWORD_VALIDATE: PASSWORD_VALIDATE_REGEXP,
  SMS_CODE: SMS_CODE_REGEXP,
  EMAIL_CODE: EMAIL_CODE_REGEXP,
  GLAZER_PHONE: GLAZER_PHONE_REGEXP,
};
