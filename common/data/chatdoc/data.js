import { SOURCE } from '../source';

export const GLOBAL_USERS_NUMBER = '1,000,000';

export const RECOMMEND_PROJECTS_DATA = [
  {
    name: 'Extension',
    text: 'Chat seamlessly with PDFs using our Chrome Extension',
    url: { product: 'other', pathName: 'chatdocExtensionChrome' },
  },
  {
    name: 'ChatDB',
    text: 'Revolutionize ChatDOC for Document Collections - Join ChatDB Waitlist',
    url: { product: 'chatdb', pathName: 'index' },
  },
  {
    name: 'ChatDOC PDF Parser',
    text: 'ChatDOC PDF Parser API is live—integrate advanced PDF parsing into your projects now!',
    url: { product: 'pdfparser', pathName: 'index' },
  },
];

export const SUPPORT_FORMATS_DATA = [
  'PDF',
  'DOC',
  'DOCX',
  'SCAN',
  'WEBSITE',
  'EPUB',
  'MD',
  'TXT',
];

export const MEMBERSHIP_ID = {
  FREE: 0,
  PRO: 1,
  PLUS: 2,
};

export const MEMBERSHIP_DAYS = {
  MONTHLY: 30,
  ANNUALLY: 360,
};

export const PRICING_TYPE_DATA = {
  FREE: 'Free',
  PRO: 'Pro',
  PRO_PLUS: 'Pro Plus',
};

export const PRICING_DESC_CONTENT = {
  PAGES_TEXT: 'pages',
  FILES_TEXT: 'files',
  TOKENS_TEXT: 'tokens',
  QUESTION_TEXT: 'questions',
};

export const PRICING_DESC_UNIT = {
  TOKENS_UNIT: '/doc,docx',
  SIZE_UNIT: '/file',
  FILES_UNIT: '/day',
  FILES_MON_UNIT: '/month',
  FILES_30_DAYS_UNIT: '/30 days',
  FILES_360_DAYS_UNIT: '/360 days',
  COLLECTION_UNIT: '/collection',
  FILES_TOTAL: '10 in total',
  QUESTIONS_TOTAL: '100 in total',
  FILE_SIZE_UNIT: 'MB',
};

export const PRICING_ITEM_LIST = [
  'File upload limit',
  'Question limit',
  'Page limit per file',
  'Max file size',
  'OCR page',
  'GPT-4o (paid add-on)',
  'Formula recognition',
  'File limit per collection',
  'Supported file type',
];

export const ACTIVITY_ACTIONS = {
  TRY_FOR_FREE: 'try_for_free',
};

export const PAGE_CRUMB_DATA = {
  GUIDE: { label: 'Guide', link: '/guide/' },
  BLOG: { label: 'Blog', link: '/blog/' },
  LOG: { label: 'Changelog', link: '/log/' },
};

export const STATISTIC_TYPE = {
  VIEW: 'activity_view',
  FREE: 'activity_try_free',
  WEB_DURATION: 'activity_official_web_duration',
  PRODUCT_DURATION: 'activity_product_duration',
};

export const CHATDOC_BLOG_SOURCE = {
  CHATDOC: SOURCE.CHATDOC,
  CHATPAPER: SOURCE.CHATPAPER,
  PDFCHAT: SOURCE.PDFCHAT,
  'PDF PARSER': SOURCE.PDF_PARSER,
};
