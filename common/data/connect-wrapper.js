import scriber<PERSON>ogo from '../assets/logo/scriber.png';
import glazerLogo from '../assets/logo/glazer.png';

export const PDFLUX_CONNECT_DATA = {
  name: 'PDFlux',
  title: 'PDF 数据提取神器',
  subtitle: '识别提取更方便，审核协同皆可用',
  personalText: '个人免费使用',
  personalUrl: { product: 'other', pathName: 'webtool' },
  enterpriseText: '企业版咨询',
  enterpriseProduct: 'PDFlux,PDFlux SDK',
  fontColor: '#6758e5',
  backgroundImage: 'linear-gradient(76.89deg, #0ccefe 33.2%, #b404e5 95.25%)',
};

export const PAODINGJIEWEN_CONNECT_DATA = {
  name: '庖丁解文',
  title: '让AI处理你的专属文档',
  subtitle: '基于AI与文档对话，重塑信息处理',
  personalText: '内测体验',
  personalUrl: { product: 'paoding<PERSON>ewen', pathName: 'project' },
  enterpriseText: '企业版咨询',
  enterpriseProduct: '庖丁解文',
  fontColor: '#6576db',
};

export const CALLIPER_CONNECT_DATA = {
  name: 'Calliper',
  title: '智能文档比对',
  subtitle: 'AI 智能比对 PDF / Word / 扫描件，无惧超长文档和复杂排版',
  personalText: '个人版使用',
  personalUrl: { product: 'calliper', pathName: 'project' },
  enterpriseText: '企业版咨询',
  enterpriseProduct: 'Calliper VIP',
  fontColor: '#20aaf5',
  backgroundImage:
    'linear-gradient(44.72deg, rgba(68, 129, 235, 0.2) -0.5%, rgba(4, 190, 254, 0.2) 101%), linear-gradient(224.72deg, rgba(4, 251, 250, 0.9) -1%, rgba(2, 140, 245, 0.9) 100.5%)',
};

export const SCRIBER_CONNECT_DATA = {
  logo: scriberLogo,
  title: '文档信息智能认知平台, 让机器读懂业务文档',
  isDeleteTitleComma: true,
  enterpriseText: '申请试用',
  enterpriseProduct: 'Scriber',
  fontColor: '#247FB9',
  backgroundImage:
    'linear-gradient(50deg, rgba(17, 154, 202, 0.90) 2.31%, rgba(78, 210, 255, 0.90) 89.2%)',
};

export const GLAZER_CONNECT_DATA = {
  logo: glazerLogo,
  title: '庖丁智能撰写 Glazer',
  subtitle: '精通业务流程的金融文档智能撰写平台',
  personalText: 'VIP登录',
  enterpriseText: '免费试用',
  enterpriseProduct: 'Glazer',
  fontColor: '#3a579f',
  backgroundImage:
    'linear-gradient(270deg, #44BAF7 8.33%, #4597E2 51.6%, #4376D6 92.92%)',
};
