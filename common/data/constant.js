import { SOURCE, SOURCE_MAP } from './source';

export const OFFICE_LOCATION = {
  BEIJING: 1, // 北京
  SHANGHAI: 2, // 上海
  SHENZHEN: 3, // 深圳
};
export const OFFICE_LOCATION_MAP = {
  [OFFICE_LOCATION.BEIJING]: '北京',
  [OFFICE_LOCATION.SHENZHEN]: '深圳',
  [OFFICE_LOCATION.SHANGHAI]: '上海',
};
export const OFFICE_LOCATION_ARRAY = [
  {
    label: OFFICE_LOCATION_MAP[OFFICE_LOCATION.BEIJING],
    value: OFFICE_LOCATION.BEIJING,
  },
  {
    label: OFFICE_LOCATION_MAP[OFFICE_LOCATION.SHENZHEN],
    value: OFFICE_LOCATION.SHENZHEN,
  },
  {
    label: OFFICE_LOCATION_MAP[OFFICE_LOCATION.SHANGHAI],
    value: OFFICE_LOCATION.SHANGHAI,
  },
];

export const CRM_PRODUCT_NAME = {
  AUTODOC_PERSONAL: 'AUTODOC_PERSONAL',
  AUTODOC_VIP: 'AUTODOC_VIP',
  CALLIPER_PERSONAL: 'CALLIPER_PERSONAL',
  CALLIPER_VIP: 'CALLIPER_VIP',
  GRATER: 'GRATER',
};

export const STUDENT_PRODUCTS_ARRAY = [
  {
    name: SOURCE_MAP[SOURCE.PDFLUX],
    value: SOURCE.PDFLUX,
    content: '500钻石',
    price: '50',
    desc: '精准识别和提取PDF、扫描件、图片中的表格',
    newDesc: '可进行 500 次表格提取，或中英文翻译',
  },
  {
    name: SOURCE_MAP[SOURCE.AUTODOC_PERSONAL],
    value: SOURCE.AUTODOC_PERSONAL,
    content: '2次审核次数',
    price: '198',
    desc: '智能审核金融文档中的勾稽关系冲突、质量问题、格式和错别字等（投行实习生适用）',
    newDesc: '可进行 500 次表格提取，或中英文翻译',
  },
];

export const CODE_TYPE = {
  NORMAL: 1, // 普通赠送
  EDUCATE: 2, // 教育福利
};

export const OPERATE_TYPE = {
  LOGIN: 1, //登录
  REGISTER: 2, // 注册
  FORGOT_PASSWORD: 3, // 忘记密码
  RESET_PASSWORD: 4, // 重置密码
  STUDENT: 5, // 学生福利
  BUSINESS_CONTACT: 6, // 联系我们
  ACADEMIC_COOPERATION: 7, // 学术合作
  RESEARCH_DEVELOPMENT: 8, // 探索与合作
  GET_MATERIAL: 9, // 获取资料
  APPLY_API: 10, // API申请
};

export const SCREEN_SIZE_WIDTH = {
  LEAST_SCREEN_SIZE: 500,
  MINI_SCREEN_SIZE: 768,
  SMALL_SCREEN_SIZE: 992,
  MIDDLE_SCREEN_SIZE: 1200,
  LARGE_SCREEN_SIZE: 1450,
  MAX_SCREEN_SIZE: 1600,
};

export const CASE_CUSTOMER_DATA = {
  BOC: '中国银行',
  HTSC: '华泰联合证券',
  CGS: '银河证券',
};

export const COMPANY_SIZE_OPTION = [
  {
    value: '1-20',
    label: '1-20',
  },
  {
    value: '20-50',
    label: '20-50',
  },
  {
    value: '50-100',
    label: '50-100',
  },
  {
    value: '100+',
    label: '100+',
  },
];

export const PROVIDE_FEEDBACK_OPTION = [
  {
    value: 'yes',
    label: 'Yes',
  },
  {
    value: 'no',
    label: 'No',
  },
];

export const CONNECT_PHONE_DATA = {
  PHONE_NUMBER: '010-58426539',
  CONNECT_TIME: '工作日 9:30-18:30 在线',
};

export const CUSTOMERS_NUMBER = 100;
