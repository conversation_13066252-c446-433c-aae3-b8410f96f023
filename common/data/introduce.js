import purpleWxIcon from '../assets/images/purple-wx.svg';

export const INTRODUCE_DATA = [
  {
    title: '产品',
    hasUrl: true,
    dataList: [
      {
        name: 'PDF 数据提取 PDFlux',
        url: { product: 'pdflux', pathName: 'index' },
      },
      {
        name: '文档智能工具箱 PDFlux SDK',
        url: { product: 'pdflux-sdk', pathName: 'index' },
      },
      {
        name: '智能抽取 Scriber',
        url: { product: 'scriber', pathName: 'index' },
      },
      {
        name: '智能搜索 Hunter',
        url: { product: 'hunter', pathName: 'index' },
      },
      {
        name: '金融文档智能审核 AutoDoc',
        url: { product: 'autodoc', pathName: 'index' },
      },
      {
        name: '智能比对 Calliper',
        url: { product: 'calliper', pathName: 'index' },
      },
      // 隐藏 introduce 组件中的 MetalMesh。
      // 原因：MetalMesh代码已合并到stable分支，但不需要打开MetalMesh的入口。
      // {
      //   name: '财报智能提取 MetalMesh',
      //   url: { product: 'metalmesh', pathName: 'index' },
      // },
      {
        name: '智能核查银行流水 Grater',
        url: { product: 'grater', pathName: 'index' },
      },
      {
        name: '智能撰写 Glazer',
        url: { product: 'glazer', pathName: 'index' },
      },
      {
        name: '自然语言语义理解平台 Foundry',
        url: { product: 'paodingai', pathName: 'foundry' },
      },
      {
        name: '表格智能识别',
        url: { product: 'semanmeter', pathName: 'index' },
      },
      {
        name: '文档智能问答',
        url: { product: 'paodingjiewen', pathName: 'index' },
      },
      {
        name: 'AI 开放平台',
        url: { product: 'paodingai', pathName: 'aiPlatform' },
      },
    ],
  },
  {
    title: '行业应用',
    hasUrl: true,
    dataList: [
      {
        name: '监管解决方案',
        url: { product: 'paodingai', pathName: 'supervision' },
      },
      {
        name: '银行解决方案',
        url: { product: 'paodingai', pathName: 'bank' },
      },
      {
        name: '券商解决方案',
        url: { product: 'paodingai', pathName: 'brokerage' },
      },
      {
        name: '基金解决方案',
        url: { product: 'paodingai', pathName: 'fund' },
      },
      {
        name: '其他行业解决方案',
      },
    ],
  },
  {
    title: '技术文档',
    hasUrl: true,
    dataList: [
      {
        name: '什么是文档智能',
        url: { product: 'other', pathName: 'wxIntelligentDocument' },
      },
      {
        name: '表格识别技术比较',
        url: { product: 'other', pathName: 'wxTableIdentification' },
      },
      {
        name: '利用大语言模型',
        url: {
          product: 'other',
          pathName: 'wxLargeLanguageModelsApplication',
        },
        isEllipsisText: true,
      },
      {
        name: '通过增强 PDF 结构识别',
        url: {
          product: 'other',
          pathName: 'wxEnhancedPDFStructureRecognition',
        },
        isEllipsisText: true,
      },
    ],
  },
  {
    title: '联系我们2',
    hasUrl: false,
    dataList: [
      {
        name: '电话',
        desc: '庖丁科技联系电话2',
      },
      {
        name: '邮箱2',
        desc: '庖丁科技联系邮箱',
      },
      {
        name: '微信公众号',
        icon: purpleWxIcon,
        iconName: 'wx-icon',
        isWx: true,
      },
      {
        name: '地址',
        desc: ['庖丁科技联系总部地址', '庖丁科技联系子公司地址'],
        isAddress: true,
      },
    ],
  },
];
