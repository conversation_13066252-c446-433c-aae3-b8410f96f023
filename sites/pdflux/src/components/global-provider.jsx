import React, { useState, useEffect } from 'react';
import PDFluxI18n from './i18n';
import { GlobalContext } from '../../../../common/hooks/useGlobalHook';

const GlobalProvider = ({ children }) => {
  const [lang, setLang] = useState('zh');

  useEffect(() => {
    setLang(navigator.language === 'zh-CN' ? 'zh' : 'en');
  }, []);

  const globalContextValues = {
    lang,
    setLang,
  };

  return (
    <GlobalContext.Provider value={globalContextValues}>
      <PDFluxI18n>{children}</PDFluxI18n>
    </GlobalContext.Provider>
  );
};

export default GlobalProvider;
