import http from './http';
import { dataEncrypt } from '../../../../common/utils/encrypt-package';
const encryptKey = {
  calliper: 'hkx85vMOBSM7M7W',
};
export const getWechatLoginCode = (params) =>
  http.get('/subscribe-login-qrcode?app=calliper', { params });

export const getWechatLoginStatus = (params) =>
  http.get(`/check-login`, { params });

export const getWechatLoginUserInfo = () => http.get('/user/info');

export const calliperUserInfo = () => http.get('/current_user');

export const CalliperLogin = async (data) => {
  const {
    data: { csrf_token },
  } = await http.get('/csrf_token');

  return await http.post('/login', dataEncrypt(data, encryptKey.calliper), {
    headers: { 'X-CSRF-Token': csrf_token },
  });
};

export function getConfigs() {
  return http.get('/config');
}
