import axios from 'axios';
import Crypt from 'cryptjs';
import {
  getXBinaryKey,
  requestDataEncrypt,
  decryptResponseData,
} from '../../../../common/utils/encrypt-package';

const SECRET_KEY = '9g7ugt146pz4y7pou2r9isr5map2h9o6';

export const baseURL = '/api/v1';

const requestInterceptor = (config) => {
  if (
    config.data &&
    !/login/.test(config.url) &&
    !(config.data instanceof FormData)
  ) {
    config.headers['content-type'] = 'application/json;charset=UTF-8';
    config.headers['x-binary-key'] = getXBinaryKey(SECRET_KEY);
    config.data = requestDataEncrypt(config.data);
  }
  return config;
};

const requestErrorInterceptor = (error) => Promise.reject(error);

const responseInterceptor = async (response) => {
  if (response.data.status === 'error') {
    return Promise.reject(response.data);
  }
  if (response.config.responseType === 'blob') {
    return response;
  }
  if (response.headers['x-binary-key']) {
    response.data = decryptResponseData(response.data);
  } else if (/application\/json/.test(response.headers['content-type'])) {
    let encryptData = Crypt.fromBytes(new Uint8Array(response.data));
    try {
      response.data = JSON.parse(encryptData);
    } catch (e) {
      return Promise.reject(e);
    }
  }
  return response.data;
};

const responseErrorInterceptor = async (error) => {
  if (error.response.headers['x-binary-key']) {
    error.response.data = decryptResponseData(error.response.data);
  } else if (/application\/json/.test(error.response.headers['content-type'])) {
    let encryptData = Crypt.fromBytes(new Uint8Array(error.response.data));
    try {
      error.response.data = JSON.parse(encryptData);
    } catch (e) {
      return Promise.reject(e);
    }
  }

  let errorMessage = '网络错误';
  if (error.response.data.message) {
    errorMessage = error.response.data.message;
  }

  return Promise.reject({ message: errorMessage });
};

const http = axios.create({
  baseURL,
  timeout: 120000,
  responseType: 'arraybuffer',
});
http.interceptors.request.use(requestInterceptor, requestErrorInterceptor);
http.interceptors.response.use(responseInterceptor, responseErrorInterceptor);

export default http;
