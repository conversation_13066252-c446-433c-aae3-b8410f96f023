import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Container } from 'react-bootstrap';
import { toast, Slide, ToastContainer } from 'react-toastify';
import { useMobilePageContext } from '../../../../../common/hooks/useMobilePageContext';
import bannerPng from '../../assets/images/banner.png';
import 'react-toastify/dist/ReactToastify.css';
import classnames from 'classnames';

const Banner = ({
  calliperLink,
  subtitle,
  bannerButtons,
  bannerLogin,
  isShowLogin,
}) => {
  const isMobile = useMobilePageContext();

  const copyCalliperLink = useCallback(() => {
    if (calliperLink) {
      navigator.clipboard
        .writeText(calliperLink)
        .then(() => {
          toast.success('复制成功', {
            position: 'top-center',
            autoClose: 3000,
            transition: Slide,
          });
        })
        .catch(() => {
          toast.error('复制失败，请重新尝试', {
            position: 'top-center',
            autoClose: 3000,
            transition: Slide,
          });
        });
    } else {
      toast.error('复制失败，请重新尝试', {
        position: 'top-center',
        autoClose: 3000,
        transition: Slide,
      });
    }
  }, [calliperLink]);

  return (
    <div className="banner-wrapper">
      <ToastContainer />
      <Container className="banner-container">
        <div className="banner-left">
          <h2 className="banner-title banner-title-highlight">
            Calliper 千分尺
          </h2>
          <div className="banner-line" />
          <h1 className="banner-subtitle">{subtitle}</h1>
          <p className="banner-desc">
            AI 智能比对 PDF / Word / 扫描件，差异一目了然
            <br />
            无惧超长文档和复杂排版，不漏报，少误报，更准确
          </p>
          <div
            className={classnames({
              'banner-button-box': true,
              'banner-button-box-mobile': isMobile,
              'banner-button-box-login': isShowLogin,
            })}>
            {!isMobile ? (
              <>{bannerButtons}</>
            ) : (
              <>
                <span className="banner-prompt">请在电脑端使用</span>
                <button
                  className="default-btn copy-link-btn"
                  onClick={copyCalliperLink}>
                  点击复制地址
                </button>
              </>
            )}
          </div>
        </div>
        <div
          className={classnames({
            'banner-right': true,
            'banner-right-login': isShowLogin,
          })}>
          <div className="banner-login">{bannerLogin}</div>
          <img className="banner-image" src={bannerPng} alt="banner" />
        </div>
      </Container>
    </div>
  );
};

Banner.propTypes = {
  calliperLink: PropTypes.string,
  subtitle: PropTypes.string,
  bannerButtons: PropTypes.element,
  bannerLogin: PropTypes.oneOfType([PropTypes.element, PropTypes.bool]),
  isShowLogin: PropTypes.bool,
};

export default Banner;
