import React from 'react';
import PropTypes from 'prop-types';
import DownloadClient from '../../../../../common/components/download/downloadClient';
import DownloadSvg from '../../../../../common/assets/images/download.svg';
import WebSvg from '../../../../../common/assets/icons/web.svg';
import classnames from 'classnames';

const BannerButtons = ({ setIsShowLogin, isShowLogin }) => {
  return (
    <>
      <button
        className={classnames({
          'default-btn use-btn': true,
          'login-btn-hide': isShowLogin,
        })}
        onClick={() => setIsShowLogin(true)}>
        <WebSvg /> 网页版
      </button>
      <DownloadClient
        downloadTitle={'客户端下载'}
        arrowDownloadIcon={DownloadSvg}
      />
    </>
  );
};

BannerButtons.propTypes = {
  setIsShowLogin: PropTypes.func,
  isShowLogin: PropTypes.bool,
};

export default BannerButtons;
