import React from 'react';
import PropTypes from 'prop-types';
import ConnectBtn from '../../../../../common/components/connect/connectButton/connect-btn';
import classnames from 'classnames';

const BannerButtons = ({ setIsShowLogin, isShowLogin }) => {
  return (
    <>
      <ConnectBtn buttonTitle="申请试用" className="use-btn" />
      <button
        className={classnames({
          'default-btn gradient-btn vip-login-btn': true,
          'vip-login-btn-hide': isShowLogin,
        })}
        onClick={() => setIsShowLogin(true)}>
        <span>VIP登录</span>
      </button>
    </>
  );
};

BannerButtons.propTypes = {
  setIsShowLogin: PropTypes.func,
  isShowLogin: PropTypes.bool,
};

export default BannerButtons;
