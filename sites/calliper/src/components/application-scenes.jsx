import React from 'react';
import { Container, Card } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';

const ApplicationScenes = () => {
  const {
    allApplicationScenesData: { nodes },
  } = useStaticQuery(query);

  return (
    <div className="application-scenes-wrapper">
      <Container>
        <h3 className="common-title application-scenes-title">应用场景广泛</h3>
        <div className="application-scenes-cards">
          {nodes[0].childrenApplicationScenesJson.map((item, index) => (
            <Card key={index} className="application-scenes-card">
              <div className="card-content">
                <div className="content-title-box">
                  <GatsbyImage
                    image={item.icon.childImageSharp.gatsbyImageData}
                    alt={item.title}
                    className="content-icon"
                  />
                  <p className="content-title">{item.title}</p>
                </div>
                <p className="content-desc">{item.desc}</p>
              </div>
            </Card>
          ))}
        </div>
      </Container>
    </div>
  );
};

const query = graphql`
  query {
    allApplicationScenesData: allFile(
      filter: {
        absolutePath: { regex: "application-scenes/application-scenes/" }
      }
    ) {
      nodes {
        id
        childrenApplicationScenesJson {
          title
          desc
          icon {
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
  }
`;
export default ApplicationScenes;
