import React from 'react';
import SEO from '../../../../common/components/seo/seo';

const META_LIST = [
  {
    name: 'keywords',
    content: [
      'Calliper',
      '智能',
      '比对',
      '交叉比对',
      'PDF、Word、扫描件',
      '合同比对',
      '版本比对',
      '前后比对',
      '文档比对',
      '文档差异',
      '差异比对',
      '文档智能比对',
      '纸质文档比对',
      '超长文档',
      '可视化呈现',
      '庖丁科技',
      'PDFlux',
      'PDFlux SDK',
    ],
  },
  {
    name: 'viewport',
    content:
      'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
  },
];
const CalliperSEO = () => (
  <SEO
    title="Calliper 文档内容对比神器"
    description="Calliper 文档内容对比神器，基于高精度文档结构解析，无论是Word、PDF还是图片，单栏、双栏还是艺术排版，文本段落还是表格，都提供准确的差异对比及展示"
    meta={META_LIST}
  />
);

export default CalliperSEO;
