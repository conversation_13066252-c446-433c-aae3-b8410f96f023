import React, { useState } from 'react';
import { Container, Carousel, NavDropdown } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import XGPlayerVideo from '../../../../common/components/video/xgplayer-video/xgplayer-video';
import { useMobilePageContext } from '../../../../common/hooks/useMobilePageContext';
import classnames from 'classnames';

const Advantage = () => {
  const {
    allAdvantageData: { nodes },
  } = useStaticQuery(query);

  const [currentSelectListItem, setCurrentSelectListItem] = useState(0);
  const isMobile = useMobilePageContext();

  return (
    <div
      className={classnames({
        'advantage-wrapper': true,
        'advantage-wrapper-mobile': isMobile,
      })}>
      <Container>
        <h3 className="common-title advantage-title">不漏报，少误报，更准确</h3>
      </Container>
      <Container>
        <div className="advantage-content">
          <div className="content-left">
            <ol className="content-list">
              {nodes[0].childrenAdvantageJson.map((item, index) => (
                <li
                  key={index}
                  className="list-item"
                  onClick={() => setCurrentSelectListItem(index)}>
                  <div
                    className={classnames({
                      'list-item-content': true,
                      'list-item-content-active':
                        index === currentSelectListItem,
                    })}>
                    <h6 className="list-item-title">{item.title}</h6>
                    <p className="list-item-desc">{item.desc}</p>
                  </div>
                </li>
              ))}
            </ol>
          </div>
          <div className="content-right">
            <Carousel
              interval={null}
              activeIndex={currentSelectListItem}
              controls={false}
              indicators={false}
              className="carousel-vertically">
              {nodes[0].childrenAdvantageJson.map((item, index) => (
                <Carousel.Item key={index}>
                  <XGPlayerVideo
                    className="content-video"
                    src={item.video.publicURL}
                    poster={item.poster}
                  />
                </Carousel.Item>
              ))}
            </Carousel>
          </div>
        </div>
      </Container>
      <div className="advantage-content-mobile">
        {nodes[0].childrenAdvantageJson.map((item, index) => (
          <NavDropdown
            key={index}
            title={
              <Container>
                <div className="list-item-mobile">
                  <h6 className="list-item-title">{item.title}</h6>
                  <p className="list-item-desc">{item.desc}</p>
                </div>
              </Container>
            }
            renderMenuOnMount>
            <Container>
              <XGPlayerVideo
                className="content-video"
                src={item.video.publicURL}
                poster={item.poster}
              />
            </Container>
          </NavDropdown>
        ))}
      </div>
    </div>
  );
};

const query = graphql`
  query {
    allAdvantageData: allFile(
      filter: { absolutePath: { regex: "/advantage/advantage/" } }
    ) {
      nodes {
        id
        childrenAdvantageJson {
          title
          desc
          video {
            publicURL
          }
          poster {
            name
            publicURL
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
  }
`;

export default Advantage;
