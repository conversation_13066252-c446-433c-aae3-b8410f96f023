import React from 'react';
import PropTypes from 'prop-types';
import { Nav, NavItem } from 'react-bootstrap';
import { getUrlWithProduct } from '../../../../../common/urls';
import { useMobilePageContext } from '../../../../../common/hooks/useMobilePageContext';
import { getEnvVariables } from '../../../../../common/utils/env';
import classnames from 'classnames';

const NavMenuRight = ({ isVipSite }) => {
  const isMobile = useMobilePageContext();
  const { product } = getEnvVariables();

  const logHref = getUrlWithProduct(product, 'changlog');
  const helpCenterHref = getUrlWithProduct('other', 'calliperHelpCenter');
  const calliperHref = getUrlWithProduct('calliper', 'index');
  const calliperVipHref = getUrlWithProduct('calliper-vip', 'index');

  return (
    <>
      <Nav.Link href={logHref} target="_blank" className="nav-item">
        更新日志
      </Nav.Link>
      {!isMobile && (
        <Nav.Link href={helpCenterHref} target="_blank" className="nav-item">
          帮助文档
        </Nav.Link>
      )}
      <NavItem className="version-links">
        <div className={classnames({ active: !isVipSite })}>
          <Nav.Link href={calliperHref} target="_blank">
            个人版
          </Nav.Link>
        </div>
        <div className={classnames({ active: isVipSite })}>
          <Nav.Link href={calliperVipHref} target="_blank">
            企业版
          </Nav.Link>
        </div>
      </NavItem>
    </>
  );
};

NavMenuRight.propTypes = {
  isVipSite: PropTypes.bool,
};

export default NavMenuRight;
