import React from 'react';
import PropTypes from 'prop-types';
import WXChartSvg from '../../../../common/assets/icons/wechat.svg';

const UserInfo = ({ userInfo, handleLogin }) => {
  return (
    <div className="scan-login-box">
      <h5 className="scan-login-title">
        <WXChartSvg className="login-title-logo" />
        检测到您的账号已登录
      </h5>
      <div className="user-info-wrap">
        <div className="user-personal-info">
          <div className="user-personal-avatar-wrap">
            <img
              className="user-personal-avatar-img"
              src={userInfo.avatar}
              alt=""
            />
          </div>
          <span className="user-personal-name">{userInfo.name}</span>
        </div>
      </div>
      <div className="scan-footer">
        <button className="redirect-button" onClick={() => handleLogin()}>
          <span>进入系统</span>
        </button>
      </div>
    </div>
  );
};

UserInfo.propTypes = {
  userInfo: PropTypes.object,
  handleLogin: PropTypes.func,
};

export default UserInfo;
