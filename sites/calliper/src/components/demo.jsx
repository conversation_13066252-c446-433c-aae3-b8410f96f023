import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Container, Spinner } from 'react-bootstrap';
import classnames from 'classnames';

const Demo = ({ isShowLogin, demoIframeSrc }) => {
  const iframeRef = useRef();
  const checkLoadTimer = useRef();

  const [loading, setLoading] = useState(true);

  const getIframeLoadStatus = (iframe) => {
    checkLoadTimer.current = setInterval(() => {
      if (iframe.contentDocument.readyState === 'complete') {
        clearInterval(checkLoadTimer.current);
        setLoading(false);
      }
    }, 500);
  };

  useEffect(() => {
    if (iframeRef.current) {
      getIframeLoadStatus(iframeRef.current);
    }
    return () => {
      clearInterval(checkLoadTimer.current);
    };
  }, []);

  return (
    <div
      className={classnames({
        'demo-wrapper': true,
        'demo-wrapper-login': isShowLogin,
      })}>
      <Container>
        <h3 className="common-title demo-title">差异一目了然，一网打尽</h3>
        <div className="demo-content-box">
          <div className="content-bg">
            <iframe
              title="demo"
              src={demoIframeSrc}
              ref={iframeRef}
              className="content-demo"
            />
            {loading && (
              <div className="content-demo-loading">
                <Spinner
                  animation="border"
                  role="status"
                  className="content-demo-spinner"
                />
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
};

Demo.propTypes = {
  isShowLogin: PropTypes.bool,
  demoIframeSrc: PropTypes.string,
};

export default Demo;
