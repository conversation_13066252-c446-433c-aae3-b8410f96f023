import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import SuggestBrowserTip from '../../../../common/components/suggestBrowserTip/index';
import Header from '../../../../common/components/header/header';
import NavMenuRight from './header/nav-menu-right';
import Footer from '../../../../common/components/footer/footer';
import { useHandleResize } from '../../../../common/hooks/useHandleResizeHook';
import { graphql, useStaticQuery } from 'gatsby';

const Layout = ({ children, className, isVipSite }) => {
  const query = graphql`
    query {
      allProductsData: allFile(
        filter: { absolutePath: { regex: "/common/product/products/" } }
      ) {
        nodes {
          id
          childrenProductsJson {
            subname
            headerSubname
            productList {
              name
              chineseName
              headerTitle
              title
              url {
                product
                pathName
              }
              canScanLogin
              featuredImage {
                publicURL
                childImageSharp {
                  gatsbyImageData(layout: FULL_WIDTH)
                }
              }
            }
          }
        }
      }
      allCustomerCaseData: allFile(
        filter: {
          absolutePath: { regex: "/common/customers-case/customer-case/" }
        }
      ) {
        nodes {
          id
          childrenCustomerCaseJson {
            id
            name
            type
            icon {
              childImageSharp {
                gatsbyImageData(layout: FULL_WIDTH)
              }
            }
            articleInfo {
              title
            }
          }
        }
      }
      allApplicationData: allFile(
        filter: { absolutePath: { regex: "/common/application/application/" } }
      ) {
        nodes {
          id
          childrenApplicationJson {
            id
            name
            type
            icon {
              publicURL
            }
          }
        }
      }
    }
  `;
  const data = useStaticQuery(query);
  const { allProductsData, allCustomerCaseData, allApplicationData } = data;
  const [headerAndFooterHeight, setHeaderAndFooterHeight] = useState();

  const getHeaderAndFooterHeight = () => {
    const headerOffsetHeight =
      document.querySelector('.header-wrapper').offsetHeight;
    const footerOffsetHeight =
      document.querySelector('.footer-wrapper').offsetHeight;
    setHeaderAndFooterHeight(headerOffsetHeight + footerOffsetHeight);
  };

  const mainMinHeightStyle = useMemo(
    () => ({
      minHeight: `calc(100vh - ${headerAndFooterHeight}px)`,
    }),
    [headerAndFooterHeight],
  );

  useHandleResize(() => {
    getHeaderAndFooterHeight();
  });

  return (
    <div className={className}>
      <SuggestBrowserTip />
      <Header
        showHeaderFeature
        showProductBrand
        navMenuRight={<NavMenuRight isVipSite={isVipSite} />}
        navbarExpand={'lg'}
        data={{ allProductsData, allCustomerCaseData, allApplicationData }}
      />
      <main style={mainMinHeightStyle}>{children}</main>
      <Footer hidePhone hideEmail hideWX hideCompany />
    </div>
  );
};

Layout.propTypes = {
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  className: PropTypes.string,
  isVipSite: PropTypes.bool,
};

export default Layout;
