import React from 'react';
import { Container } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';
import { getConnectHref } from '../../../../common/utils/connect';

const ApiTrial = () => {
  const {
    allApiTrialData: { nodes },
  } = useStaticQuery(query);
  const apiTrialData = nodes[0].childrenApiTrialJson[0];
  const connectHref = getConnectHref();

  return (
    <div className="api-trial-wrapper">
      <Container>
        <div className="api-trial-content">
          <div className="content-left">
            <GatsbyImage
              image={apiTrialData.image.childImageSharp.gatsbyImageData}
              className="api-trial-img"
              alt={apiTrialData.image.name}
            />
          </div>
          <div className="content-right">
            <GatsbyImage
              image={apiTrialData.icon.childImageSharp.gatsbyImageData}
              className="api-trial-icon"
              alt={apiTrialData.icon.name}
            />
            <h3 className="common-title api-trial-title">
              {apiTrialData.title}
            </h3>
            <ol className="api-trial-desc">
              {apiTrialData.descList.map((item, index) => (
                <li key={index} className="desc-item">
                  <p className="desc-text">{item}</p>
                </li>
              ))}
            </ol>
            <a
              href={connectHref}
              target="_blank"
              className="default-btn api-trial-btn"
              rel="noreferrer">
              <span className="api-trial-btn-text">申请试用</span>
            </a>
          </div>
        </div>
      </Container>
    </div>
  );
};

const query = graphql`
  query {
    allApiTrialData: allFile(
      filter: { absolutePath: { regex: "/api-trial/api-trial/" } }
    ) {
      nodes {
        id
        childrenApiTrialJson {
          title
          descList
          icon {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
          image {
            name
            childImageSharp {
              gatsbyImageData
            }
          }
        }
      }
    }
  }
`;

export default ApiTrial;
