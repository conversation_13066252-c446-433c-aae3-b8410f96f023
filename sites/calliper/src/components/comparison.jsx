import React from 'react';
import PropTypes from 'prop-types';
import { Container, OverlayTrigger, Popover } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';
import informationIcon from '../assets/images/information.svg';

const ProductAvailabilityItem = ({ comparisonProductData }) => {
  return (
    <div className="product-availability-item">
      <OverlayTrigger
        placement="bottom"
        overlay={
          <Popover className="comparison-information-popover">
            <img
              src={informationIcon}
              alt="information-icon"
              className="information-icon"
            />
            <p className="information-text">
              {comparisonProductData.featureText}
            </p>
          </Popover>
        }>
        <div className="availability-icon-box">
          <GatsbyImage
            image={
              comparisonProductData.availabilityIcon.childImageSharp
                .gatsbyImageData
            }
            alt={comparisonProductData.availabilityIcon.name}
            className="availability-icon"
          />
        </div>
      </OverlayTrigger>
    </div>
  );
};

const Comparison = () => {
  const {
    allComparisonData: { nodes },
  } = useStaticQuery(query);

  return (
    <div className="comparison-wrapper">
      <Container>
        <h3 className="common-title comparison-title">
          为什么选择 Calliper ？
        </h3>
        <div className="comparison-content">
          <div className="comparison-banner">
            <div className="banner-title">
              <h4 className="banner-title-text">性能对比</h4>
            </div>
            <div className="banner-title-product">
              <div className="banner-title-calliper">
                <h4 className="banner-title-text">Calliper千分尺</h4>
              </div>
              <div className="banner-title-other">
                <h4 className="banner-title-text">其他文档比对产品</h4>
              </div>
            </div>
          </div>
          <div className="comparison-lists">
            {nodes[0].childrenComparisonJson.map((item) => (
              <div className="comparison-list" key={item.type}>
                <div className="list-title">
                  <h6 className="list-title-text">{item.type}</h6>
                </div>
                {item.comparisonList.map((comparisonListItem) => (
                  <div
                    className="list-item"
                    key={comparisonListItem.featureName}>
                    <div className="list-type">
                      <p className="list-type-text">
                        {comparisonListItem.featureName}
                      </p>
                    </div>
                    <div className="list-product-availability">
                      <ProductAvailabilityItem
                        comparisonProductData={
                          comparisonListItem.calliperProduct
                        }
                      />
                      <ProductAvailabilityItem
                        comparisonProductData={comparisonListItem.otherProduct}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

const query = graphql`
  query {
    allComparisonData: allFile(
      filter: { absolutePath: { regex: "/comparison/comparison/" } }
    ) {
      nodes {
        id
        childrenComparisonJson {
          type
          comparisonList {
            featureName
            calliperProduct {
              availabilityIcon {
                name
                childImageSharp {
                  gatsbyImageData
                }
              }
              featureText
            }
            otherProduct {
              availabilityIcon {
                name
                childImageSharp {
                  gatsbyImageData
                }
              }
              featureText
            }
          }
        }
      }
    }
  }
`;

export default Comparison;

ProductAvailabilityItem.propTypes = {
  comparisonProductData: PropTypes.object,
};
