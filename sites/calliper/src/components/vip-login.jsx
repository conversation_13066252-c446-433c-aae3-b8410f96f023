import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Form } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { CalliperLogin } from '../api/index';
import Agreement from '../../../../common/components/agreement/agreement';

const VipLogin = ({ isLogin, userInfo, handleLogin }) => {
  const agreementRef = useRef();
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [isAgreementChecked, setIsAgreementChecked] = useState(true);

  const handleUsernameChange = (e) => {
    const value = e.target.value.trim();
    setName(value);
  };

  const handlePasswordChange = (e) => {
    const value = e.target.value.trim();
    setPassword(value);
  };

  const login = useCallback(async () => {
    if (isAgreementChecked) {
      try {
        setSubmitting(true);
        await CalliperLogin({ name, password });
        handleLogin();
      } catch (error) {
        setErrorMsg(error.message);
      } finally {
        setName('');
        setPassword('');
        setSubmitting(false);
      }
    }
  }, [name, password, isAgreementChecked, handleLogin]);

  useEffect(() => {
    if (isLogin && userInfo) {
      handleLogin();
    }
  }, [isLogin, userInfo, handleLogin]);

  useEffect(() => {
    setErrorMsg(agreementRef.current.validateAgreementChecked());
  }, [isAgreementChecked]);

  return (
    <div className="vip-login">
      <h3 className="login-title">
        VIP账号<span>登录</span>
      </h3>
      <Form className="login-form">
        <Form.Group className="login-form-item form-item-name">
          <Form.Label className="form-label">账号</Form.Label>
          <Form.Control
            type="text"
            name="name"
            value={name}
            onChange={handleUsernameChange}
            className="form-input"
          />
        </Form.Group>
        <Form.Group className="login-form-item form-item-password">
          <Form.Label className="form-label">密码</Form.Label>
          <Form.Control
            type="password"
            name="password"
            value={password}
            onChange={handlePasswordChange}
            className="form-input"
          />
        </Form.Group>
        <Agreement
          ref={agreementRef}
          themeColor="#20aaf5"
          className="calliper-agreement"
          productName="Calliper 文档智能比对产品"
          setIsAgreementChecked={setIsAgreementChecked}
        />
        {errorMsg ? <p className="error-message">{errorMsg}</p> : null}
        <button
          type="button"
          className="form-submit"
          disabled={submitting || !name || !password}
          onClick={login}>
          {submitting ? '登录中...' : '登录'}
        </button>
      </Form>
    </div>
  );
};

VipLogin.propTypes = {
  isLogin: PropTypes.bool,
  userInfo: PropTypes.object,
  handleLogin: PropTypes.func,
};

export default VipLogin;
