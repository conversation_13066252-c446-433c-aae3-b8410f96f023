import React, { useRef, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Spinner } from 'react-bootstrap';
import Agreement from '../../../../common/components/agreement/agreement';
import UserInfo from './user-info';
import WXChartSvg from '../../../../common/assets/icons/wechat.svg';
import RefreshSvg from '../../../../common/assets/icons/refresh.svg';
import UserServiceSvg from '../assets/icons/user-service.svg';
import { USER_AGREEMENT_DATA } from '../data/agreement';
import {
  getWechatLoginUserInfo,
  getWechatLoginCode,
  getWechatLoginStatus,
} from '../api/index';
import { useConfigContext } from '../hooks/config-context';

const PersonalLogin = ({ isShowLogin, handleLogin }) => {
  const config = useConfigContext();
  const agreementRef = useRef();
  const loginStatusTimeOut = useRef();
  const checkLoginQRCodeInvalidTimeOut = useRef();
  const [isAgreementChecked, setIsAgreementChecked] = useState(true);
  const [isExpired, setIsExpired] = useState(false);
  const [weChatLoginQR, setWeChatLoginQR] = useState(null);
  const [weChatLoginState, setWeChatLoginState] = useState(null);
  const [isSystemError, setIsSystemError] = useState(false);
  const [userInfo, setUserInfo] = useState(null);

  const queryWechatLoginStatus = useCallback(
    async (params) => {
      try {
        const res = await getWechatLoginStatus(params);
        if (checkLoginQRCodeInvalidTimeOut.current) {
          clearTimeout(checkLoginQRCodeInvalidTimeOut.current);
        }
        if (res.data) {
          clearTimeout(loginStatusTimeOut.current);
          if (res.data.redirect_url) {
            window.location.href = res.data.redirect_url;
          } else {
            handleLogin();
          }
        }
      } catch (error) {
        loginStatusTimeOut.current = setTimeout(
          () => queryWechatLoginStatus(params),
          2000,
        );
      }
    },
    [handleLogin],
  );

  const getUserInfo = async () => {
    try {
      const userInfo = await getWechatLoginUserInfo();
      setUserInfo(userInfo.data);
      return userInfo;
    } catch (error) {
      return null;
    }
  };

  const checkLoginQRCodeInvalid = () => {
    checkLoginQRCodeInvalidTimeOut.current = setTimeout(() => {
      if (loginStatusTimeOut.current) {
        clearTimeout(loginStatusTimeOut.current);
      }
      setIsExpired(true);
    }, 60e3);
  };

  const checkLogin = useCallback(
    async (weChatLoginState) => {
      try {
        setWeChatLoginQR(null);
        const userInfo = await getUserInfo();
        if (userInfo) {
          return;
        }
        const weChatInfo = await getWechatLoginCode({
          state: weChatLoginState,
        });
        setIsExpired(false);
        const { qrcode, query_id } = weChatInfo.data;
        setWeChatLoginQR(qrcode);
        await queryWechatLoginStatus({
          query_id: query_id,
        });
        checkLoginQRCodeInvalid();
      } catch (error) {
        setIsSystemError(true);
      }
    },
    [queryWechatLoginStatus],
  );

  useEffect(() => {
    if (config.bondowner_state && isShowLogin) {
      checkLogin(config.bondowner_state);
      setWeChatLoginState(config.bondowner_state);
    }
  }, [config, checkLogin, isShowLogin]);

  useEffect(() => {
    return () => {
      if (loginStatusTimeOut.current) {
        clearTimeout(loginStatusTimeOut.current);
      }
      if (checkLoginQRCodeInvalidTimeOut.current) {
        clearTimeout(checkLoginQRCodeInvalidTimeOut.current);
      }
    };
  }, []);

  return (
    <div className="scan-login">
      <div className="scan-login-container">
        {userInfo ? (
          <UserInfo userInfo={userInfo} handleLogin={handleLogin} />
        ) : (
          <div className="scan-login-box">
            <h5 className="scan-login-title">
              <WXChartSvg className="login-title-logo" />
              微信扫一扫登录
            </h5>
            {!isAgreementChecked ? (
              <div>
                <UserServiceSvg className="wechat-tips-img" />
                <p>请在勾选《用户协议》后再扫码登录</p>
              </div>
            ) : weChatLoginQR ? (
              <div className="login-qr-box">
                <div className="qr-content">
                  {isExpired && (
                    <div
                      className="qr-expired"
                      onClick={() => checkLogin(weChatLoginState)}>
                      <span className="qr-info">二维码失效，点击刷新</span>
                      <RefreshSvg className="qr-svg" />
                    </div>
                  )}
                  <img className="login-qr" src={weChatLoginQR} alt="" />
                </div>
                <p className="qr-desc">
                  扫码成功后，点击“<i>关注公众号</i>”即可登录
                </p>
              </div>
            ) : isSystemError ? (
              <span className="qr-text-error">获取二维码失败</span>
            ) : (
              <div className="qr-spinner">
                <Spinner animation="grow" variant="primary" />
              </div>
            )}
            <Agreement
              ref={agreementRef}
              themeColor="#20aaf5"
              productName="Calliper 文档智能比对产品"
              className="calliper-agreement"
              agreementData={USER_AGREEMENT_DATA}
              setIsAgreementChecked={setIsAgreementChecked}
            />
          </div>
        )}
      </div>
    </div>
  );
};

PersonalLogin.propTypes = {
  isLogin: PropTypes.bool,
  userInfo: PropTypes.object,
  handleLogin: PropTypes.func,
};

export default PersonalLogin;
