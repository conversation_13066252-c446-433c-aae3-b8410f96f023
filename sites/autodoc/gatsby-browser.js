import React from 'react';
import 'bootstrap/dist/css/bootstrap.css';
import '../../common/styles/reset.less';
import { MobilePageContext } from '../../common/hooks/useMobilePageContext';

export const shouldUpdateScroll = () => {
  return false;
};

export const wrapPageElement = ({ element, props }) => {
  const { isMobilePage } = props.pageContext;

  return (
    <MobilePageContext.Provider value={{ isMobilePage }}>
      {element}
    </MobilePageContext.Provider>
  );
};
