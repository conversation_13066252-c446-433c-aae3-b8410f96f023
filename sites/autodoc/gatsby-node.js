const fs = require('fs');
const path = require('path');
const { CopyUnsupportedBrowserWebpackPlugin } = require('unsupported-browser-plugin');
const { getEnvVariables } = require('../../common/utils/env');

const { isDevEnv } = getEnvVariables();

exports.onPreExtractQueries = (context) => {
  const { store } = context;
  const { program } = store.getState();

  fs.cpSync(
    path.resolve(__dirname, '../../common/hooks'),
    path.resolve(program.directory, '.cache/fragments/'),
    { recursive: true },
  );

  fs.cpSync(
    path.resolve(__dirname, '../../common/components'),
    path.resolve(program.directory, '.cache/fragments/'),
    { recursive: true },
  );
};

exports.onCreateNode = ({ node, actions, getNode }) => {
  const { createNodeField } = actions;

  if (node.internal.type === 'ImageSharp') {
    const fileNode = getNode(node.parent);
    createNodeField({
      name: 'relativeDirectory',
      node,
      value: fileNode.relativeDirectory,
    });
  }
};

exports.onCreateWebpackConfig = ({ actions, getConfig }) => {
  const { setWebpackConfig } = actions;

  if (!isDevEnv) {
    setWebpackConfig({
      plugins: [
        new CopyUnsupportedBrowserWebpackPlugin({
          lang: 'zh',
          product: 'AutoDoc',
        }),
      ],
    });
  }
};

const createMobilePages = (createPage, originalPage, originalContext) => {
  createPage({
    ...originalPage,
    path: originalPage.path.replace(/\/+$/, '') + '/m',
    context: {
      ...originalContext,
      isMobilePage: true,
    },
  });
};

exports.onCreatePage = ({ page, actions }) => {
  const { createPage } = actions;

  // 除404.html外，创建对应页面的移动端版本
  if (page.path !== '/404.html') {
    createMobilePages(createPage, page);
  }
};
