.corrector-toggle {
  display: flex;
  align-items: center;

  .typo-prev-btn,
  .typo-next-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #1a79ff;
    border: none;
    border-radius: 6px;

    &:disabled {
      background-color: #e5e8ee;
      cursor: not-allowed;
    }
  }

  .arrow-prev,
  .arrow-next {
    overflow: unset;
  }

  .toggle-typo {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 76px;
    height: 30px;
    margin: 0 6px;
    padding: 4px 5px;
    color: #606266;
    font-size: 16px;
    line-height: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
  }

  .toggle-typo-total {
    padding: 4px 10px;
    cursor: pointer;

    .typo-total-number {
      padding: 0 5px;
      color: #ff797a;
      font-weight: 600;
    }
  }

  .typo-current-index {
    position: relative;
    max-width: 78px;
    height: 100%;
  }

  .current-index,
  .current-index-input {
    min-width: 16px;
    padding: 0 6px;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
  }

  .current-index {
    display: block;
    height: auto;
    overflow: hidden;
    color: #ff797a;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .current-index-input {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    color: transparent;
    background-color: transparent;
    border: none;
    caret-color: #606266;

    &:focus {
      box-shadow: none;
    }
  }

  .typo-total-number {
    padding: 0 6px;
  }
}
