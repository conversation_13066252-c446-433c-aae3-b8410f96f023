.corrector-text-clear {
  line-height: 1;

  .text-clear-button {
    display: flex;
    align-items: center;
    padding: 0;
    background-color: #fff;
    border: none;
  }

  .text-clear-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .text-clear-info {
    color: #1a79ff;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
  }
}

.corrector-clear-modal {
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 100%;

  .modal-dialog {
    display: flex;
    justify-content: center;
    width: 60%;
    min-width: 280px;
    margin: 0;
  }

  .modal-content {
    width: 100%;
    margin-bottom: 100px;
    padding: 30px 20px;
    border: none;
    border-radius: 8px;
  }

  .modal-body {
    margin-bottom: 24px;
    padding: 0;
    color: @dark-color;
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    text-align: center;
  }

  .modal-footer {
    flex-wrap: nowrap;
    justify-content: center;
    padding: 0;
    border: none;
  }

  .clear-cancel-btn,
  .clear-sure-btn {
    margin: 0;
    padding: 9px 43px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    border-radius: 6px;
  }

  .clear-cancel-btn {
    margin-right: 8px;
    color: @dark-color;
    background-color: #fff;
    border-color: #e4e7ed;
  }

  .clear-sure-btn {
    background-color: #1a79ff;
    border-color: #1a79ff;
  }
}
