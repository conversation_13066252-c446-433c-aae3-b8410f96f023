@media (max-width: @mini-screen-size) {
  :root {
    --autodoc-corrector-page-height: 100vh;
  }

  .autodoc-corrector-page {
    --corrector-page-padding: 30px;

    ::-webkit-scrollbar {
      width: 6px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #d3d8e2;
    }

    position: relative;
    display: flex;
    flex-direction: column;
    height: 100vh;
    height: var(--autodoc-corrector-page-height);
    background-color: #f7f9fd;

    .corrector-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .corrector-brand {
      display: flex;
      align-items: center;
    }

    .corrector-logo {
      width: 36px;
      height: 36px;
      margin-right: 9px;
    }

    .corrector-name {
      color: @dark-color;
      font-weight: 700;
      font-size: 20px;
      font-family: Helvetica;
      line-height: 28px;
    }

    .result-container {
      height: inherit;
      overflow: hidden;
    }

    .container-left {
      width: 100%;
      padding: 16px 5px 10px 16px;
    }

    .container-text {
      height: 98%;
      padding-bottom: 30px;
      scrollbar-color: #d3d8e2 transparent;

      &:empty {
        &::before {
          color: #999;
        }
      }
    }

    .container-text-top {
      position: relative;
      height: 100%;
      overflow: hidden;
    }

    .container-text-bottom {
      justify-content: flex-end;
      padding: 6px 4px;
    }

    .container-text-bottom-clear {
      justify-content: space-between;
    }

    .text-count-empty {
      color: #ff797a;
    }

    .text-submit-btn {
      width: 100%;
      height: auto;
      margin: 20px 0 4px;
      padding: 10px 0;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      border: none;
    }

    .text-submit-loading {
      position: absolute;
      bottom: 50%;
      left: calc(50% - (100px / 2));
      z-index: 9;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100px;
      padding: 17px 23px;
      color: #1e68fc;
      font-size: 14px;
      line-height: 22px;
      background-color: #f7f9fd;
      border-radius: 12px;
    }

    .text-submit-spinner {
      width: 36px;
      height: 36px;
      margin-bottom: 8px;
      border-color: #1e68fc;
      border-width: 3px;
      border-right-color: transparent;
    }

    .error-correction-item-box {
      visibility: hidden;
    }

    .error-correction-item-active {
      position: absolute;
      top: 0;
      width: fit-content;
      max-width: 90%;
      padding: 7px;
      line-height: 20px;
      background-color: #fff;
    }

    .error-correction-typo,
    .error-correction-result {
      font-size: 14px;
    }

    .error-correction-text {
      margin: 0 4px;
      font-size: 12px;
    }
  }
}

@media (max-width: @least-screen-size) {
  .autodoc-corrector-page {
    --corrector-page-padding: 16px;
  }

  .corrector-text-clear {
    .text-clear-info,
    .text-count-limit {
      font-size: 14px;
    }
  }

  .corrector-clear-modal {
    .modal-content {
      padding: 24px 16px;
    }
  }
}
