:root {
  --container-text-typo-border-color: #ff5454;
}

.autodoc-corrector-page {
  padding: var(--corrector-page-padding);
  font-family: 'PingFang SC';

  ::-webkit-scrollbar {
    width: 5px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 10px;
  }

  .result-container {
    display: flex;
    width: 100%;
    height: calc(100vh - (2 * var(--corrector-page-padding)));
    background-color: #f7f9fd;
    border-radius: 8px;
  }

  .container-left {
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(55% - 10px);
    padding: 20px 15px 20px 20px;
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 6%);
  }

  .container-right {
    width: 45%;
    padding: 30px 35px 30px 40px;
  }

  .container-text-top {
    position: relative;
    height: calc(100% - 80px);
  }

  .container-text {
    display: inline-block;
    width: 100%;
    height: 100%;
    padding: 0;
    padding-right: 5px;
    overflow-y: scroll;
    color: #606266;
    font-size: 14px;
    line-height: 30px;
    letter-spacing: 1px;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    outline: 0;
    scrollbar-width: thin;
    scrollbar-color: #aaa transparent;

    &:empty {
      &::before {
        color: #9ea0a5;
        content: '请输入需要校对的文本';
        pointer-events: none;
      }
    }

    &:focus {
      &::before {
        content: none;
      }
    }
  }

  .container-text-anthor {
    position: absolute;
    top: 0;
    pointer-events: none;
  }

  .container-text-typo {
    border-bottom: 2px solid var(--container-text-typo-border-color);
    cursor: pointer;
  }

  .container-text-typo-active {
    background-color: #bad7ff;
  }

  .container-text-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
  }

  .text-count-limit {
    color: #c0c4cc;
    font-size: 16px;
    line-height: 20px;
  }

  .text-submit-btn {
    min-width: 118px;
    height: 42px;
    padding: 0 12px;
    color: rgba(255, 255, 255, 88%);
    font-size: 16px;
    line-height: 20px;
    background-color: #1a79ff;
    border: 1px solid #1a79ff;
    border-radius: 6px;
    box-shadow: 0 2px 0 0 rgba(0, 0, 0, 2%);

    &:disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
      cursor: not-allowed;
      pointer-events: auto;
    }
  }

  .error-correction-title {
    display: flex;
    align-items: center;
  }

  .error-correction-title-text {
    margin-right: 5px;
    color: #303133;
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
  }

  .error-number-prompt {
    width: 20px;
    height: 20px;
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    background-color: #ff797a;
    border-radius: 4px;
  }

  .error-correction-content {
    height: 86%;
    margin-top: 20px;
    padding-right: 5px;
    overflow-y: scroll;
    scrollbar-width: thin;
    scrollbar-color: #aaa transparent;
  }

  .error-correction-item {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 52px;
    margin-bottom: 10px;
    padding: 16px 26px;
    background-color: #fff;
    border-radius: 4px 8px 8px 4px;
    cursor: pointer;

    &::after {
      position: absolute;
      left: 0;
      width: 4px;
      height: 100%;
      background-color: #bad7ff;
      border-radius: 8px 0 0 8px;
      content: '';
    }
  }

  .error-correction-item-active {
    border: 1px solid #1a79ff;
    border-radius: 8px;
    box-shadow: 0 1px 4px 0 rgba(26, 121, 255, 15%);

    &::after {
      content: unset;
    }
  }

  .error-correction-typo,
  .error-correction-result {
    color: #5e5e5e;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;
  }

  .error-correction-text {
    margin: 0 5px;
    color: #999;
    font-size: 14px;
    line-height: 18px;
    white-space: nowrap;
  }

  .error-correction-quote {
    color: #999;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;
  }

  .error-correction-empty-typos {
    display: block;
    margin-top: 175px;
    text-align: center;
  }

  .error-correction-text-empty {
    color: #909399;
  }

  .empty-typos-img {
    width: 31%;
    min-width: 100px;
  }
}
