@edition-width: 110px;
@feature-height: 40px;

.edition-constract {
  padding: 0 20px;

  .constract-header {
    display: flex;
    padding-bottom: 6px;
    color: #303133;
    font-weight: 500;
    font-size: 15px;
    text-align: center;
    border-bottom: 1px solid rgba(220, 228, 240, 100%);
  }

  .header-feature {
    flex: 1;
    font-weight: 600;
  }

  .header-edition {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
    width: @edition-width;
    color: #2d6eec;

    > .enterprise-badge {
      display: block;
      width: 15px;
      height: 15px;
      background: url('../assets/images/vip-badge.svg') no-repeat;
      background-size: contain;
    }
  }

  .constract-content {
    display: flex;
    gap: 10px;
  }

  .content-features {
    flex: 1;
  }

  .feature-box {
    display: flex;
    align-items: center;
    height: @feature-height;
    line-height: @feature-height;

    &.has-sub {
      height: auto;
    }

    &:not(:last-of-type) {
      border-bottom: 1px solid rgba(220, 228, 240, 100%);
    }
  }

  .feature-sub {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  .sub-box {
    height: @feature-height;

    &:not(:last-of-type) {
      border-bottom: 1px solid rgba(220, 228, 240, 100%);
    }

    &:last-of-type {
      height: @feature-height - 1;
    }
  }

  .feature-name {
    width: 80px;
    color: #303133;
    font-weight: 600;
    font-size: 13px;
  }

  .feature-desc,
  .sub-desc {
    color: #b9bdc6;
    font-weight: 400;
    font-size: 12px;
  }

  .sub-name {
    color: #303133;
    font-weight: 500;
    font-size: 13px;
  }

  .support-features {
    width: @edition-width;
  }

  .support-features-box {
    display: flex;
    align-items: center;
    justify-content: center;
    height: @feature-height;
    line-height: @feature-height;

    &:not(:last-of-type) {
      border-bottom: 1px solid rgba(220, 228, 240, 100%);
    }
  }

  .support-feature-check {
    display: block;
    width: 20px;
    height: 20px;
    background: url('../assets/images/check.svg') no-repeat;
    background-size: contain;
  }
}
