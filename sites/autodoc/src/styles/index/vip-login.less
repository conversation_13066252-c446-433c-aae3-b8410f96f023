.vip-login {
  .vip-login-container {
    position: relative;
    z-index: 1;
    width: 400px;
    padding: 14px;
    text-align: center;
    background: #fff;
    border: 1px solid #eaedf3;
    border-radius: 10px;
    box-shadow: 0 4px 14px 0 rgb(63 92 185 / 10%);

    .nav-line-active {
      position: absolute;
      top: 53px;
      left: 70px;
      z-index: 2;
      width: 109px;
      height: 2px;
      background-color: #409eff;
      transition: transform 0.3s;
    }

    .nav {
      display: flex;
      justify-content: center;
      border: none;

      &::after {
        z-index: 1;
        width: 100%;
        height: 2px;
        background-color: #e4e7ed;
        content: '';
      }

      .nav-item {
        padding: 0 20px;

        .nav-link {
          width: 109px;
          padding: 0;
          color: #303133;
          font-weight: 500;
          font-size: 14px;
          line-height: 40px;
          border: none;
        }

        .active {
          color: #409eff;
          border: none;
        }
      }
    }

    .vip-tab-content {
      padding: 30px 20px 20px 30px;

      .vip-login-wrapper {
        display: flex;
        flex-flow: column;
        row-gap: 10px;

        .login-type {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          margin-bottom: 15px;
          color: #303133;
          font-size: 16px;
          column-gap: 10px;
        }

        .vip-login-form {
          > div {
            margin-bottom: 18px;

            .form-label {
              &::before {
                margin-right: 4px;
                color: #f56c6c;
                content: '*';
              }

              position: absolute;
              width: 80px;
              margin: 0;
              padding: 0 12px 0 0;
              color: #606266;
              font-size: 14px;
              line-height: 40px;
              text-align: left;
              vertical-align: middle;
            }

            .form-control,
            .form-select {
              display: inline-block;
              width: 240px;
              height: 40px;
              margin-left: 80px;
              padding: 0 30px 0 15px;
              color: #606266;
              font-size: 14px;
              line-height: 40px;
              border: 1px solid #dcdfe6;
              border-radius: @border-radius-base;
              outline: 0;

              &.is-invalid {
                background-image: none;
                border-color: #f56c6c;
              }
            }

            .invalid-feedback {
              position: absolute;
              width: 240px;
              margin: 0 0 0 80px;
              padding-top: 4px;
              color: #f56c6c;
              font-size: 12px;
              line-height: 1;
              text-align: start;
            }
          }

          .sms-code {
            display: flex;

            .form-control {
              width: 55%;
              padding-right: 15px;
            }

            .invalid-feedback {
              margin-top: 40px;
            }

            .send-sms-code {
              width: 133px;
              height: 40px;
              margin-left: 8px;
              padding: 10px;
              font-size: 14px;
              line-height: 1;
              background-color: #409eff;
              border-color: #409eff;

              &:disabled {
                font-size: 12px;
                background-color: #c8c9cc;
                border-color: #c8c9cc;
              }
            }
          }

          .error-message {
            color: #dc3545;
            user-select: none;
          }

          .user-field-content {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0;
            text-align: start;

            &.sms-code-field-content {
              justify-content: center;
            }

            .form-check {
              padding-left: 0;

              .form-check-input {
                width: 14px;
                height: 14px;
                margin: 8px 0 0;
                border: 1px solid #dcdfe6;
                border-radius: 2px;
                cursor: pointer;

                &:checked {
                  background-color: #409eff;
                  border-color: #409eff;

                  & + .form-check-label {
                    color: #409eff;
                  }
                }
              }

              .form-check-label {
                padding-left: 10px;
                font-size: 14px;
                line-height: 19px;
                cursor: pointer;
              }
            }

            .apply-trial {
              .trial-img {
                margin-right: 6px;
              }

              .connect-btn {
                width: auto;
                height: 22px;
                margin: 0;
                padding: 0;
                color: #1989fa;
                font-size: 12px;
                line-height: 22px;
                background: none;
                border-style: none;
                outline: none;
                cursor: pointer;
              }
            }
          }

          .vip-login-btn {
            width: 100%;
            margin-top: 15px;
            padding: 10px 9px;
            font-weight: 700;
            font-size: 15px;
            line-height: 1;
            letter-spacing: 5px;
            background-image: linear-gradient(269deg, #2095ff, #2d6eec);
            border: none;

            &:disabled {
              cursor: not-allowed;
              pointer-events: auto;
            }
          }

          .form-footer {
            display: flex;
            justify-content: center;
            margin-bottom: 0;
          }

          .checkbox-wrapper {
            height: 40px;
            margin: 0;

            .agreement-checkbox {
              width: 20px;
              height: 20px;
              margin: 10px 0;
              padding: 0 25px 0 0;
            }

            .text {
              margin: 10px 0;
              color: #a4a4a4;
              font-size: 13px;
            }

            .link {
              font-size: 14px;
            }
          }

          .login-mode {
            align-items: center;
            margin-left: auto;
            color: #409eff;
            cursor: pointer;

            > img {
              width: 16px;
              height: 16px;
              margin-right: 10px;
            }

            > span {
              font-size: 14px;
              line-height: 40px;
            }
          }
        }
      }
    }

    .sub-account-content {
      .vip-login-wrapper {
        .vip-login-form {
          > div {
            .form-label {
              width: 100px;
            }

            .form-control {
              width: 220px;
              margin-left: 100px;
            }

            .invalid-feedback {
              margin-left: 100px;
            }
          }

          .sms-code {
            .form-control {
              width: 45%;
            }
          }
        }
      }
    }
  }
}

.next-header-wrapper {
  .vip-login {
    .vip-login-container {
      box-shadow: 0 4px 14px 0 #3f5cb91a;
    }
  }
}
