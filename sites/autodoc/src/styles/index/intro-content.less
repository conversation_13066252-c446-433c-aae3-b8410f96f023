.intro-wrapper {
  .intro-content {
    background: #fff;
    box-shadow: inset 0 -1px 0 0 rgba(198, 198, 198, 20%);

    &:nth-of-type(odd) {
      background: #f9fbff;

      .container {
        flex-direction: row-reverse;
      }
    }

    &:nth-of-type(4),
    &:nth-of-type(5),
    &:nth-of-type(6) {
      .intro-title {
        flex-flow: column;
        align-items: flex-start;
      }
    }

    &:nth-of-type(2) {
      .intro-text {
        width: 380px;
      }
    }

    &:nth-of-type(5) {
      .intro-text {
        width: 380px;
      }
    }
  }

  .container {
    display: flex;
    flex-flow: row;
    align-items: center;
    justify-content: space-evenly;
    max-width: 1400px;
    margin: 0 auto;
    padding: 90px 0;
  }

  .intro-text {
    width: 420px;
  }

  .intro-title {
    display: flex;
    flex-flow: row;
    align-items: center;
    margin-bottom: 20px;
    column-gap: 15px;
  }

  .intro-name {
    color: #303133;
    font-weight: 500;
    font-size: 30px;
  }

  .intro-desc {
    color: #909399;
    font-weight: 500;
    font-size: 20px;
    line-height: 35px;

    b {
      margin: 0;
      color: #2d6eec;
      font-weight: 500;
      font-size: 20px;
    }
  }

  .intro-image {
    width: 800px;
  }
}
