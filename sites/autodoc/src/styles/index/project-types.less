.project-types-wrapper {
  width: 100%;
  background: #f1f5f9;

  .container {
    width: 1100px;
    margin: 0 auto;
    padding: 73px 0 63px;
    text-align: center;
  }

  .project-title {
    margin-bottom: 36px;
    color: #303133;
    font-weight: 500;
    font-size: 34px;
  }

  .project-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .project-group {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 540px;
    height: 180px;
    column-gap: 20px;
    background: rgba(255, 255, 255, 50%);
    border-radius: 18px;
    backdrop-filter: blur(15px);

    &:last-of-type {
      width: 100%;
    }
  }

  .project-type {
    display: flex;
    flex-flow: column;
    align-items: center;
    row-gap: 15px;
  }

  .type-name {
    color: #606266;
    font-size: 16px;
  }

  .type-name-other {
    display: none;
  }

  .type-items {
    display: flex;
    flex-flow: row;
    align-items: center;
    column-gap: 40px;
  }

  .type-item {
    display: flex;
    flex-flow: column;
    align-items: center;
    row-gap: 10px;
  }

  .type-item-name {
    color: #374567;
    font-size: 14px;
  }

  .type-item-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 78px;
    height: 78px;
    background: #fff;
    border-radius: 23px;
    box-shadow: 1px 1px 15px 0 rgba(29, 53, 139, 7%);

    img {
      width: 45px;
    }
  }
}
