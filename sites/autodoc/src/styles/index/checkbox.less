.checkbox-wrapper {
  display: flex;
  justify-content: center;
  padding: 0;
  color: #606266;
  font-weight: 500;
  font-size: 14px;

  .agreement-checkbox {
    position: relative;
    width: 20px;
    height: 20px;
    visibility: hidden;
    cursor: pointer;

    &::after {
      position: absolute;
      top: 3px;
      left: 0;
      display: inline-block;
      box-sizing: border-box;
      width: 1rem;
      height: 1rem;
      padding-left: 0;
      color: #fff;
      text-align: center;
      background: linear-gradient(0deg, #f6f7f9 2.43%, #fff 100%);
      border: 1px solid #d8dce6;
      border-radius: 3px;
      box-shadow: inset 0 2px 0 rgba(255, 255, 255, 5%);
      visibility: visible;
      content: ' ';
    }

    &:checked::after {
      background: #409eff
        url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3E%3C/svg%3E");
      border: 1px solid #409eff;
      content: '';
    }
  }

  .link {
    margin-left: 5px;
    color: #409eff;
    cursor: pointer;
  }
}
