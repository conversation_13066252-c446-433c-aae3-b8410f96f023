.header-wrapper {
  width: 100%;
  height: 700px;
  background-image: linear-gradient(180deg, #2c54cc, #2c86f0);

  &.next-header-wrapper {
    background-image: linear-gradient(180deg, #1171d8 26.34%, #8aceff 98.06%);
  }

  .container {
    position: relative;
    width: 1200px;
    height: 100%;
    margin: 0 auto;
    padding: 0;
  }

  .header {
    position: absolute;
    top: 0;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 14px 0 14px 25px;
    transform: translateX(-50%);
  }

  .header-logo {
    width: 145px !important;
    height: 42px !important;
  }

  .header-menu {
    display: flex;
    align-items: center;
    margin-left: auto;
    color: rgba(255, 255, 255, 80%);
    column-gap: 20px;

    .nav-link {
      padding: 0;
      color: rgba(255, 255, 255, 80%);
      font-size: 16px;
      line-height: 22px;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      cursor: pointer;
    }
  }

  .edition-links {
    display: flex;
    align-items: center;
    padding-left: 20px;
    border-left: 1px solid #95abe7;
  }

  .active-line {
    position: absolute;
    bottom: -2px;
    width: 100%;
    height: 2px;
    background-image: linear-gradient(90deg, #1e9aff, #67c6ff 99%);
    border-radius: 2px;
  }

  .edition-person,
  .edition-enterprise {
    position: relative;
    margin-right: 20px;
    padding-bottom: 2px;
    line-height: 22px;

    &.active {
      .nav-link {
        color: #fff;
      }
    }
  }

  .edition-enterprise {
    display: flex;
    align-items: center;
  }

  .enterprise-badge {
    position: absolute;
    display: block;
    width: 15px;
    height: 15px;
    margin: 0 0 -2px 52px;
    background: url('../../assets/images/vip-badge.svg') no-repeat;
    background-size: contain;
  }

  .banner-content,
  .banner-content-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .banner-intro,
  .banner-intro-mobile {
    width: 700px;
    margin-top: 100px;
  }

  .banner-intro-next {
    margin-top: 70px;
  }

  .mobile-login {
    width: 100%;
    height: auto;
    text-align: center;
    background: #fff;
    border: 1px solid #eaedf3;
    border-radius: 6px;
    box-shadow: 0 4px 14px 0 rgba(63, 92, 185, 10%);
  }

  .login-inner {
    width: 100%;
  }

  .login-title {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eaedf3;
  }

  .login-subtitle {
    margin: 10px 0;
    color: rgba(96, 98, 102, 100%);

    > span {
      color: rgba(52, 183, 69, 100%);
    }
  }

  .login-hint {
    margin: 20px 0;
    color: #2d6eec;
    font-size: 14px;
    line-height: 20px;
  }

  .connect-btn {
    width: 150px;
    height: 36px;
    margin-top: 20px;
    color: #fff;
    font-weight: 400;
    font-size: 18px;
    background-image: linear-gradient(269deg, #2095ff 0%, #2665e6 100%),
      linear-gradient(270deg, #9d93ff 0%, #615aed 100%);
    border: none;
    border-radius: 5px;
  }
}
