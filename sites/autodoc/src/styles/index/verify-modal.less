.verify-modal {
  width: 450px;
  margin-top: 15vh;

  .modal-content {
    border-radius: 2px;
  }

  .modal-header {
    padding: 20px 30px 15px;
    border: none;

    .btn-close {
      width: 12px;
      margin: 0;
      padding: 0;

      &:focus {
        box-shadow: none;
      }
    }
  }

  .modal-title {
    color: #303133;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
  }

  .modal-body {
    padding: 10px 30px 20px;
  }

  .form-tip {
    margin-bottom: 30px;
    color: #737373;
    font-size: 13px;
  }

  .verify-form {
    .form-label {
      position: absolute;
      width: 100px;
      line-height: 40px;
    }

    .input-group {
      flex-wrap: nowrap;

      .input-group-text {
        padding: 0 20px;
        color: #909399;
        background-color: #f5f7fa;
        border: 1px solid #dcdfe6;
        border-top-right-radius: 6px !important;
        border-bottom-right-radius: 6px !important;

        > span {
          display: inline-block;
          max-width: 80px;
          overflow: hidden;
          font-size: 14px;
          text-overflow: ellipsis;
        }
      }
    }

    .form-control {
      display: inline-block;
      width: 290px;
      height: 40px;
      margin-left: 100px;
      color: #606266;
      font-size: 14px;
      line-height: 40px;
      text-overflow: ellipsis;
    }

    .form-control-suffix {
      border-radius: 6px !important;
    }

    .invalid-feedback {
      position: absolute;
      top: 33px;
      left: 100px;
    }
  }

  .verify-form-number {
    position: relative;
    margin-bottom: 22px;
  }

  .verify-form-code {
    position: relative;
    display: flex;
    margin-bottom: 32px;

    .form-control {
      width: 70%;
      margin-right: 5px;
    }

    .form-label {
      &::before {
        margin-right: 4px;
        color: #f56c6c;
        content: '*';
      }
    }

    .verify-code-btn {
      width: 45%;
      font-size: 13px;
      background-color: #409eff;
      border-color: #409eff;
      border-radius: 6px;
    }
  }

  .error-message,
  .success-message {
    text-align: center;
    user-select: none;
  }

  .error-message {
    color: #dc3545;
  }

  .success-message {
    color: green;
  }

  .form-button {
    margin: 10px 0 0;
    text-align: center;

    .verify-btn {
      width: 120px;
      padding: 10px 20px;
      font-size: 13px;
      line-height: 1;
      background-color: #409eff;
      border-color: #409eff;
      border-radius: 6px;
    }
  }
}

.sms-verify-modal {
  .verify-form-phone {
    .form-label {
      &::before {
        margin-right: 4px;
        color: transparent;
        content: '*';
      }
    }
  }
}
