@media (max-width: @middle-screen-size) {
  .header-wrapper {
    height: 100%;

    .container {
      width: 100%;
      padding: 10px 20px 20px;
    }

    .header {
      position: relative;
      padding: 0;
    }

    .navbar-toggler {
      border: none;

      > svg {
        width: 25px;
      }

      &:focus {
        box-shadow: none;
      }
    }

    .header-menu {
      align-items: flex-start;
      padding-left: 20px;

      > .nav-link {
        height: 40px;
      }
    }

    .edition-links {
      display: block;
      padding: 0;
      border: none;
    }

    .edition-person {
      margin-bottom: 15px;
    }

    .banner-content-mobile {
      flex-direction: column;
      align-items: center;
    }

    .banner-intro {
      width: 50%;
      margin-top: 40px;
    }

    .banner-intro-mobile {
      width: 100%;
      margin-top: 0;
      padding: 20px;
    }
  }

  .project-types-wrapper {
    .container {
      width: 100%;
      padding: 20px;
    }

    .project-list {
      justify-content: center;
    }

    .project-group {
      width: 450px;

      &:last-of-type {
        width: 100%;
      }
    }

    .type-items {
      column-gap: 30px;
    }
  }

  .intro-wrapper {
    .container {
      width: 100%;
      padding: 20px 30px;
      column-gap: 15px;
    }

    .intro-image {
      width: 630px;
    }
  }

  #customers {
    .container {
      width: 100%;
      padding: 40px 30px 100px;
      row-gap: 30px;

      h2 {
        font-size: 28px;
      }
    }
  }

  .link-wrapper {
    height: 100%;
    padding: 0 25px;

    .link-card {
      position: relative;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 15px;
    }

    .link-title {
      font-size: 25px;
      line-height: 30px;
    }

    .link-desc {
      font-size: 16px;
    }

    .link-trial {
      margin-top: 15px;
      font-size: 14px;
      line-height: 26px;
    }
  }

  .autodoc-contrast {
    display: block !important;

    .autodoc-contrast-title {
      display: flex;
      justify-content: center;
      margin: 10px 0;
      font-weight: 500;
      font-size: 25px;
    }

    > img {
      width: 100%;
      padding: 15px 30px;
    }
  }

  .enterprise-popover {
    display: none;
  }

  .autodoc-index-page,
  .autodoc-vip-index-page {
    .autodoc-products-recommend-top {
      display: none;
    }

    .autodoc-products-recommend-bottom {
      display: block;
    }

    .autodoc-products-recommend-top,
    .autodoc-products-recommend-bottom {
      .products-list-swiper {
        .swiper-pagination-bullet-active {
          background-color: #3478f6;
        }
      }

      .recommend-title {
        display: flex;
      }

      .recommend-title-img {
        display: none;
      }
    }
  }
}

@media (max-width: @small-screen-size) {
  .header-wrapper {
    .banner-content {
      flex-direction: column;
      align-items: center;
    }

    .banner-intro {
      width: 100%;
      margin-top: 0;
      padding: 20px;
    }
  }

  .project-types-wrapper {
    .project-title {
      margin: 35px 0;
      font-size: 25px;
    }

    .project-list {
      flex-direction: column;
      margin-bottom: 20px;
    }

    .project-group {
      width: 100% !important;
      height: 100%;
      padding-bottom: 10px;
      column-gap: 0;

      > svg {
        margin: 0 15px;
      }
    }

    .type-name-other {
      display: block;
    }

    .other-middle-name {
      display: none;
    }

    .type-item {
      width: 80px;
    }

    .type-item-name {
      font-size: 13px;
    }
  }

  .intro-wrapper {
    .intro-content {
      &:nth-of-type(4),
      &:nth-of-type(5),
      &:nth-of-type(6) {
        .intro-title {
          flex-flow: inherit;
        }
      }

      &:nth-of-type(2),
      &:nth-of-type(5) {
        .intro-text {
          width: 100%;
        }
      }
    }

    .container {
      display: block;
      padding: 25px 40px;
    }

    .intro-text {
      width: 100%;
    }

    .intro-title {
      column-gap: 10px;

      img {
        width: 30px;
      }
    }

    .intro-name {
      font-size: 24px;
    }

    .intro-desc {
      font-weight: 400;
      font-size: 14px;
      line-height: 30px;

      b {
        margin: 0;
        font-size: 14px;
      }
    }

    .intro-image {
      width: 100%;
      margin-top: 15px;
    }
  }

  .link-wrapper {
    .autodoc-icon {
      width: 60px !important;
      height: 60px !important;
    }
  }
}

@media (max-width: @mini-screen-size) {
  .scan-login {
    .scan-login-container {
      width: 330px;
    }
  }

  .project-types-wrapper {
    .project-group {
      > svg {
        margin: 0 3px;
      }
    }

    .type-items {
      flex-wrap: wrap;
      justify-content: center;
      column-gap: 3px;
    }

    .type-item {
      padding-bottom: 5px;
    }

    .type-item-logo {
      width: 60px;
      height: 60px;
      border-radius: 15px;

      img {
        width: 40px;
      }
    }
  }

  .intro-wrapper {
    .intro-title {
      display: block;
      margin-bottom: 10px;
    }

    .intro-name {
      padding-left: 5px;
      font-size: 22px;
    }
  }

  .link-wrapper {
    .autodoc-icon {
      width: 40px !important;
      height: 40px !important;
      padding: 20px;
    }

    .link-title {
      font-weight: 500;
      font-size: 15px;
      line-height: 15px;
    }

    .link-desc {
      align-items: normal;
      font-size: 12px;

      img {
        width: 15px;
        height: 15px;
      }
    }
  }

  .autodoc-contrast {
    > img {
      padding: 15px;
    }
  }
}

@media (min-width: @mini-screen-size) {
  .header-wrapper {
    .edition-links {
      display: flex;
    }

    .edition-person {
      margin-bottom: 0;
    }
  }
}
