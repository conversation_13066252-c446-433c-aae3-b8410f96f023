.agreement-modal {
  width: 680px;
  max-width: none;
  margin-top: 15vh;

  .modal-content {
    border-radius: 3px;
  }

  .modal-header {
    padding: 20px 20px 10px;
  }

  .modal-title {
    color: #303133;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
  }

  .btn-close {
    width: 12px;
    margin: 0;
    padding: 0;
  }

  .modal-body {
    padding: 30px 20px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }

  .service-content {
    margin: 0 20px;
  }

  .content-title,
  .content-desc {
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 22px;
  }

  .content-title {
    font-weight: bold;
  }

  .content-desc {
    &:not(:first-child) {
      text-indent: 2em;
    }
  }

  .modal-footer {
    display: block;
    padding: 10px 20px 20px;
    padding-bottom: 10px;
    border-top: 1px solid #eaedf3;
  }

  .checkbox-wrapper {
    justify-content: flex-start;
  }

  .button-wrapper {
    display: flex;
    justify-content: center;
  }

  .agree-btn {
    padding: 12px 20px;
    line-height: 14px;
    background-color: #409eff;
    border-color: #409eff;
    border-radius: 3px;

    &:disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}
