export const FEATURES_DATA = [
  {
    key: 'loginType',
    name: '用户登录方式',
  },
  {
    key: 'chargeType',
    name: '付费方式',
  },
  {
    key: 'listReview',
    name: '列表复核',
  },
  {
    key: 'review',
    name: '全面复核',
    desc: '在原文中定位冲突，并展示判断过程与数据来源',
  },
  {
    key: 'projectType',
    name: '项目类型',
    sub: [
      {
        key: 'ipo',
        name: '主板｜科创板｜创业板｜北交所｜配股｜增发｜可转债｜新三板公转书｜债券',
      },
      {
        key: 'debt_financing',
        name: '债务融资工具',
        desc: '支持中期票据、短期融资券、超短期融资券、定向债务融资工具',
      },
      {
        key: 'abs',
        name: 'ABS 债券',
        desc: '区分不同主体进行数据复核',
      },
      {
        key: 'annual',
        name: '年报｜半年报｜新三板/北交所年报｜新三板/北交所半年报',
      },
      {
        key: 'inquiry',
        name: 'IPO 问询函',
        desc: '核查答复文件是否遗漏监管问询问题',
      },
      {
        key: 'general',
        name: '通用',
        desc: '利用通用模型对其他类型项目进行解析',
      },
    ],
  },
  {
    key: 'module',
    name: '分析模块',
    sub: [
      {
        key: 'articulation',
        name: '勾稽关系',
      },
      {
        key: 'faulty_wording',
        name: '笔误和错别字',
      },
      {
        key: 'bond_quality',
        name: '质量问题',
        desc: '根据披露要求，对债务融资工具进行审核',
      },
      {
        key: 'intable_formula',
        name: '表内复核',
        desc: '复核表内数据的勾稽关系',
      },
      {
        key: 'format_proof',
        name: '格式校对',
        desc: '章节引用、标点符号等格式检查',
      },
      {
        key: 'consistency',
        name: '相似内容比对',
        desc: '底稿溯源对比防止出错',
      },
      {
        key: 'others',
        name: '其他',
        desc: '更多检查功能',
      },
    ],
  },
  {
    key: 'dashboard',
    name: '数据统计',
  },
];

export const PERSON_SUPPORT_FEATURE_KEYS = [
  'listReview',
  'ipo',
  'annual',
  'articulation',
  'faulty_wording',
];
