import * as yup from 'yup';
import { REGEXP_LIST } from '../../../../common/data/regular-expression';

const SCHEME = {
  USERNAME: yup.string().trim().required('用户名不能为空'),
  ACCOUNT: yup.string().trim().required('主账号名不能为空'),
  SUBACCOUNT: yup.string().trim().required('子账号名不能为空'),
  PASSWORD: yup.string().trim().required('密码不能为空'),
  PHONE: yup
    .string()
    .trim()
    .matches(REGEXP_LIST.AUTODOC_PHONE, '手机号码格式不正确，请检查')
    .required('手机号码不能为空'),
  SMSCODE: yup.string().trim().required('验证码不能为空'),
};

export const ACCOUNT_SCHEME = yup.object().shape({
  username: SCHEME.USERNAME,
  password: SCHEME.PASSWORD,
});

export const SUB_ACCOUNT_SCHEME = yup.object().shape({
  account: SCHEME.ACCOUNT,
  subAccount: SCHEME.SUBACCOUNT,
  password: SCHEME.PASSWORD,
});

export const SMSCODE_SCHEME = yup.object().shape({
  username: SCHEME.USERNAME,
  phone: SCHEME.PHONE,
  smsCode: SCHEME.SMSCODE,
});

export const SUB_SMSCODE_SCHEME = yup.object().shape({
  account: SCHEME.ACCOUNT,
  subAccount: SCHEME.SUBACCOUNT,
  phone: SCHEME.PHONE,
  smsCode: SCHEME.SMSCODE,
});
