import loginAccountSvg from '../assets/images/login-account.svg';
import loginSmsCodeSvg from '../assets/images/login-sms-code.svg';
const ACCOUNT_MODE = 'account';
const SMS_CODE_MODE = 'smsCode';

export const ACCOUNT_FORM_GROUP = [
  { name: 'username', label: '用户名：', placeholder: '请输入用户名' },
];

export const SUBACCOUNT_FORM_GROUP = [
  { name: 'account', label: '主账号名：', placeholder: '请输入主账号名' },
  {
    name: 'subAccount',
    label: '子账号名：',
    placeholder: '请输入子账号名',
  },
];

export const LOGIN_MODE_LIST = [
  {
    icon: loginAccountSvg,
    title: '账号密码登录',
    mode: ACCOUNT_MODE,
    name: 'account',
  },
  {
    icon: loginSmsCodeSvg,
    title: '验证码登录',
    mode: SMS_CODE_MODE,
    name: 'sms-code',
  },
];

export const AUTODOC_VIP_AGREEMENT_LIST = [
  {
    title: '一、产品购买',
    desc: '您可以通过商务联系完成购买或申请一定数量的试用次数（联系电话: 010-********，邮箱: <EMAIL>）；',
  },
  {
    title: '二、服务方式',
    desc: '网上登录访问；',
  },
  {
    title: '三、使用次数限制',
    desc: '网上登录访问方式会限制使用分析次数，次数使用完请联系您的客户经理；',
  },
  {
    title: '四、用户声明与承诺',
    desc: '您登录后将视为您本人所为，不能将账号提供给任何第三方人员、机构使用，由此产生的法律问题，将由您承担后果；',
  },
  {
    title: '五、隐私保护',
    desc: '未经您同意，我们不会向第三方共享您的个人信息，文档信息；',
  },
  {
    title: '六、免责声明',
    desc: '请您确保上传的数据，符合国家法律法规和社会道德要求；相关责任和系统无关；',
  },
];
