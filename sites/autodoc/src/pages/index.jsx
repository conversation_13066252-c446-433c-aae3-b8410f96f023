import React, { useCallback, useState, useEffect } from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import SEO from '../components/seo';
import I18n from '../../../../common/components/i18n/i18n';
import SuggestBrowserTip from '../../../../common/components/suggestBrowserTip/index';
import Header from '../components/header';
import ProjectTypes from '../components/project-types';
import NextProjectType from '../components/next-project-type';
import NextIntroContent from '../components/next-intro-content';
import IntroContent from '../components/intro-content';
import ProductsRecommend from '../../../../common/components/products/products-recommend';
import Customers from '../../../../common/components/customers/customersList/customers';
import Link from '../components/link';
import Footer from '../../../../common/components/footer/footer';
import Crm from '../../../../common/components/crm/crm';
import GitInfo from '../../../../common/components/gitInfo/git-info';
import contrastPng from '../assets/images/autodoc-contrast.png';
import { getConfigFile } from '../api/index';
import { ConfigContext } from '../hooks/config-context';
import { CRM_PRODUCT_NAME } from '../../../../common/data/constant';
import { getEnvVariables } from '../../../../common/utils/env';
import '../styles/index.less';

const { isVipMode, isNextSite } = getEnvVariables();

export const Head = () => (
  <SEO
    title={
      isNextSite ? 'AutoDoc 年报智能审核系统' : 'AutoDoc 金融文档智能审核系统'
    }
  />
);

const IndexPage = ({}) => {
  const { gitCommit, customersData, allProductCaseData } = useStaticQuery(
    graphql`
      query {
        gitCommit: gitCommit(latest: { eq: true }) {
          hash
          date
        }
        customersData: allFile(
          filter: { relativePath: { regex: "/common/customers/" } }
          sort: { name: ASC }
        ) {
          nodes {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 568)
            }
            name
          }
        }
        allProductCaseData: allFile(
          filter: {
            absolutePath: { regex: "/common/product-case/product-case/" }
          }
        ) {
          nodes {
            childrenProductCaseJson {
              id
              name
              content
              image {
                childImageSharp {
                  gatsbyImageData(layout: CONSTRAINED)
                }
              }
            }
          }
        }
      }
    `,
  );

  const [config, setConfig] = useState(null);

  const redirectToAutodocVip = useCallback(() => {
    window.location.href = `${window.location.origin}/autodoc/${window.location.search}`;
  }, []);

  const getConfig = useCallback(async () => {
    try {
      const { data: configData } = await getConfigFile();
      setConfig(configData);
    } catch (error) {
      return null;
    }
  }, []);

  useEffect(() => {
    getConfig();
  }, [getConfig]);

  return (
    <ConfigContext.Provider value={config || {}}>
      {config !== null && (
        <>
          <div
            className={
              isVipMode ? 'autodoc-vip-index-page' : 'autodoc-index-page'
            }>
            {isVipMode && config.custom_service ? (
              <Crm currentProduct={CRM_PRODUCT_NAME.AUTODOC_VIP} />
            ) : (
              <Crm currentProduct={CRM_PRODUCT_NAME.AUTODOC_PERSONAL} />
            )}
            <SuggestBrowserTip />
            <Header handleLogin={redirectToAutodocVip} />
            {!isNextSite && (
              <ProductsRecommend
                data={{ allProductCaseData }}
                className="autodoc-products-recommend-top"
              />
            )}
            {isNextSite ? <NextProjectType /> : <ProjectTypes />}
            {isNextSite ? <NextIntroContent /> : <IntroContent />}
            {!isVipMode && (
              <div className="autodoc-contrast">
                <span className="autodoc-contrast-title">AutoDoc 版本对比</span>
                <img src={contrastPng} alt="autodoc-contrast" />
              </div>
            )}
            {!isNextSite && (
              <ProductsRecommend
                data={{ allProductCaseData }}
                className="autodoc-products-recommend-bottom"
              />
            )}
            <Customers data={{ customersData }} />
            <Link />
            <Footer hideWX hideCompany />
          </div>
          <GitInfo data={gitCommit} />
        </>
      )}
    </ConfigContext.Provider>
  );
};

const IndexZhPage = () => {
  return (
    <I18n>
      <IndexPage />
    </I18n>
  );
};

export default IndexZhPage;
