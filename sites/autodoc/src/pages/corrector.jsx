import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  useMemo,
} from 'react';
import ContentEditable from 'react-contenteditable';
import { Spinner } from 'react-bootstrap';
import { toast, Slide, ToastContainer } from 'react-toastify';
import { getFaultWordResult } from '../api/index';
import I18n from '../../../../common/components/i18n/i18n';
import CorrectorSEO from '../components/corrector/corrector-seo';
import CorrectorToggle from '../components/corrector/corrector-toggle';
import CorrectorClearModal from '../components/corrector/corrector-clear-modal';
import emptyTyposPng from '../assets/images/empty-typos.png';
import correctorLogo from '../assets/images/corrector.png';
import {
  setCookie,
  getCookie,
  delCookie,
} from '../../../../common/utils/cookie';
import { getEnvVariables } from '../../../../common/utils/env';
import { useCountdown } from '../../../../common/hooks/useCountdownHook';
import { useHandleResize } from '../../../../common/hooks/useHandleResizeHook';
import {
  getIsMiniScreenSize,
  drawConnectLine,
  getPosition,
  getPointsOfConnectLine,
  getShowConnectLine,
} from '../utils/line';
import '../styles/corrector.less';
import classnames from 'classnames';
import _ from 'lodash';

export const Head = () => <CorrectorSEO />;

const CorrectorPage = () => {
  const TEXT_MAX_LENGTH = 1000;
  const TYPO_TOOL_USE_COUNT_LIMIT = 5;
  const TYPO_TOOL_EXPIRES_TIME = 60;
  const COUNTDOWN_SECONDS = 60;
  const TYPO_TOOL_SUBMIT_TIME = 'typo_tool_submit_time';
  const TYPO_TOOL_SUBMIT_COUNTDOWN = 'typo_tool_submit_countdown';

  const { isVipSite } = getEnvVariables();
  const contentEditableRef = useRef();
  const containerTextTypoRef = useRef();
  const errorCorrectionItemRef = useRef();
  const collectionPolylineRef = useRef();
  const [isLimitUseTypoTool, setIsLimitUseTypoTool] = useState(false);
  const [isCheckTyposLoading, setIsCheckTyposLoading] = useState(false);
  const [textValue, setTextValue] = useState('');
  const [typosDataList, setTyposDataList] = useState([]);
  const [onComposition, setOnComposition] = useState(false);
  const [currentTypoIndex, setCurrentTypoIndex] = useState(1);
  const [currentMobileTypoIndex, setCurrentMobileTypoIndex] = useState(1);
  const [currentMobileTypoData, setCurrentMobileTypoData] = useState();
  const [totalTypoNumber, setTotalTypoNumber] = useState();
  const [isShowTotalNumber, setIsShowTotalNumber] = useState(false);
  const [isMiniScreenSize, setIsMiniScreenSize] = useState();
  const [errorCorrectionItemPosition, setErrorCorrectionItemPosition] =
    useState([]);
  const [submitCountDown, setSubmitCountDown] = useState(COUNTDOWN_SECONDS);
  const { startCountdown, countdown, isCountdowning } =
    useCountdown(submitCountDown);

  const getTextValueWithoutElement = (textValue) => {
    return textValue.replace(/<.*?>/g, '');
  };

  const getNowDate = () => {
    return Date.parse(new Date());
  };

  const getPointAndPosition = useCallback(() => {
    if (isMiniScreenSize) {
      const windowScrollDistance =
        window.pageYOffset || document.documentElement.scrollTop;
      setTimeout(() => {
        getErrorCorrectionItemPosition();
        setTimeout(() => {
          getLinePoints(windowScrollDistance);
        });
      }, 10);
    } else {
      getLinePoints();
    }
  }, [isMiniScreenSize]);

  const getCurrentTypoActive = useCallback(
    (activeIndex) => {
      if (isMiniScreenSize) {
        if (activeIndex) {
          setIsShowTotalNumber(false);
        } else if (_.isUndefined(activeIndex)) {
          setIsShowTotalNumber(true);
        }
        errorCorrectionItemRef.current = document.querySelector(
          '.error-correction-item-active',
        );
        containerTextTypoRef.current = document.querySelector(
          `[data-order-index='${activeIndex}']`,
        );
        setCurrentMobileTypoIndex(activeIndex);
        setCurrentTypoIndex(
          containerTextTypoRef.current?.getAttribute('data-typo-index'),
        );
      } else {
        errorCorrectionItemRef.current = document.querySelector(
          `[data-error-correction-index='${activeIndex}']`,
        );
        containerTextTypoRef.current = document.querySelector(
          `[data-typo-index='${activeIndex}']`,
        );
        setCurrentTypoIndex(activeIndex);
        setCurrentMobileTypoIndex(
          containerTextTypoRef.current?.getAttribute('data-order-index'),
        );
      }
      document.querySelectorAll('.error-correction-item').forEach((item) => {
        item.classList.remove('error-correction-item-active');
      });
      document.querySelectorAll('.container-text-typo').forEach((item) => {
        item.classList.remove('container-text-typo-active');
      });
      errorCorrectionItemRef.current?.classList.add(
        'error-correction-item-active',
      );
      containerTextTypoRef.current?.classList.add('container-text-typo-active');
      getPointAndPosition();
    },
    [isMiniScreenSize, getPointAndPosition],
  );

  const handleCurrentTypoScrollIntoView = useCallback(
    (selectIndex) => {
      getCurrentTypoActive(selectIndex);
      containerTextTypoRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    },
    [getCurrentTypoActive],
  );

  const handleErrorCorrectionItem = useCallback(
    (selectIndex) => {
      handleCurrentTypoScrollIntoView(selectIndex);
    },
    [handleCurrentTypoScrollIntoView],
  );

  const handleContainerTextTypo = useCallback(
    (selectIndex) => {
      getCurrentTypoActive(selectIndex);
      errorCorrectionItemRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    },
    [getCurrentTypoActive],
  );

  const getErrorCorrectionItemPosition = () => {
    if (containerTextTypoRef.current && errorCorrectionItemRef.current) {
      const rightPoints = getPosition(
        containerTextTypoRef.current,
        errorCorrectionItemRef.current,
      );
      setErrorCorrectionItemPosition(rightPoints);
    }
  };

  const getLinePoints = (windowScrollDistance) => {
    if (
      containerTextTypoRef.current?.innerText &&
      errorCorrectionItemRef.current
    ) {
      let points = getPointsOfConnectLine(
        containerTextTypoRef.current,
        errorCorrectionItemRef.current,
        windowScrollDistance,
      );
      let isShowConnectLine = getShowConnectLine(
        containerTextTypoRef.current,
        errorCorrectionItemRef.current,
      );
      if (!isShowConnectLine) {
        collectionPolylineRef.current.setAttribute('points', '');
      } else {
        collectionPolylineRef.current.setAttribute('points', points);
      }
    } else {
      collectionPolylineRef.current.setAttribute('points', '');
    }
  };

  const errorCorrectionItemStyle = useMemo(() => {
    let left;
    if (!_.isNil(errorCorrectionItemPosition[1])) {
      left = errorCorrectionItemPosition[1] + 'px';
    }
    return {
      top: errorCorrectionItemPosition[0] + 'px',
      left: left,
      right: errorCorrectionItemPosition[2] + 'px',
    };
  }, [errorCorrectionItemPosition]);

  const getCurrentIndexToActiveTypo = useCallback(
    (e) => {
      let activeIndex;
      const typoIndex = Number(e.target.getAttribute('data-typo-index'));
      const orderIndex = Number(e.target.getAttribute('data-order-index'));
      if (isMiniScreenSize) {
        if (!isCheckTyposLoading) {
          activeIndex = orderIndex;
        }
      } else {
        activeIndex = typoIndex;
      }
      if (activeIndex) {
        handleContainerTextTypo(activeIndex);
      }
    },
    [handleContainerTextTypo, isMiniScreenSize, isCheckTyposLoading],
  );

  const getTextValueWithTypos = useCallback((resultData) => {
    if (resultData.length > 0) {
      let typosTextStr = '';
      let typoNumer = 0;
      resultData.forEach((item) => {
        let textItem = item.text;
        let startNum = 0;
        if (item.faulty_wordings.length > 0) {
          item.faulty_wordings.forEach((faultyWordingItem) => {
            typoNumer++;
            const position = faultyWordingItem.position_blank;
            let typo = textItem.substring(position[0], position[1]);
            typosTextStr = typosTextStr.concat(
              `<span>${textItem.substring(startNum, position[0])}</span>`,
            );
            typosTextStr = typosTextStr.concat(
              `<span class="container-text-typo" data-typo-index=${typoNumer} data-order-index=${typoNumer}>${typo}</span>`,
            );
            startNum = position[1];
          });
          if (startNum < textItem.length) {
            typosTextStr = typosTextStr.concat(
              `<span>${textItem.substring(startNum)}</span>`,
            );
          }
        } else {
          typosTextStr = typosTextStr.concat(`<span>${textItem}</span>`);
        }
      });
      setTextValue(typosTextStr);
    } else {
      setTextValue('');
    }
  }, []);

  const getTyposList = useCallback(
    (resultData) => {
      if (resultData.length > 0) {
        let typosListArray = [];
        resultData.forEach((item) => {
          if (item.faulty_wordings.length > 0) {
            item.faulty_wordings.forEach((faultyWordingItem) => {
              typosListArray.push({
                correct: faultyWordingItem.correct,
                wrong: faultyWordingItem.wrong,
                comment: faultyWordingItem.comment,
              });
            });
          }
        });
        setTyposDataList(typosListArray);
        setTotalTypoNumber(typosListArray.length);
        setTimeout(() => {
          handleErrorCorrectionItem(1);
          setTimeout(() => {
            handleContainerTextTypo(1);
          }, 500);
        });
      } else {
        setTyposDataList([]);
        setTotalTypoNumber(0);
      }
    },
    [handleErrorCorrectionItem, handleContainerTextTypo],
  );

  const checkUseTypoToolCountIsOverLimit = useCallback(() => {
    let currentTimeArray = getCookie(TYPO_TOOL_SUBMIT_TIME)?.split('-');
    let nowDate = getNowDate();
    if (currentTimeArray) {
      let diffTime = (nowDate - currentTimeArray[0]) / 1000;
      if (diffTime < TYPO_TOOL_EXPIRES_TIME) {
        if (currentTimeArray.length >= TYPO_TOOL_USE_COUNT_LIMIT) {
          setIsLimitUseTypoTool(true);
        }
      } else {
        setCookie(
          TYPO_TOOL_SUBMIT_TIME,
          currentTimeArray.slice(1).join('-'),
          1,
        );
      }
    }
  }, []);

  const saveUseTypoToolTimeInCookie = useCallback(() => {
    let timeArray = getCookie(TYPO_TOOL_SUBMIT_TIME)?.split('-') || [];
    let nowDate = getNowDate();
    timeArray.push(nowDate);
    setCookie(TYPO_TOOL_SUBMIT_TIME, timeArray.join('-'), 1);
    checkUseTypoToolCountIsOverLimit();
  }, [checkUseTypoToolCountIsOverLimit]);

  const saveSubmitCountDownInCookie = useCallback(() => {
    let time = getNowDate();
    setCookie(TYPO_TOOL_SUBMIT_COUNTDOWN, time, COUNTDOWN_SECONDS / 60 / 60);
  }, []);

  const checkTotalTypoNumber = useCallback(() => {
    const totalTypoLength =
      document.querySelectorAll('[data-order-index]').length;
    setTotalTypoNumber(totalTypoLength);
  }, []);

  const addAttributeAboutOrderIndex = useCallback(() => {
    document
      .querySelectorAll('.container-text-typo')
      ?.forEach((item, index) => {
        item.setAttribute('data-order-index', index + 1);
      });
  }, []);

  const handleInput = useCallback(() => {
    if (isMiniScreenSize) {
      getCurrentTypoActive(0);
      if (totalTypoNumber) {
        setIsShowTotalNumber(true);
      }
    }
  }, [totalTypoNumber, getCurrentTypoActive, isMiniScreenSize]);

  const handleChange = useCallback(() => {
    addAttributeAboutOrderIndex();
    checkTotalTypoNumber();
    getPointAndPosition();
    if (!onComposition) {
      setTextValue(contentEditableRef.current.innerHTML);
    }
  }, [
    onComposition,
    checkTotalTypoNumber,
    addAttributeAboutOrderIndex,
    getPointAndPosition,
  ]);

  const handleKeyPress = (event) => {
    if (event.target.innerText.length >= TEXT_MAX_LENGTH) {
      event.preventDefault();
    }
  };

  const handleCompositionStart = useCallback((event) => {
    if (event.target.innerText.length >= TEXT_MAX_LENGTH) {
      handleCursorPositionToEnd();
    }
    setOnComposition(true);
  }, []);

  const handleCompositionEnd = useCallback(() => {
    setOnComposition(false);
    handleChange();
  }, [handleChange]);

  const handleToCheckTypos = useCallback(async () => {
    try {
      if (isMiniScreenSize) {
        getCurrentTypoActive(0);
        setIsShowTotalNumber(true);
        document.documentElement.style.setProperty(
          '--container-text-typo-border-color',
          'transparent',
        );
      }
      if (isLimitUseTypoTool) {
        toast.warning('提交频繁，请稍后再试', {
          position: 'top-center',
          transition: Slide,
        });
        startCountdown();
        saveSubmitCountDownInCookie();
        return;
      }
      setIsCheckTyposLoading(true);
      contentEditableRef.current.setAttribute('contenteditable', 'false');
      const currentTextValue = getTextValueWithoutElement(textValue);
      const resp = await getFaultWordResult({
        source_text: currentTextValue,
      });
      saveUseTypoToolTimeInCookie();
      getTextValueWithTypos(resp.data);
      getTyposList(resp.data);
      if (isMiniScreenSize) {
        setTimeout(() => {
          document.documentElement.style.setProperty(
            '--container-text-typo-border-color',
            '#ff5454',
          );
        }, 30);
      }
    } catch (error) {
      console.error(error);
      if (error.type === 'http') {
        error.message = '错别字检查超时，请重新提交';
      }
      toast.error(error?.message, {
        position: 'top-center',
        transition: Slide,
      });
    } finally {
      contentEditableRef.current.setAttribute('contenteditable', 'true');
      setIsCheckTyposLoading(false);
    }
  }, [
    textValue,
    isMiniScreenSize,
    startCountdown,
    saveSubmitCountDownInCookie,
    saveUseTypoToolTimeInCookie,
    getTextValueWithTypos,
    getTyposList,
    getCurrentTypoActive,
    isLimitUseTypoTool,
  ]);

  const handleCursorPositionToEnd = () => {
    const textContainer = document.querySelector('.container-text');
    if (window.getSelection) {
      textContainer.focus();
      let range = window.getSelection();
      range.selectAllChildren(textContainer);
      range.collapseToEnd();
    } else if (document.selection) {
      let range = document.selection.createRange();
      range.moveToElementText(textContainer);
      range.collapse(false);
      range.select();
    }
  };

  const getCurrentTextWithHTML = useCallback(() => {
    let textLength = 0;
    let itemsTextLength = 0;
    let currentTextStr = '';
    const contentEditableTextChildeNodes = Array.from(
      document.querySelector('.container-text').childNodes,
    );
    contentEditableTextChildeNodes.forEach((item) => {
      const isHTMLItem = !_.isNil(item.innerText);
      if (isHTMLItem) {
        textLength += item.innerText.length;
      } else {
        textLength += item.length;
      }
      if (textLength <= TEXT_MAX_LENGTH) {
        if (isHTMLItem) {
          itemsTextLength += item.innerText.length;
          currentTextStr = currentTextStr.concat(item.outerHTML);
        } else {
          itemsTextLength += item.length;
          currentTextStr = currentTextStr.concat(item.textContent);
        }
      } else {
        let diffLength = TEXT_MAX_LENGTH - itemsTextLength;
        if (isHTMLItem) {
          item.innerHTML = item.innerText.substring(0, diffLength);
          currentTextStr = currentTextStr.concat(item.outerHTML);
        } else {
          item.textContent = item.textContent.substring(0, diffLength);
          currentTextStr = currentTextStr.concat(item.textContent);
        }
      }
    });
    return currentTextStr;
  }, []);

  const checkMaxLengthValue = useCallback(() => {
    if (getTextValueWithoutElement(textValue).length >= TEXT_MAX_LENGTH) {
      const currentTextValue = getCurrentTextWithHTML();
      setTextValue(currentTextValue);
      handleCursorPositionToEnd();
    }
  }, [textValue, getCurrentTextWithHTML]);

  const getCurrentSubmitCountDown = useCallback(() => {
    const submitCountDownTime = getCookie(TYPO_TOOL_SUBMIT_COUNTDOWN);
    if (submitCountDownTime) {
      let nowDate = getNowDate();
      let currentSubmitCountDown =
        COUNTDOWN_SECONDS - (nowDate - submitCountDownTime) / 1000;
      setSubmitCountDown(currentSubmitCountDown);
    }
  }, []);

  const resetSubmitCountDown = useCallback(() => {
    setIsLimitUseTypoTool(false);
    setSubmitCountDown(COUNTDOWN_SECONDS);
    delCookie(TYPO_TOOL_SUBMIT_TIME);
  }, []);

  useEffect(() => {
    if (countdown <= 0) {
      resetSubmitCountDown();
    }
  }, [countdown, resetSubmitCountDown]);

  useEffect(() => {
    getCurrentSubmitCountDown();
  }, [getCurrentSubmitCountDown]);

  useEffect(() => {
    if (submitCountDown !== COUNTDOWN_SECONDS) {
      startCountdown();
    }
  }, [submitCountDown, startCountdown]);

  useEffect(() => {
    checkMaxLengthValue();
  }, [checkMaxLengthValue]);

  useEffect(() => {
    checkUseTypoToolCountIsOverLimit();
  }, [checkUseTypoToolCountIsOverLimit]);

  useEffect(() => {
    const containerText = contentEditableRef.current;
    containerText?.addEventListener('click', getCurrentIndexToActiveTypo);
    return () => {
      containerText?.removeEventListener('click', getCurrentIndexToActiveTypo);
    };
  }, [getCurrentIndexToActiveTypo]);

  useEffect(() => {
    const containerText = contentEditableRef.current;
    containerText?.addEventListener('input', handleInput);
    return () => {
      containerText?.removeEventListener('input', handleInput);
    };
  }, [handleInput]);

  useEffect(() => {
    drawConnectLine();
    const collectionPolyline = document.querySelector('.collection-polyline');
    collectionPolylineRef.current = collectionPolyline;
    const containerText = contentEditableRef.current;
    const errorCorrectionContent = document.querySelector(
      '.error-correction-content',
    );
    containerText?.addEventListener('scroll', getPointAndPosition);
    errorCorrectionContent?.addEventListener('scroll', getPointAndPosition);
    return () => {
      containerText?.removeEventListener('scroll', getPointAndPosition);
      errorCorrectionContent?.removeEventListener(
        'scroll',
        getPointAndPosition,
      );
    };
  }, [getPointAndPosition]);

  const handlePaste = useCallback((event) => {
    let e = event || window.event;
    e.preventDefault();
    let text = e.clipboardData.getData('text/plain');
    text = text.replace(/\r/g, '');
    document.execCommand('insertText', false, text);
  }, []);

  useEffect(() => {
    document.addEventListener('paste', (event) => handlePaste(event));
    return () =>
      document.removeEventListener('paste', (event) => handlePaste(event));
  }, [handlePaste]);

  const getCurrentMobileTypoData = useCallback(() => {
    if (typosDataList) {
      if (currentTypoIndex) {
        setCurrentMobileTypoData(typosDataList[currentTypoIndex - 1]);
      } else {
        setCurrentMobileTypoData(typosDataList[0]);
      }
    }
  }, [currentTypoIndex, typosDataList]);

  useEffect(() => {
    getCurrentMobileTypoData();
  }, [getCurrentMobileTypoData]);

  useEffect(() => {
    if (isMiniScreenSize) {
      getCurrentTypoActive(currentMobileTypoIndex);
    } else {
      getCurrentTypoActive(currentTypoIndex);
    }
  }, [
    isMiniScreenSize,
    currentMobileTypoIndex,
    currentTypoIndex,
    getCurrentTypoActive,
  ]);

  const contentEditableCompontent = useMemo(() => {
    return (
      <ContentEditable
        innerRef={contentEditableRef}
        className="container-text"
        html={textValue}
        onChange={handleChange}
        onKeyPress={handleKeyPress}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
      />
    );
  }, [textValue, handleChange, handleCompositionStart, handleCompositionEnd]);

  const getAutodocCorrectorPageHeight = () => {
    document.documentElement.style.setProperty(
      '--autodoc-corrector-page-height',
      `${window.innerHeight}px`,
    );
  };

  useHandleResize(() => {
    getAutodocCorrectorPageHeight();
    setIsMiniScreenSize(getIsMiniScreenSize());
    getPointAndPosition();
  }, [isMiniScreenSize]);

  useEffect(() => {
    if (isMiniScreenSize) {
      window.addEventListener('scroll', getPointAndPosition);
      return () => {
        window.removeEventListener('scroll', getPointAndPosition);
      };
    }
  }, [isMiniScreenSize, getPointAndPosition]);

  useEffect(() => {
    if (!isVipSite) {
      window.location.href = `/`;
    }
  }, [isVipSite]);

  return (
    _.isBoolean(isMiniScreenSize) &&
    isVipSite && (
      <div className="autodoc-corrector-page">
        <ToastContainer />
        {isMiniScreenSize && (
          <div className="corrector-title">
            <div className="corrector-brand">
              <img
                src={correctorLogo}
                alt="corrector-logo"
                className="corrector-logo"
              />
              <h3 className="corrector-name">Corrector</h3>
            </div>
            {!!totalTypoNumber && (
              <CorrectorToggle
                isShowTotalNumber={isShowTotalNumber}
                isCheckTyposLoading={isCheckTyposLoading}
                typoTotalNumber={totalTypoNumber}
                currentMobileTypoIndex={currentMobileTypoIndex}
                getCurrentTypoActive={getCurrentTypoActive}
                handleCurrentTypoScrollIntoView={
                  handleCurrentTypoScrollIntoView
                }
              />
            )}
          </div>
        )}
        <div className="result-container">
          <div className="container-left">
            <div className="container-text-top">
              {contentEditableCompontent}
              {isMiniScreenSize && currentMobileTypoData && (
                <div
                  className={classnames({
                    'error-correction-item-active': true,
                    'error-correction-item-box':
                      _.isEmpty(errorCorrectionItemPosition) ||
                      !containerTextTypoRef.current,
                  })}
                  style={errorCorrectionItemStyle}>
                  {_.isNil(currentMobileTypoData.correct) ? (
                    <span className="error-correction-result">
                      {currentMobileTypoData.comment}
                    </span>
                  ) : (
                    <>
                      <span className="error-correction-typo">
                        {currentMobileTypoData.wrong}
                      </span>
                      <span className="error-correction-text">建议修改为</span>
                      <span className="error-correction-result">
                        {currentMobileTypoData.correct}
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>
            {isMiniScreenSize ? (
              <div
                className={classnames({
                  'container-text-bottom': true,
                  'container-text-bottom-clear': !_.isEmpty(
                    getTextValueWithoutElement(textValue),
                  ),
                })}>
                {!_.isEmpty(getTextValueWithoutElement(textValue)) && (
                  <CorrectorClearModal
                    setTextValue={setTextValue}
                    setTotalTypoNumber={setTotalTypoNumber}
                    getCurrentTypoActive={getCurrentTypoActive}
                    isCheckTyposLoading={isCheckTyposLoading}
                  />
                )}
                <div className="text-count-limit">
                  <span
                    className={classnames({
                      'text-count-empty': _.isEmpty(
                        getTextValueWithoutElement(textValue),
                      ),
                    })}>
                    {getTextValueWithoutElement(textValue).length}
                  </span>
                  <span> / </span>
                  <span>{TEXT_MAX_LENGTH}字</span>
                </div>
              </div>
            ) : (
              <div className="container-text-bottom">
                <span className="text-count-limit">
                  {getTextValueWithoutElement(textValue).length}字/&nbsp;
                  {TEXT_MAX_LENGTH}字
                </span>
                <button
                  className="text-submit-btn"
                  disabled={isCountdowning || isCheckTyposLoading || !textValue}
                  onClick={() => handleToCheckTypos()}>
                  {isCheckTyposLoading
                    ? '提交中...'
                    : isCountdowning
                      ? `${countdown}s后再提交`
                      : '提交'}
                </button>
              </div>
            )}
          </div>
          {!isMiniScreenSize && (
            <div className="container-right">
              <div className="error-correction-title">
                <h6 className="error-correction-title-text">智能纠错</h6>
                <span className="error-number-prompt">
                  {typosDataList.length}
                </span>
              </div>
              <div className="error-correction-content">
                {_.isEmpty(typosDataList) ? (
                  <div className="error-correction-empty-typos">
                    <img
                      src={emptyTyposPng}
                      alt="empty-typos"
                      className="empty-typos-img"
                    />
                    <p className="error-correction-text error-correction-text-empty">
                      暂时未发现任何待修改问题
                    </p>
                  </div>
                ) : (
                  typosDataList.map((typoItem, index) => (
                    <div
                      key={index}
                      className="error-correction-item"
                      data-error-correction-index={index + 1}
                      onClick={() => handleErrorCorrectionItem(index + 1)}>
                      {_.isNil(typoItem.correct) ? (
                        <span className="error-correction-result">
                          {typoItem.comment}
                        </span>
                      ) : (
                        <>
                          <span className="error-correction-typo">
                            {typoItem.wrong}
                          </span>
                          <span className="error-correction-text">
                            建议修改为
                          </span>
                          <span className="error-correction-result">
                            {typoItem.correct}
                          </span>
                        </>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
        {isMiniScreenSize && (
          <>
            <button
              className="text-submit-btn"
              disabled={isCountdowning || isCheckTyposLoading || !textValue}
              onClick={() => handleToCheckTypos()}>
              {isCountdowning ? `${countdown}s后再提交` : '提交'}
            </button>
            {isCheckTyposLoading && (
              <div className="text-submit-loading">
                <Spinner
                  animation="border"
                  role="status"
                  className="text-submit-spinner"
                />
                <span>检测中...</span>
              </div>
            )}
          </>
        )}
      </div>
    )
  );
};

const CorrectorZhPage = () => {
  return (
    <I18n>
      <CorrectorPage />
    </I18n>
  );
};

export default CorrectorZhPage;
