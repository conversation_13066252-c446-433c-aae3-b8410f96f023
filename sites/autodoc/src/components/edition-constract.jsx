import React from 'react';
import PropTypes from 'prop-types';
import { FEATURES_DATA, PERSON_SUPPORT_FEATURE_KEYS } from '../data/features';
import classnames from 'classnames';
import '../styles/edition-constract.less';

const SupportFeatures = ({ getEditionFeatures }) => {
  return (
    <div className="support-features">
      {Object.values(getEditionFeatures()).map((val, key) => (
        <div key={key} className="support-features-box">
          {typeof val === 'string' ? (
            <span>{val}</span>
          ) : (
            val && <i className="support-feature-check" />
          )}
        </div>
      ))}
    </div>
  );
};

const EditionConstract = () => {
  const defaultFeatures = FEATURES_DATA.reduce((featureMap, item) => {
    if (item.sub) {
      item.sub.forEach((subItem) => {
        featureMap[subItem.key] = false;
      });
    } else {
      if (['loginType', 'chargeType'].includes(item.key)) {
        featureMap[item.key] = '';
      } else {
        featureMap[item.key] = false;
      }
    }
    return featureMap;
  }, {});

  const getPersonEditionFeatures = () => {
    const personFeatures = { ...defaultFeatures };
    Object.keys(personFeatures).map((key) => {
      if (PERSON_SUPPORT_FEATURE_KEYS.includes(key)) {
        personFeatures[key] = true;
      }
    });
    personFeatures.loginType = '微信扫码登录';
    personFeatures.chargeType = '微信充值';
    return personFeatures;
  };

  const getEnterpriseEditionFeatures = () => {
    const enterpriseFeatures = { ...defaultFeatures };
    Object.keys(enterpriseFeatures).map((key) => {
      enterpriseFeatures[key] = true;
    });
    enterpriseFeatures.loginType = 'VIP账号登录';
    enterpriseFeatures.chargeType = '合同签署';
    return enterpriseFeatures;
  };

  return (
    <div className="edition-constract">
      <div className="constract-header">
        <span className="header-feature">功能特性</span>
        <span className="header-edition">个人版</span>
        <span className="header-edition">
          企业版<i className="enterprise-badge"></i>
        </span>
      </div>
      <div className="constract-content">
        <div className="content-features">
          {FEATURES_DATA.map((item, index) => (
            <div
              className={classnames({
                'feature-box': true,
                'has-sub': item.sub,
              })}
              key={index}>
              <span className="feature-name">{`${item.name}${
                item.desc ? '：' : ''
              }`}</span>
              <span className="feature-desc">{item.desc}</span>
              <div className="feature-sub">
                {item.sub &&
                  item.sub.map((subItem, subItemIndex) => (
                    <div key={subItemIndex} className="sub-box">
                      <span className="sub-name">{`${subItem.name}${
                        subItem.desc ? '：' : ''
                      }`}</span>
                      <span className="sub-desc">{subItem.desc}</span>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
        <SupportFeatures getEditionFeatures={getPersonEditionFeatures} />
        <SupportFeatures getEditionFeatures={getEnterpriseEditionFeatures} />
      </div>
    </div>
  );
};

SupportFeatures.propTypes = {
  getEditionFeatures: PropTypes.func,
};

export default EditionConstract;
