import React from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';
import { getEnvVariables } from '../../../../common/utils/env';

const Link = () => {
  const { autodocImage, gearImage } = useStaticQuery(query);
  const autodocIcon = autodocImage.childImageSharp.gatsbyImageData;
  const gearIcon = gearImage.childImageSharp.gatsbyImageData;
  const { isNextSite } = getEnvVariables();

  return (
    <section className="link-wrapper">
      <div className="link-card">
        <div className="link-content">
          <GatsbyImage
            image={autodocIcon}
            alt="autodoc-icon"
            className="autodoc-icon"
          />
          <h3 className="link-text">
            <span className="link-title">
              {isNextSite
                ? 'AutoDoc 年报智能审核系统'
                : 'AutoDoc 金融文档智能审核系统'}
            </span>
            <span className="link-desc">
              <GatsbyImage
                image={gearIcon}
                alt="gear-icon"
                className="gear-icon"
              />
              规避合规风险 提升工作效率
            </span>
          </h3>
        </div>
        <a href="#" className="default-btn link-trial">
          前往试用
        </a>
      </div>
    </section>
  );
};

const query = graphql`
  {
    autodocImage: file(absolutePath: { regex: "/images/autodoc.png/" }) {
      childImageSharp {
        gatsbyImageData(layout: FIXED, height: 90)
      }
      name
    }
    gearImage: file(absolutePath: { regex: "/images/gear-icon.png/" }) {
      childImageSharp {
        gatsbyImageData(layout: FIXED, height: 20)
      }
      name
    }
  }
`;

export default Link;
