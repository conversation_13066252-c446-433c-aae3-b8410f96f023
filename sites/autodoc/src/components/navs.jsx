import React from 'react';
import PropTypes from 'prop-types';
import { Nav, NavItem, OverlayTrigger, Popover } from 'react-bootstrap';
import EditionConstract from './edition-constract';
import { ScrollNav } from '../../../../common/components/scroll/pageScroll/page-scroll';
import { getUrlWithProduct } from '../../../../common/urls';
import { getEnvVariables } from '../../../../common/utils/env';

const Navs = ({ scrollToElement }) => {
  const paodingaiHref = getUrlWithProduct('paodingai', 'index');
  const productHref = getUrlWithProduct('paodingai', 'product');
  const autodocHref = getUrlWithProduct('autodoc', 'index');
  const autodocVipHref = getUrlWithProduct('autodoc-vip', 'index');

  const { isVipMode } = getEnvVariables();

  return (
    <Nav className="header-menu">
      <Nav.Link href={paodingaiHref} target="_blank">
        庖丁科技首页
      </Nav.Link>
      <Nav.Link href={productHref} target="_blank">
        产品
      </Nav.Link>
      <Nav.Link onClick={() => scrollToElement('customers')}>客户</Nav.Link>
      {!isVipMode ? (
        <NavItem className="edition-links">
          <div className="edition-person active">
            <Nav.Link>个人版</Nav.Link>
            <span className="active-line" />
          </div>
          <div className="edition-enterprise">
            <OverlayTrigger
              trigger={['hover', 'focus']}
              rootClose={true}
              placement="bottom-end"
              overlay={
                <Popover className="enterprise-popover">
                  <EditionConstract />
                </Popover>
              }>
              <Nav.Link href={autodocVipHref} target="_blank">
                企业版
              </Nav.Link>
            </OverlayTrigger>
            <i className="enterprise-badge"></i>
          </div>
        </NavItem>
      ) : (
        <NavItem className="edition-links">
          <div className="edition-person">
            <Nav.Link href={autodocHref} target="_blank">
              个人版
            </Nav.Link>
          </div>
          <div className="edition-enterprise active">
            <Nav.Link>企业版</Nav.Link>
            <span className="active-line" />
            <i className="enterprise-badge"></i>
          </div>
        </NavItem>
      )}
    </Nav>
  );
};

Navs.propTypes = {
  scrollToElement: PropTypes.func,
};

export default ScrollNav(Navs);
