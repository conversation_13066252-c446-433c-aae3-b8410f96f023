import React from 'react';
import { Container } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';
import { getEnvVariables } from '../../../../common/utils/env';

const IntroContent = () => {
  const { isVipSite } = getEnvVariables();
  const {
    allIntroCaseData: { nodes: allIntroCaseData },
  } = useStaticQuery(query);

  return (
    <section className="intro-wrapper">
      {allIntroCaseData[0].childrenIntroCaseJson.map((item) => (
        <div className="intro-content" key={item.id}>
          <Container>
            <div className="intro-text">
              <h3 className="intro-title">
                <GatsbyImage
                  image={item.titleIconName.childImageSharp.gatsbyImageData}
                  alt={`${item.name}_icon`}
                />
                <span className="intro-name">{item.name}</span>
              </h3>
              <p
                className="intro-desc"
                dangerouslySetInnerHTML={{ __html: item.desc }}
              />
            </div>
            {!isVipSite ? (
              <GatsbyImage
                image={item.introPersonalImages.childImageSharp.gatsbyImageData}
                className="intro-image"
                alt={item.name}
              />
            ) : (
              <GatsbyImage
                image={
                  item.introEnterpriseImages.childImageSharp.gatsbyImageData
                }
                className="intro-image"
                alt={item.name}
              />
            )}
          </Container>
        </div>
      ))}
    </section>
  );
};

const query = graphql`
  query {
    allIntroCaseData: allFile(
      filter: { absolutePath: { regex: "/intro-case/intro-case/" } }
    ) {
      nodes {
        childrenIntroCaseJson {
          id
          name
          desc
          titleIconName {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 42)
            }
          }
          introPersonalImages {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 1700)
            }
          }
          introEnterpriseImages {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 1700)
            }
          }
        }
      }
    }
  }
`;

export default IntroContent;
