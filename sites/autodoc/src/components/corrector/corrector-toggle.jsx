import React, { useCallback, useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Form } from 'react-bootstrap';
import ArrowPrevSvg from '../../../../../common/assets/icons/arrow-prev.svg';
import ArrowNextSvg from '../../../../../common/assets/icons/arrow-next.svg';
import classnames from 'classnames';
import _ from 'lodash';

const CorrectorToggle = ({
  isShowTotalNumber,
  isCheckTyposLoading,
  typoTotalNumber,
  currentMobileTypoIndex,
  getCurrentTypoActive,
  handleCurrentTypoScrollIntoView,
}) => {
  const [typoIndexValue, setTypoIndexValue] = useState(0);

  const checkToggleBtn = useCallback(
    (currentTypoIndexValue) => {
      if (currentTypoIndexValue > typoTotalNumber) {
        setTypoIndexValue(typoTotalNumber);
        handleCurrentTypoScrollIntoView(typoTotalNumber);
      } else {
        setTypoIndexValue(currentTypoIndexValue);
        handleCurrentTypoScrollIntoView(currentTypoIndexValue);
      }
    },
    [typoTotalNumber, handleCurrentTypoScrollIntoView],
  );

  const disabledPrevBtn = useMemo(() => {
    return typoIndexValue <= 1;
  }, [typoIndexValue]);

  const disabledNextBtn = useMemo(() => {
    return typoIndexValue === typoTotalNumber;
  }, [typoIndexValue, typoTotalNumber]);

  const handleTypoIndexValueChange = useCallback(
    (e) => {
      checkToggleBtn(e.target.value);
    },
    [checkToggleBtn],
  );

  const handleTypoIndexValueBlur = useCallback(
    (e) => {
      const inputVal = _.parseInt(e.target.value);
      if (_.isNaN(inputVal) || inputVal <= 0) {
        checkToggleBtn(1);
      } else {
        checkToggleBtn(inputVal);
      }
    },
    [checkToggleBtn],
  );

  const toggleToPrev = useCallback(() => {
    if (!isCheckTyposLoading) {
      checkToggleBtn(_.toNumber(typoIndexValue) - 1);
    }
  }, [typoIndexValue, checkToggleBtn, isCheckTyposLoading]);

  const toggleToNext = useCallback(() => {
    if (!isCheckTyposLoading) {
      checkToggleBtn(_.toNumber(typoIndexValue) + 1);
    }
  }, [typoIndexValue, checkToggleBtn, isCheckTyposLoading]);

  const toggleToHideTotalNumber = useCallback(() => {
    if (isShowTotalNumber && !isCheckTyposLoading) {
      getCurrentTypoActive(1);
    }
  }, [getCurrentTypoActive, isShowTotalNumber, isCheckTyposLoading]);

  useEffect(() => {
    if (currentMobileTypoIndex || currentMobileTypoIndex === '') {
      checkToggleBtn(currentMobileTypoIndex);
    }
  }, [currentMobileTypoIndex, checkToggleBtn]);

  return (
    <div className="corrector-toggle">
      <button
        className="typo-prev-btn"
        disabled={disabledPrevBtn}
        onClick={toggleToPrev}>
        <ArrowPrevSvg className="arrow-prev" />
      </button>
      <div
        className={classnames({
          'toggle-typo': true,
          'toggle-typo-total': isShowTotalNumber,
        })}
        onClick={toggleToHideTotalNumber}>
        {!isShowTotalNumber && (
          <>
            <div className="typo-current-index">
              <span className="current-index">{typoIndexValue}</span>
              <Form.Control
                type="text"
                name="typoIndex"
                autoComplete="off"
                value={typoIndexValue}
                onChange={handleTypoIndexValueChange}
                onBlur={handleTypoIndexValueBlur}
                className="current-index-input"
              />
            </div>
            <span className="separate-line">/</span>
          </>
        )}
        {isShowTotalNumber && <span>共</span>}
        <span className="typo-total-number">{typoTotalNumber}</span>
        {isShowTotalNumber && <span>个</span>}
      </div>
      <button
        className="typo-next-btn"
        disabled={disabledNextBtn}
        onClick={toggleToNext}>
        <ArrowNextSvg className="arrow-next" />
      </button>
    </div>
  );
};

CorrectorToggle.propTypes = {
  isShowTotalNumber: PropTypes.bool,
  isCheckTyposLoading: PropTypes.bool,
  typoTotalNumber: PropTypes.number,
  currentMobileTypoIndex: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  getCurrentTypoActive: PropTypes.func,
  handleCurrentTypoScrollIntoView: PropTypes.func,
};

export default CorrectorToggle;
