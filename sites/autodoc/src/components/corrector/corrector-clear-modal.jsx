import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import ClearIcon from '../../../../../common/assets/icons/clear.svg';

const CorrectorClearModal = ({
  setTextValue,
  setTotalTypoNumber,
  getCurrentTypoActive,
  isCheckTyposLoading,
}) => {
  const [clearModalVisible, setClearModalVisible] = useState(false);

  const handleClearModalClose = () => {
    setClearModalVisible(false);
  };

  const openClearModal = () => {
    if (!isCheckTyposLoading) {
      setClearModalVisible(true);
    }
  };

  const clearText = () => {
    setTextValue('');
    setTotalTypoNumber(0);
    getCurrentTypoActive(0);
  };

  return (
    <div className="corrector-text-clear">
      <button
        type="button"
        onClick={openClearModal}
        className="text-clear-button">
        <ClearIcon className="text-clear-icon" />
        <span className="text-clear-info">清空</span>
      </button>
      <Modal
        show={clearModalVisible}
        onHide={handleClearModalClose}
        className="corrector-clear-modal">
        <Modal.Body>确认清空吗？</Modal.Body>
        <Modal.Footer>
          <button
            onClick={handleClearModalClose}
            className="default-btn clear-cancel-btn">
            取消
          </button>
          <button onClick={clearText} className="default-btn clear-sure-btn">
            确认
          </button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

CorrectorClearModal.propTypes = {
  setTextValue: PropTypes.func,
  setTotalTypoNumber: PropTypes.func,
  getCurrentTypoActive: PropTypes.func,
  isCheckTyposLoading: PropTypes.bool,
};

export default CorrectorClearModal;
