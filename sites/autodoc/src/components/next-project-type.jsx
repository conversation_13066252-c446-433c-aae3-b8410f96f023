import React, { Fragment } from 'react';
import { Container } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';

const ProjectTypes = () => {
  const { nextProjectTypeImage } = useStaticQuery(query);
  const nextImage = nextProjectTypeImage.childImageSharp.gatsbyImageData;

  return (
    <section className="project-types-wrapper">
      <Container>
        <h3 className="project-title">AI 大模型 助力年报审核 开启新纪元</h3>
        <GatsbyImage
          image={nextImage}
          alt="autodoc-next"
          className="autodoc-icon"
        />
      </Container>
    </section>
  );
};
const query = graphql`
  {
    nextProjectTypeImage: file(
      absolutePath: { regex: "/images/next-project-type.png/" }
    ) {
      childImageSharp {
        gatsbyImageData(layout: CONSTRAINED, height: 475)
      }
      name
    }
  }
`;

export default ProjectTypes;
