import React from 'react';
import { Container } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';

const IntroContent = () => {
  const {
    allNextIntroCaseData: { nodes: allNextIntroCaseData },
  } = useStaticQuery(query);

  return (
    <section className="intro-wrapper">
      {allNextIntroCaseData[0].childrenNextIntroCaseJson.map((item) => (
        <div className="intro-content" key={item.id}>
          <Container>
            <div className="intro-text">
              <h3 className="intro-title">
                <GatsbyImage
                  image={item.titleIconName.childImageSharp.gatsbyImageData}
                  alt={`${item.name}_icon`}
                />
                <span className="intro-name">{item.name}</span>
              </h3>
              <p
                className="intro-desc"
                dangerouslySetInnerHTML={{ __html: item.desc }}
              />
            </div>
            <GatsbyImage
              image={item.introEnterpriseImages.childImageSharp.gatsbyImageData}
              className="intro-image"
              alt={item.name}
            />
          </Container>
        </div>
      ))}
    </section>
  );
};

const query = graphql`
  query {
    allNextIntroCaseData: allFile(
      filter: { absolutePath: { regex: "/next-intro-case/next-intro-case/" } }
    ) {
      nodes {
        childrenNextIntroCaseJson {
          id
          name
          desc
          titleIconName {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 42)
            }
          }
          introEnterpriseImages {
            childImageSharp {
              gatsbyImageData(layout: CONSTRAINED, width: 1700)
            }
          }
        }
      }
    }
  }
`;

export default IntroContent;
