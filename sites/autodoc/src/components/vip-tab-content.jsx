import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import { Form } from 'react-bootstrap';
import { Formik } from 'formik';
import * as yup from 'yup';
import { encode, decode } from 'js-base64';
import {
  setCookie,
  getCookie,
  delCookie,
} from '../../../../common/utils/cookie';
import { LOGIN_MODE_LIST } from '../data/data-vip';
import {
  ACCOUNT_SCHEME,
  SUB_ACCOUNT_SCHEME,
  SMSCODE_SCHEME,
  SUB_SMSCODE_SCHEME,
} from '../data/scheme';
import {
  autoDocVipLogin,
  autoDocVipChildLogin,
  vipLoginBySmsCode,
  vipChildLoginBySmsCode,
  queryVipUserLoginMode,
  getSmsCode,
  AutodocSmsToken,
} from '../api/index';
import { getEnvVariables } from '../../../../common/utils/env';
import { useCountdown } from '../../../../common/hooks/useCountdownHook';
import { REGEXP_LIST } from '../../../../common/data/regular-expression';
import ConnectBtn from '../../../../common/components/connect/connectButton/connect-btn';
import vipIcon from '../assets/images/vip.png';
import ApplyTrialSvg from '../assets/icons/apply-trial.svg';
import classnames from 'classnames';

const ACCOUNT_MODE = 'account';
const SMS_CODE_MODE = 'smsCode';
const USER_LOGIN_MODE_KEY = 'user_login_mode';

const VipTabContent = ({
  canLoginBySmsCode,
  subAccountEnable,
  formGroup,
  setShowUserAgreement,
  checkHandleLogin,
  showResetPassword,
  currentUser,
}) => {
  const { isNextSite, isVipSite } = getEnvVariables();
  const USER_LOGIN_FORM = `${window.location.port}_user_login_form`;
  const SUB_USER_LOGIN_FORM = `${window.location.port}_sub_user_login_form`;

  const checkbox = useRef();
  const [errorMsg, setErrorMsg] = useState('');
  const [loginByAccount, setLoginByAccount] = useState(false);
  const [loginBySmsCode, setLoginBySmsCode] = useState(false);
  const [vipAccountInfo, setVipAccountInfo] = useState({});
  const [currentLoginMode, setCurrentLoginMode] = useState(ACCOUNT_MODE);
  const [sendSmsCodeLoading, setSendSmsCodeLoading] = useState(false);
  const [cacheUserLoginModeList, setCacheUserLoginModeList] = useState([]);
  const [currentUserValue, setCurrentUserValue] = useState();
  const [isCheckedUserService, setCheckedUserService] = useState(true);
  const [initialValue, setInitialValue] = useState();
  const [enableReinitialize, setEnableReinitialize] = useState(true);

  const { startCountdown, countdown, isCountdowning } = useCountdown(60);

  const schemaPhone = yup.object().shape({
    phone: yup
      .string()
      .trim()
      .matches(REGEXP_LIST.AUTODOC_PHONE, '手机号码格式不正确，请检查')
      .required('手机号码不能为空'),
  });

  const validSchema = useCallback(() => {
    if (loginBySmsCode) {
      if (!subAccountEnable) {
        return SMSCODE_SCHEME;
      } else {
        return SUB_SMSCODE_SCHEME;
      }
    }
    if (subAccountEnable) {
      return SUB_ACCOUNT_SCHEME;
    } else {
      return ACCOUNT_SCHEME;
    }
  }, [loginBySmsCode, subAccountEnable]);

  const hasAccountLoginMode = useMemo(() => {
    if (vipAccountInfo) {
      if (vipAccountInfo.account_password_login === undefined) {
        return true;
      }
      return vipAccountInfo.account_password_login;
    }
  }, [vipAccountInfo]);

  const hasSmsCodeLoginMode = useMemo(() => {
    if (vipAccountInfo) {
      return vipAccountInfo.sms_code_login;
    }
  }, [vipAccountInfo]);

  const queryLoginMode = async (e) => {
    const parentUserName = e.target.value;
    if (parentUserName === '') {
      setVipAccountInfo({});
    }
    if (e.target.name === 'subAccount') return;
    const { data } = await queryVipUserLoginMode({
      parent_username: parentUserName,
    });
    setVipAccountInfo(data);
    setCurrentUserValue(parentUserName);
  };

  useEffect(() => {
    if (!hasAccountLoginMode && hasSmsCodeLoginMode) {
      setCurrentLoginMode(SMS_CODE_MODE);
    } else {
      setCurrentLoginMode(ACCOUNT_MODE);
    }
    if (hasAccountLoginMode && hasSmsCodeLoginMode) {
      const cacheLoginModeList =
        JSON.parse(localStorage.getItem(USER_LOGIN_MODE_KEY)) || [];
      setCacheUserLoginModeList(cacheLoginModeList);
    }
  }, [hasAccountLoginMode, hasSmsCodeLoginMode]);

  useEffect(() => {
    if (hasAccountLoginMode && hasSmsCodeLoginMode) {
      const currentUserLoginMode = cacheUserLoginModeList.find(
        (item) => item.name === currentUserValue,
      );
      if (currentUserLoginMode) {
        setCurrentLoginMode(currentUserLoginMode.mode);
      }
    }
  }, [
    hasAccountLoginMode,
    hasSmsCodeLoginMode,
    cacheUserLoginModeList,
    currentUserValue,
  ]);

  useEffect(() => {
    if (currentLoginMode === ACCOUNT_MODE) {
      setLoginByAccount(true);
      setLoginBySmsCode(false);
    } else if (currentLoginMode === SMS_CODE_MODE) {
      setLoginBySmsCode(true);
      setLoginByAccount(false);
    }
  }, [currentLoginMode]);

  const getSmsCodeParams = useCallback(
    (value, token) => {
      let params = {
        phone_number: value.phone,
        username: !subAccountEnable ? value.username : value.subAccount,
        csrf_token: token,
      };
      if (subAccountEnable) {
        params.parent_username = value.account;
      }
      return params;
    },
    [subAccountEnable],
  );

  const sendSmsCodeToPhone = async (value) => {
    try {
      setErrorMsg('');
      setSendSmsCodeLoading(true);
      const { data } = await AutodocSmsToken();
      await getSmsCode(getSmsCodeParams(value, data.csrf_token));
      startCountdown();
    } catch (error) {
      console.error(error);
      setErrorMsg(error.message || '获取验证码失败');
    } finally {
      setSendSmsCodeLoading(false);
    }
  };

  const getLoginParams = useCallback(
    (value) => {
      let data = {};
      if (loginBySmsCode) {
        data = {
          username: value.username,
          phone_number: value.phone,
          sms_code: value.smsCode,
        };
        if (subAccountEnable) {
          data.parent_username = value.account;
          data.username = value.subAccount;
        }
      } else {
        data = {
          username: value.username,
          password: value.password,
          isVip: true,
        };
        if (subAccountEnable) {
          data.parent_username = value.account;
          data.username = value.subAccount;
        }
      }
      return data;
    },
    [loginBySmsCode, subAccountEnable],
  );

  useEffect(() => {
    if (!isCheckedUserService) {
      setErrorMsg('请阅读并勾选同意《用户协议》');
    } else {
      setErrorMsg('');
    }
  }, [isCheckedUserService]);

  const cacheLoginMode = useCallback(
    (value) => {
      if (hasAccountLoginMode && hasSmsCodeLoginMode) {
        const currentUserLoginMode = cacheUserLoginModeList.find(
          (item) => item.name === value.username,
        );
        if (!!currentUserLoginMode) {
          currentUserLoginMode.mode = currentLoginMode;
        } else {
          cacheUserLoginModeList.push({
            name: value.username,
            mode: currentLoginMode,
          });
        }
        localStorage.setItem(
          USER_LOGIN_MODE_KEY,
          JSON.stringify(cacheUserLoginModeList),
        );
      }
    },
    [
      cacheUserLoginModeList,
      currentLoginMode,
      hasAccountLoginMode,
      hasSmsCodeLoginMode,
    ],
  );

  const cacheUserLoginForm = useCallback(
    (value) => {
      if (value.password === '') return;
      if (value.rememberPassword) {
        value.smsCode = '';
        const enCodeForm = encode(JSON.stringify(value));
        if (subAccountEnable) {
          setCookie(SUB_USER_LOGIN_FORM, enCodeForm, 72);
        } else {
          setCookie(USER_LOGIN_FORM, enCodeForm, 72);
        }
      } else {
        if (subAccountEnable) {
          delCookie(SUB_USER_LOGIN_FORM);
        } else {
          delCookie(USER_LOGIN_FORM);
        }
      }
    },
    [subAccountEnable, USER_LOGIN_FORM, SUB_USER_LOGIN_FORM],
  );

  const cacheValue = useCallback(
    (value) => {
      cacheLoginMode(value);
      cacheUserLoginForm(value);
    },
    [cacheLoginMode, cacheUserLoginForm],
  );

  const handleVipLogin = useCallback(
    async (value) => {
      setErrorMsg('');
      const data = getLoginParams(value);
      let resp = null;
      try {
        if (subAccountEnable) {
          if (loginBySmsCode) {
            resp = await vipChildLoginBySmsCode(data);
          } else {
            resp = await autoDocVipChildLogin(data);
          }
        } else {
          if (loginBySmsCode) {
            resp = await vipLoginBySmsCode(data);
          } else {
            resp = await autoDocVipLogin(data);
          }
        }
        cacheValue(value);
        checkHandleLogin(resp.data.redirect_url);
      } catch (error) {
        if (error.status === 418) {
          showResetPassword(data);
          return;
        }
        console.error(error);
        setErrorMsg(error.message || '用户名或密码错误');
      }
    },
    [
      cacheValue,
      setErrorMsg,
      loginBySmsCode,
      getLoginParams,
      checkHandleLogin,
      showResetPassword,
      subAccountEnable,
    ],
  );

  const getInitialValue = useCallback(
    (initialCookieValue) => {
      let initialValueData = {
        username: '',
        account: '',
        subAccount: '',
        password: '',
        phone: '',
        smsCode: '',
        rememberPassword: false,
        ...initialCookieValue,
      };
      if (currentUser) {
        if (currentUser.parent) {
          initialValueData.account = currentUser.parent.username;
          initialValueData.subAccount = currentUser.username;
        } else {
          initialValueData.username = currentUser.username;
        }
      }
      setInitialValue(initialValueData);
    },
    [currentUser],
  );

  const checkCookie = useCallback(() => {
    const loginForm = getCookie(USER_LOGIN_FORM);
    const subLoginForm = getCookie(SUB_USER_LOGIN_FORM);
    let initialCookieValue = {};
    if (loginForm && !subAccountEnable) {
      const deCodeForm = decode(loginForm);
      initialCookieValue = { ...JSON.parse(deCodeForm) };
    }
    if (subLoginForm && subAccountEnable) {
      const deCodeForm = decode(subLoginForm);
      initialCookieValue = { ...JSON.parse(deCodeForm) };
    }
    getInitialValue(initialCookieValue);
  }, [USER_LOGIN_FORM, SUB_USER_LOGIN_FORM, getInitialValue, subAccountEnable]);

  useEffect(() => {
    checkCookie();
  }, [checkCookie]);

  return (
    <div
      className={classnames({
        'vip-tab-content': true,
        'account-content': !subAccountEnable,
        'sub-account-content': subAccountEnable,
      })}>
      <div className="vip-login-wrapper">
        <div className="login-type">
          <img className="vip-icon" src={vipIcon} alt="vip-icon" />
          <span>{!subAccountEnable ? `VIP账号登录` : `VIP子账号登录`}</span>
        </div>
        {initialValue && (
          <Formik
            initialValues={initialValue}
            enableReinitialize={enableReinitialize}
            validationSchema={validSchema()}
            onSubmit={(values) => {
              setEnableReinitialize(false);
              handleVipLogin(values);
            }}>
            {({
              values,
              errors,
              touched,
              handleSubmit,
              handleChange,
              handleBlur,
              setFieldError,
              setFieldTouched,
              setFieldValue,
            }) => (
              <Form onSubmit={handleSubmit} className="vip-login-form">
                {formGroup.map((item, index) => (
                  <Form.Group key={index}>
                    <Form.Label>{item.label}</Form.Label>
                    <Form.Control
                      type="text"
                      name={item.name}
                      placeholder={item.placeholder}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values[item.name]}
                      onInput={queryLoginMode}
                      isInvalid={touched[item.name] && !!errors[item.name]}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors[item.name]}
                    </Form.Control.Feedback>
                  </Form.Group>
                ))}
                {loginBySmsCode && (
                  <>
                    <Form.Group>
                      <Form.Label>手机号：</Form.Label>
                      <Form.Control
                        type="text"
                        name="phone"
                        placeholder="请输入手机号"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.phone}
                        isInvalid={touched.phone && !!errors.phone}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.phone}
                      </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group className="sms-code">
                      <Form.Label>验证码：</Form.Label>
                      <Form.Control
                        type="text"
                        name="smsCode"
                        placeholder="请输入验证码"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.smsCode}
                        isInvalid={touched.smsCode && !!errors.smsCode}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.smsCode}
                      </Form.Control.Feedback>
                      <button
                        className="default-btn send-sms-code"
                        disabled={isCountdowning || sendSmsCodeLoading}
                        onClick={() => {
                          setErrorMsg('');
                          const phone = values.phone;
                          schemaPhone
                            .validate({ phone })
                            .then(() => sendSmsCodeToPhone(values))
                            .catch((e) => {
                              setFieldTouched('phone', true);
                              setFieldError('phone', e.message);
                            });
                        }}>
                        {sendSmsCodeLoading
                          ? '发送中...'
                          : isCountdowning
                            ? `${countdown}s后重新发送`
                            : '发送验证码'}
                      </button>
                    </Form.Group>
                  </>
                )}
                {loginByAccount && (
                  <Form.Group>
                    <Form.Label>密码：</Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      placeholder="请输入密码"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.password}
                      isInvalid={touched.password && !!errors.password}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.password}
                    </Form.Control.Feedback>
                  </Form.Group>
                )}
                {errorMsg ? <p className="error-message">{errorMsg}</p> : null}
                <div
                  className={classnames({
                    'user-field-content': true,
                    'sms-code-field-content': !loginByAccount,
                  })}>
                  {loginByAccount && (
                    <Form.Check
                      inline
                      ref={checkbox}
                      type="checkbox"
                      defaultChecked={values.rememberPassword}
                      id={
                        !subAccountEnable ? 'remember-pwd' : 'vip-remember-pwd'
                      }
                      label={'记住密码'}
                      onChange={() =>
                        setFieldValue(
                          'rememberPassword',
                          checkbox.current.checked,
                        )
                      }
                    />
                  )}
                  {!subAccountEnable && (
                    <div className="apply-trial">
                      {/* <button
                        type="button"
                        className="trial-button"
                        onClick={() => setShowApplyModal(true)}>
                        <ApplyTrialSvg className="trial-img" />
                        <span>申请试用</span>
                      </button> */}
                      <div className="trial-button">
                        <ApplyTrialSvg className="trial-img" />
                        <ConnectBtn buttonTitle="申请试用" />
                      </div>
                    </div>
                  )}
                </div>
                <button
                  type="submit"
                  className="default-btn vip-login-btn"
                  disabled={!isCheckedUserService}>
                  登录
                </button>
                <div className="form-footer">
                  <div className="checkbox-wrapper">
                    <Form.Control
                      type="checkbox"
                      className="agreement-checkbox"
                      defaultChecked={isCheckedUserService}
                      onChange={() =>
                        setCheckedUserService(!isCheckedUserService)
                      }
                    />
                    <span className="text">
                      同意
                      <span
                        className="link"
                        onClick={() => setShowUserAgreement(true)}>
                        《用户协议》
                      </span>
                    </span>
                  </div>
                  {canLoginBySmsCode &&
                    hasAccountLoginMode &&
                    hasSmsCodeLoginMode &&
                    LOGIN_MODE_LIST.map(
                      (item, index) =>
                        currentLoginMode !== item.mode && (
                          <span
                            className="login-mode"
                            key={index}
                            onClick={() => {
                              setErrorMsg('');
                              setCurrentLoginMode(item.mode);
                            }}>
                            <img src={item.icon} alt={`${item.name}-icon`} />
                            <span>{item.title}</span>
                          </span>
                        ),
                    )}
                </div>
              </Form>
            )}
          </Formik>
        )}
      </div>
    </div>
  );
};

export default VipTabContent;
