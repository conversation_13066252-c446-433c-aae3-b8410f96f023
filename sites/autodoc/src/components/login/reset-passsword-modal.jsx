import React from 'react';
import PropTypes from 'prop-types';
import { Modal, Form } from 'react-bootstrap';
import { toast, Slide } from 'react-toastify';
import { Formik } from 'formik';
import * as yup from 'yup';
import { autoDocResetPassword } from '../../api/index';
import { REGEXP_LIST } from '../../../../../common/data/regular-expression';
import 'react-toastify/dist/ReactToastify.css';

const ResetPasswordModal = ({ title, show, onHide, resetPasswordUser }) => {
  const resetPasswordScheme = yup.object().shape({
    newPassword: yup
      .string()
      .matches(
        REGEXP_LIST.PASSWORD_VALIDATE,
        '长度在 6 到 18 个字符(字母、数字、下划线、连字符、点)',
      )
      .required('请输入新密码'),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref('newPassword')], '两次密码不一致，请重新输入')
      .required('请输入确认密码'),
  });

  const handleUpdateUserPassword = async (value) => {
    try {
      const data = {
        username: resetPasswordUser.username,
        new_password: value.confirmPassword,
      };
      if (resetPasswordUser.parent_username) {
        data.parent_username = resetPasswordUser.parent_username;
      }
      await autoDocResetPassword(data);
      toast.success('重置密码成功', {
        autoClose: false,
        transition: Slide,
      });
      onHide();
    } catch (error) {
      toast.error(error.message, {
        autoClose: 3000,
        hideProgressBar: true,
        transition: Slide,
      });
    }
  };

  return (
    <Modal
      show={show}
      dialogClassName="reset-password-modal"
      onHide={() => onHide()}>
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Formik
          initialValues={{ newPassword: '', confirmPassword: '' }}
          validationSchema={resetPasswordScheme}
          onSubmit={handleUpdateUserPassword}>
          {({
            values,
            errors,
            touched,
            handleSubmit,
            handleChange,
            handleBlur,
          }) => (
            <Form onSubmit={handleSubmit} className="password-form">
              <Form.Group className="form-item-wrapper">
                <Form.Label bsPrefix="bs-label" className="form-label">
                  新密码：
                </Form.Label>
                <Form.Control
                  type="password"
                  name="newPassword"
                  value={values.newPassword}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isValid={touched.newPassword && !errors.newPassword}
                  isInvalid={touched.newPassword && !!errors.newPassword}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.newPassword}
                </Form.Control.Feedback>
              </Form.Group>
              <Form.Group className="form-item-wrapper">
                <Form.Label bsPrefix="bs-label" className="form-label">
                  确认新密码：
                </Form.Label>
                <Form.Control
                  type="password"
                  name="confirmPassword"
                  value={values.confirmPassword}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isValid={touched.confirmPassword && !errors.confirmPassword}
                  isInvalid={
                    touched.confirmPassword && !!errors.confirmPassword
                  }
                />
                <Form.Control.Feedback type="invalid">
                  {errors.confirmPassword}
                </Form.Control.Feedback>
              </Form.Group>
              <div className="form-button">
                <button type="submit" className="default-btn">
                  确认
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </Modal.Body>
    </Modal>
  );
};

ResetPasswordModal.defaultProps = {
  title: '密码重置',
  show: true,
};

ResetPasswordModal.propTypes = {
  title: PropTypes.string,
  show: PropTypes.bool,
  onHide: PropTypes.func,
  resetPasswordUser: PropTypes.object,
};

export default ResetPasswordModal;
