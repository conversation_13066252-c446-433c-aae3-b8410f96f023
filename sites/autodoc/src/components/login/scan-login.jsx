import React, { useRef, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Form, Spinner } from 'react-bootstrap';
import {
  autoDocGetUserInfo,
  getWechatLoginCode,
  getWechatLoginStatus,
  updateUserServiceAgreement,
} from '../../api/index';
import UserAgreement from '../user-agreement';
import { AUTODOC_AGREEMENT_LIST } from '../../data/data';
import { useConfigContext } from '../../hooks/config-context';
import UserServiceSvg from '../../assets/icons/user-service.svg';
import WXChartSvg from '../../../../../common/assets/icons/wechat.svg';
import RefreshSvg from '../../../../../common/assets/icons/refresh.svg';

const ScanLogin = ({ handleLogin }) => {
  const config = useConfigContext();
  const checkboxRef = useRef();
  const loginStatusTimeOut = useRef();
  const checkLoginQRCodeInvalidTimeOut = useRef();
  const [userInfo, setUserInfo] = useState(null);
  const [weChatLoginQR, setWeChatLoginQR] = useState(null);
  const [weChatLoginState, setWeChatLoginState] = useState(null);
  const [isExpired, setIsExpired] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);
  const [isShowLogin, setIsShowLogin] = useState(false);
  const [isSystemError, setIsSystemError] = useState(false);
  const [isCheckedUserService, setIsCheckedUserService] = useState(false);

  const queryWechatLoginStatus = useCallback(
    async (params) => {
      try {
        const res = await getWechatLoginStatus(params);
        if (checkLoginQRCodeInvalidTimeOut.current) {
          clearTimeout(checkLoginQRCodeInvalidTimeOut.current);
        }
        if (res.data) {
          clearTimeout(loginStatusTimeOut.current);
          if (res.data.redirect_url) {
            window.location.href = res.data.redirect_url;
          } else {
            handleLogin();
          }
        }
      } catch (error) {
        loginStatusTimeOut.current = setTimeout(
          () => queryWechatLoginStatus(params),
          2000,
        );
      }
    },
    [handleLogin],
  );

  const getUserInfo = async () => {
    try {
      const userInfo = await autoDocGetUserInfo();
      setUserInfo(userInfo);
      return userInfo;
    } catch (error) {
      setIsShowLogin(true);
      return null;
    }
  };

  const checkLoginQRCodeInvalid = () => {
    checkLoginQRCodeInvalidTimeOut.current = setTimeout(() => {
      if (loginStatusTimeOut.current) {
        clearTimeout(loginStatusTimeOut.current);
      }
      setIsExpired(true);
    }, 60e3);
  };

  const checkLogin = useCallback(
    async (weChatLoginState) => {
      try {
        const userInfo = await getUserInfo();
        if (userInfo) {
          handleLogin();
          return;
        }
        const weChatInfo = await getWechatLoginCode({
          state: weChatLoginState,
        });
        setIsExpired(false);
        const { qrcode, query_id } = weChatInfo.data;
        setWeChatLoginQR(qrcode);
        await queryWechatLoginStatus({
          query_id: query_id,
        });
        checkLoginQRCodeInvalid();
      } catch (error) {
        setIsSystemError(true);
      }
    },
    [queryWechatLoginStatus, handleLogin],
  );

  const onSubmit = async ({ id, data }) => {
    try {
      const updateUserInfo = await updateUserServiceAgreement(id, data);
      setUserInfo(updateUserInfo);
      handleLogin();
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (config.wechat_login_state) {
      checkLogin(config.wechat_login_state);
      setWeChatLoginState(config.wechat_login_state);
    }
  }, [config, checkLogin]);

  useEffect(() => {
    return () => {
      if (loginStatusTimeOut.current) {
        clearTimeout(loginStatusTimeOut.current);
      }
      if (checkLoginQRCodeInvalidTimeOut.current) {
        clearTimeout(checkLoginQRCodeInvalidTimeOut.current);
      }
    };
  }, []);

  return (
    isShowLogin && (
      <div className="scan-login">
        <div className="scan-login-container">
          <div className="scan-login-box">
            <h5 className="scan-login-title">
              <WXChartSvg className="login-title-logo" />
              微信扫一扫登录
            </h5>
            {isCheckedUserService ? (
              <div>
                <UserServiceSvg className="wechat-tips-img" />
                <p>请在勾选《用户协议》后再扫码登录</p>
              </div>
            ) : weChatLoginQR ? (
              <div className="login-qr-box">
                <div className="qr-content">
                  {isExpired && (
                    <div
                      className="qr-expired"
                      onClick={() => checkLogin(weChatLoginState)}>
                      <span className="qr-info">二维码失效，点击刷新</span>
                      <RefreshSvg className="qr-svg" />
                    </div>
                  )}
                  <img className="login-qr" src={weChatLoginQR} alt="" />
                </div>
                <p className="qr-desc">
                  扫码成功后，点击“<i>关注公众号</i>”即可登录
                </p>
              </div>
            ) : isSystemError ? (
              <span className="qr-text-error">获取二维码失败</span>
            ) : (
              <div className="qr-spinner">
                <Spinner animation="grow" variant="primary" />
              </div>
            )}
          </div>
          <div className="checkbox-wrapper">
            <Form.Control
              ref={checkboxRef}
              type="checkbox"
              defaultChecked={true}
              className="agreement-checkbox"
              onChange={() => {
                setIsCheckedUserService(!checkboxRef.current.checked);
              }}
            />
            <span className="text">
              同意
              <span className="link" onClick={() => setIsShowModal(true)}>
                《用户协议》
              </span>
            </span>
            <UserAgreement
              show={isShowModal}
              onHide={() => setIsShowModal(false)}
              isShowFooter={userInfo !== null}
              onSubmit={() =>
                onSubmit({
                  id: userInfo.data.id,
                  data: {
                    show_service_agreement: false,
                  },
                })
              }
              agreementList={AUTODOC_AGREEMENT_LIST}
            />
          </div>
        </div>
      </div>
    )
  );
};

ScanLogin.propTypes = {
  handleLogin: PropTypes.func,
};

export default ScanLogin;
