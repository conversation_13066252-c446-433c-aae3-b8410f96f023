import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab } from 'react-bootstrap';
import UserAgreement from '../user-agreement';
import EmailVerifyModal from './email-verify-modal';
import SmsVerifyModal from './sms-verify-modal';
import ResetPasswordModal from './reset-passsword-modal';
import VipTabContent from '../vip-tab-content';
import {
  ACCOUNT_FORM_GROUP,
  SUBACCOUNT_FORM_GROUP,
  AUTODOC_VIP_AGREEMENT_LIST,
} from '../../data/data-vip';
import {
  updateUserServiceAgreement,
  autoDocGetUserInfo,
  getInvalidEmails,
} from '../../api/index';
import { useConfigContext } from '../../hooks/config-context';

const USER_SYS = {
  EXT_SYS_VIP: 3,
  EXT_SYS_FILE_FLAWS: 6,
};

const VIP_TAB_DATA = [
  {
    key: 1,
    title: 'VIP账号',
    formGroup: ACCOUNT_FORM_GROUP,
    subAccountEnable: false,
  },
  {
    key: 2,
    title: 'VIP子账号',
    formGroup: SUBACCOUNT_FORM_GROUP,
    subAccountEnable: true,
  },
];

const VIPLogin = ({ handleLogin }) => {
  const config = useConfigContext();
  const [showUserAgreement, setShowUserAgreement] = useState(false);
  const [showUserAgreementFooter, setShowUserAgreementFooter] = useState(false);
  const [showEmailVerifyModal, setShowEmailVerifyModal] = useState(false);
  const [showSmsVerifyModal, setShowSmsVerifyModal] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const [invalidEmailList, setInvalidEmailList] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [resetPasswordUser, setResetPasswordUser] = useState(null);
  const [activeKey, setActiveKey] = useState(1);
  const [navItemWidth, setNavItemWidth] = useState([]);

  const canLoginBySmsCode = useMemo(() => {
    return config.show_phone_sms_code_login;
  }, [config]);

  const casdoorEnable = useMemo(() => {
    return config.casdoor_auth_enable;
  }, [config]);

  // saas-py3 是55650测试环境，saas-2b-pre是65176预发布环境，saas-mvip是生产环境。
  const isProdEnv = useMemo(() => {
    const platform = config.platform;
    return (
      platform === 'saas-mvip' ||
      platform === 'saas-dev' ||
      platform === 'saas-2b-pre' ||
      platform === 'saas-py3'
    );
  }, [config]);

  const isFileFlawsUser = useCallback((userInfo) => {
    return userInfo.user_sys === USER_SYS.EXT_SYS_FILE_FLAWS;
  }, []);

  const isVIPUser = useCallback((userInfo) => {
    return userInfo.user_sys === USER_SYS.EXT_SYS_VIP;
  }, []);

  const needEmailVerify = useCallback(
    (userInfo) => {
      return (
        userInfo &&
        userInfo.email_verify &&
        !userInfo.user_email_verified &&
        (isFileFlawsUser(userInfo) || isVIPUser(userInfo))
      );
    },
    [isFileFlawsUser, isVIPUser],
  );

  const needPhoneVerify = useCallback((userInfo) => {
    return userInfo && userInfo.show_telephone_verify;
  }, []);

  const isShowServiceAgreement = useCallback(
    (userInfo) => {
      return (
        isProdEnv &&
        userInfo &&
        isVIPUser(userInfo) &&
        userInfo.show_service_agreement !== false
      );
    },
    [isProdEnv, isVIPUser],
  );

  const getInvalidEmailsList = async () => {
    try {
      const resp = await getInvalidEmails();
      setInvalidEmailList(resp.data);
    } catch (error) {
      console.error(error);
    }
  };

  const navLineActivedStyle = useMemo(
    () => ({
      transform:
        'translateX(' +
        navItemWidth
          .slice(0, activeKey - 1)
          .reduce((sum, width) => sum + width, 0) +
        'px)',
    }),
    [activeKey, navItemWidth],
  );

  useEffect(() => {
    const navItemList = Array.from(
      document.querySelectorAll('.vip-login-tabs>.nav-item'),
    );
    setNavItemWidth(navItemList.map((el) => el.offsetWidth));
  }, []);

  useEffect(() => {
    if (currentUser && currentUser.parent) {
      setActiveKey(2);
    }
  }, [currentUser]);

  const getUserInfoData = useCallback(async () => {
    try {
      const { data: userInfoData } = await autoDocGetUserInfo();
      setCurrentUser(userInfoData);
      return userInfoData;
    } catch (error) {
      return null;
    }
  }, []);

  const handleLoginRedirect = useCallback(
    (redirectUrl) => {
      if (casdoorEnable && redirectUrl) {
        window.location.href = redirectUrl;
      } else {
        handleLogin();
      }
    },
    [casdoorEnable, handleLogin],
  );

  const handleLoginSuccess = useCallback(
    async (userInfo, redirectUrl) => {
      if (needPhoneVerify(userInfo)) {
        setShowSmsVerifyModal(true);
      } else if (needEmailVerify(userInfo)) {
        await getInvalidEmailsList();
        setShowEmailVerifyModal(true);
      } else if (isShowServiceAgreement(userInfo)) {
        setShowUserAgreement(true);
        setShowUserAgreementFooter(true);
      } else {
        handleLoginRedirect(redirectUrl);
      }
    },
    [
      needPhoneVerify,
      needEmailVerify,
      isShowServiceAgreement,
      handleLoginRedirect,
    ],
  );

  const showResetPassword = useCallback((data) => {
    setResetPasswordUser(data);
    setShowResetPasswordModal(true);
  }, []);

  const handleVerifySuccess = useCallback(
    (redirectUrl) => {
      if (isShowServiceAgreement(currentUser)) {
        setShowUserAgreement(true);
        setShowUserAgreementFooter(true);
      } else {
        handleLoginRedirect(redirectUrl);
      }
    },
    [currentUser, isShowServiceAgreement, handleLoginRedirect],
  );

  const handleUserAgreementSuccess = useCallback(
    async ({ id, data }) => {
      try {
        await updateUserServiceAgreement(id, data);
        handleLoginRedirect();
      } catch (error) {
        console.error(error);
      }
    },
    [handleLoginRedirect],
  );

  const checkHandleLogin = useCallback(
    async (redirectUrl) => {
      const userInfo = await getUserInfoData();
      if (userInfo) {
        handleLoginSuccess(userInfo, redirectUrl);
      }
    },
    [getUserInfoData, handleLoginSuccess],
  );

  useEffect(() => {
    checkHandleLogin();
  }, [checkHandleLogin]);

  return (
    <div className="vip-login">
      <div className="vip-login-container">
        <div className="nav-line-active" style={navLineActivedStyle} />
        <Tabs
          activeKey={activeKey}
          className="vip-login-tabs"
          onSelect={(activeKey) => setActiveKey(activeKey)}>
          {VIP_TAB_DATA.map((item) => (
            <Tab eventKey={item.key} title={item.title} key={item.key}>
              <VipTabContent
                formGroup={item.formGroup}
                subAccountEnable={item.subAccountEnable}
                canLoginBySmsCode={canLoginBySmsCode}
                currentUser={currentUser}
                checkHandleLogin={checkHandleLogin}
                showResetPassword={showResetPassword}
                setShowUserAgreement={setShowUserAgreement}
              />
            </Tab>
          ))}
        </Tabs>
      </div>
      <UserAgreement
        show={showUserAgreement}
        onHide={() => setShowUserAgreement(false)}
        isShowFooter={showUserAgreementFooter}
        onSubmit={() =>
          handleUserAgreementSuccess({
            id: currentUser.id,
            data: {
              show_service_agreement: false,
            },
          })
        }
        agreementList={AUTODOC_VIP_AGREEMENT_LIST}
      />
      {showEmailVerifyModal && (
        <EmailVerifyModal
          onHide={() => setShowEmailVerifyModal(false)}
          currentUser={currentUser}
          invalidEmailList={invalidEmailList}
          handleVerifySuccess={handleVerifySuccess}
        />
      )}
      {showSmsVerifyModal && (
        <SmsVerifyModal
          onHide={() => setShowSmsVerifyModal(false)}
          currentUser={currentUser}
          handleVerifySuccess={handleVerifySuccess}
        />
      )}
      {showResetPasswordModal && (
        <ResetPasswordModal
          onHide={() => setShowResetPasswordModal(false)}
          resetPasswordUser={resetPasswordUser}
        />
      )}
    </div>
  );
};

VIPLogin.propTypes = {
  handleLogin: PropTypes.func.isRequired,
};

export default VIPLogin;
