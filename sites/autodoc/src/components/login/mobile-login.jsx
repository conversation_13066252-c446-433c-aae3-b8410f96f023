import React from 'react';
import { getEnvVariables } from '../../../../../common/utils/env';
import ConnectBtn from '../../../../../common/components/connect/connectButton/connect-btn';

const MobileLogin = () => {
  const { isVipMode } = getEnvVariables();

  return (
    <div className="scan-login mobile-login">
      <div className="login-inner">
        <h5 className="login-title">
          {!isVipMode ? 'Web端扫码登录' : 'Web端登录'}
        </h5>
        {!isVipMode ? (
          <p className="login-subtitle">
            微信扫码成功后，点击“<span>关注公众号</span>”即可登录
          </p>
        ) : (
          <ConnectBtn buttonTitle="申请试用" />
        )}
        <p className="login-hint">请您在电脑端Google Chrome浏览器中使用</p>
      </div>
    </div>
  );
};

export default MobileLogin;
