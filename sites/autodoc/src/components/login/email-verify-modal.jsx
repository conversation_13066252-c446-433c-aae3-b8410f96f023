import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Form, InputGroup } from 'react-bootstrap';
import { Formik } from 'formik';
import * as yup from 'yup';
import { autoDocPostVerifyEmail, autoDocPostVerifyCode } from '../../api/index';
import { useCountdown } from '../../../../../common/hooks/useCountdownHook';
import { REGEXP_LIST } from '../../../../../common/data/regular-expression';
import classnames from 'classnames';

const EmailVerifyModal = ({
  title,
  show,
  onHide,
  currentUser,
  invalidEmailList,
  handleVerifySuccess,
}) => {
  const [errorInvalidEmail, setErrorInvalidEmail] = useState('');
  const [errorInvalidVerifyCode, setErrorInvalidVerifyCode] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [sendCodeLoading, setSendCodeLoading] = useState(false);

  const emailSuffix = currentUser && currentUser.email_server;

  const { startCountdown, stopCountdown, countdown, isCountdowning } =
    useCountdown(60);

  const emailVerifyScheme = yup.object().shape({
    verifyCode: yup.string().trim().required('请输入验证码'),
    email: yup
      .string()
      .trim()
      .test('checkEmail', '请输入邮箱地址', function (value) {
        let emailSuffix = null;
        const emailList = invalidEmailList.map((item) => `@${item}`);
        if (currentUser) {
          const hasEmailServer = currentUser.email_server;
          if (!!value) {
            if (hasEmailServer) {
              emailSuffix = currentUser.email_server;
              if (value.includes('@')) {
                return this.createError({
                  message: '请勿输入邮箱后缀',
                });
              }
            } else {
              if (value.includes('@')) {
                const index = value.indexOf('@');
                emailSuffix = value.slice(index);
              }
            }
          }
          if (!value) {
            return false;
          } else if (!hasEmailServer && !REGEXP_LIST.EMAIL.test(value)) {
            return this.createError({
              message: '您输入的邮箱格式有误，请重新输入',
            });
          } else {
            if (!hasEmailServer && emailList.includes(emailSuffix)) {
              return this.createError({
                message: '请输入您的公司邮箱后缀',
              });
            } else {
              return true;
            }
          }
        }
      }),
  });

  const handleVerifyRequest = useCallback(
    async (value, errors) => {
      setErrorMsg('');
      setSuccessMsg('');
      if (errors.email) return;
      let email = value.email;
      if (!email) {
        setErrorInvalidEmail('请输入邮箱地址');
        return;
      }
      if (currentUser.email_server) {
        email = `${value.email}${currentUser.email_server}`;
      }
      const data = {
        email_address: email,
      };
      try {
        setSendCodeLoading(true);
        const res = await autoDocPostVerifyEmail(data);
        setSuccessMsg(res.message || '验证码已发送到您的邮箱，请注意查收');
        startCountdown();
      } catch (error) {
        console.error(error);
        if (error.message === 'retry later') {
          setErrorMsg('验证码已发送，请1分钟后重试');
        } else {
          setErrorMsg('验证码发送失败');
        }
      } finally {
        setSendCodeLoading(false);
      }
    },
    [currentUser, startCountdown],
  );

  const handleValidateVerifyForm = async (value) => {
    try {
      setErrorMsg('');
      setSuccessMsg('');
      const data = {
        verify_code: value.verifyCode,
      };
      const resp = await autoDocPostVerifyCode(data);
      const redirectUrl = resp.data?.redirect_url;
      onHideModal();
      handleVerifySuccess(redirectUrl);
    } catch (error) {
      setErrorInvalidVerifyCode('验证码有误，请重新输入');
      return null;
    }
  };

  const onHideModal = () => {
    onHide();
    setErrorMsg('');
    setSuccessMsg('');
    setErrorInvalidEmail('');
    setErrorInvalidVerifyCode('');
    stopCountdown();
  };

  return (
    <Modal
      show={show}
      dialogClassName="verify-modal email-verify-modal"
      onHide={() => onHideModal()}>
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="form-tip">
          为保证您的账户安全，请通过公司邮箱获取验证码
        </div>
        <Formik
          initialValues={{ email: '', verifyCode: '' }}
          validationSchema={emailVerifyScheme}
          onSubmit={(values) => {
            handleValidateVerifyForm(values);
          }}>
          {({
            values,
            errors,
            touched,
            handleSubmit,
            handleChange,
            handleBlur,
          }) => (
            <Form onSubmit={handleSubmit} className="verify-form">
              <Form.Group className="verify-form-number verify-form-email">
                <Form.Label>公司邮箱：</Form.Label>
                <InputGroup>
                  <Form.Control
                    className={classnames({
                      'form-control-suffix': !emailSuffix,
                    })}
                    type="text"
                    name="email"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.email}
                    onInput={() => {
                      setErrorInvalidEmail('');
                    }}
                    isValid={
                      touched.email && !errors.email && !errorInvalidEmail
                    }
                    isInvalid={
                      errorInvalidEmail ? true : touched.email && !!errors.email
                    }
                  />
                  {emailSuffix && (
                    <InputGroup.Text>
                      <span title={emailSuffix}>{emailSuffix}</span>
                    </InputGroup.Text>
                  )}
                  <Form.Control.Feedback type="invalid">
                    {errorInvalidEmail ? errorInvalidEmail : errors.email}
                  </Form.Control.Feedback>
                </InputGroup>
              </Form.Group>
              <Form.Group className="verify-form-code">
                <Form.Label>验证码：</Form.Label>
                <Form.Control
                  type="text"
                  name="verifyCode"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.verifyCode}
                  onInput={() => {
                    setErrorInvalidVerifyCode('');
                  }}
                  isInvalid={
                    errorInvalidVerifyCode
                      ? true
                      : touched.verifyCode && !!errors.verifyCode
                  }
                />
                <Form.Control.Feedback type="invalid">
                  {errorInvalidVerifyCode
                    ? errorInvalidVerifyCode
                    : errors.verifyCode}
                </Form.Control.Feedback>
                <button
                  type="button"
                  className="default-btn verify-code-btn"
                  disabled={isCountdowning || sendCodeLoading}
                  onClick={() => {
                    handleVerifyRequest(values, errors);
                  }}>
                  {sendCodeLoading
                    ? '发送中...'
                    : isCountdowning
                      ? `${countdown}s后重新发送`
                      : '发送验证码'}
                </button>
              </Form.Group>
              {errorMsg ? <p className="error-message">{errorMsg}</p> : null}
              {successMsg ? (
                <p className="success-message">{successMsg}</p>
              ) : null}
              <div className="form-button">
                <button type="submit" className="default-btn verify-btn">
                  验证
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </Modal.Body>
    </Modal>
  );
};

EmailVerifyModal.defaultProps = {
  title: '登录验证',
  show: true,
};

EmailVerifyModal.propTypes = {
  title: PropTypes.string,
  show: PropTypes.bool,
  onHide: PropTypes.func,
  currentUser: PropTypes.object,
  invalidEmailList: PropTypes.array,
  handleVerifySuccess: PropTypes.func,
};

export default EmailVerifyModal;
