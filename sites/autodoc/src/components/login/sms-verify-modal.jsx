import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Modal, Form } from 'react-bootstrap';
import { withFormik } from 'formik';
import * as yup from 'yup';
import {
  vipLoginBySmsCode,
  vipChildLoginBySmsCode,
  AutodocSmsToken,
  getSmsCode,
} from '../../api/index';
import { useCountdown } from '../../../../../common/hooks/useCountdownHook';
import { REGEXP_LIST } from '../../../../../common/data/regular-expression';

const SmsVerifyFormList = (props) => {
  const {
    title,
    show,
    onHide,
    currentUser,
    values,
    errors,
    touched,
    handleSubmit,
    handleChange,
    handleBlur,
    setFieldError,
    isSubmitting,
  } = props;

  const { startCountdown, stopCountdown, countdown, isCountdowning } =
    useCountdown(60);
  const [sendSmsCodeLoading, setSendSmsCodeLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');

  const phoneSchemaEmail = yup.object().shape({
    phone: yup
      .string()
      .trim()
      .matches(REGEXP_LIST.AUTODOC_PHONE, '手机号码格式不正确，请检查')
      .required('手机号码不能为空'),
  });

  const validatePhone = async (phone) => {
    try {
      await phoneSchemaEmail.validate({ phone });
      return true;
    } catch (e) {
      setFieldError('phone', e.message);
      return false;
    }
  };

  const sendSmsCodeToPhone = async () => {
    try {
      setErrorMsg('');
      setSuccessMsg('');
      const isValidatedPhone = await validatePhone(values.phone);
      if (isValidatedPhone) {
        setSendSmsCodeLoading(true);
        const { data } = await AutodocSmsToken();
        let params = {
          username: currentUser.username,
          phone_number: values.phone,
          ignore_phone_check: true,
          csrf_token: data.csrf_token,
        };
        if (currentUser.parent) {
          params.parent_username = currentUser.parent.username;
        }
        const res = await getSmsCode(params);
        setSuccessMsg(res.message || '短信验证码已发送到您的手机，请注意查收');
        startCountdown();
      }
    } catch (error) {
      setErrorMsg(error.message);
      console.error(error);
    } finally {
      setSendSmsCodeLoading(false);
    }
  };

  useEffect(() => {
    if (isSubmitting) {
      setErrorMsg('');
      setSuccessMsg('');
    }
  }, [isSubmitting]);

  const onHideModal = () => {
    setErrorMsg('');
    setSuccessMsg('');
    stopCountdown();
    onHide();
  };

  return (
    <Modal
      show={show}
      dialogClassName="verify-modal sms-verify-modal"
      onHide={() => onHideModal()}>
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form className="verify-form" onSubmit={handleSubmit}>
          <Form.Group className="verify-form-number verify-form-phone">
            <Form.Label bsPrefix="bs-label" className="form-label">
              手机号：
            </Form.Label>
            <Form.Control
              type="text"
              name="phone"
              placeholder="请输入您的手机号"
              value={values.phone}
              isValid={touched.phone && !errors.phone}
              isInvalid={touched.phone && !!errors.phone}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            <Form.Control.Feedback type="invalid">
              {errors.phone}
            </Form.Control.Feedback>
          </Form.Group>
          <Form.Group className="verify-form-code">
            <Form.Label>验证码：</Form.Label>
            <Form.Control
              type="text"
              name="smsCode"
              placeholder="请输入您的短信验证码"
              value={values.smsCode}
              isValid={touched.smsCode && !errors.smsCode}
              isInvalid={touched.smsCode && !!errors.smsCode}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            <Form.Control.Feedback type="invalid">
              {errors.smsCode}
            </Form.Control.Feedback>
            <button
              type="button"
              className="default-btn verify-code-btn"
              disabled={isCountdowning || sendSmsCodeLoading}
              onClick={() => {
                sendSmsCodeToPhone();
              }}>
              {sendSmsCodeLoading
                ? '发送中...'
                : isCountdowning
                  ? `${countdown}s后重新发送`
                  : '发送验证码'}
            </button>
          </Form.Group>
          {errorMsg ? <p className="error-message">{errorMsg}</p> : null}
          {successMsg ? <p className="success-message">{successMsg}</p> : null}
          <div className="form-button">
            <button type="submit" className="default-btn verify-btn">
              验证
            </button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

const SmsVerifyModal = withFormik({
  mapPropsToValues: () => {
    const defaultValues = {
      phone: '',
      smsCode: '',
    };
    return defaultValues;
  },
  handleSubmit: async (values, formikBag) => {
    const {
      props: { currentUser, handleVerifySuccess },
      setFieldError,
    } = formikBag;
    try {
      const params = {
        phone_number: values.phone,
        sms_code: values.smsCode,
        username: currentUser.username,
      };
      let resp = null;
      if (currentUser.parent) {
        params.parent_username = currentUser.parent.username;
        resp = await vipChildLoginBySmsCode(params);
      } else {
        resp = await vipLoginBySmsCode(params);
      }
      const redirectUrl = resp.data?.redirect_url;
      handleVerifySuccess(redirectUrl);
    } catch (error) {
      setFieldError('smsCode', error.message);
      console.error(error);
    }
  },
  validationSchema: () => {
    return yup.object().shape({
      phone: yup
        .string()
        .trim()
        .matches(REGEXP_LIST.AUTODOC_PHONE, '手机号码格式不正确，请检查')
        .required('手机号码不能为空'),
      smsCode: yup.string().trim().required('请输入验证码'),
    });
  },
})(SmsVerifyFormList);

SmsVerifyModal.defaultProps = {
  title: '短信验证',
  show: true,
};

SmsVerifyModal.propTypes = {
  title: PropTypes.string,
  show: PropTypes.bool,
  onHide: PropTypes.func,
  currentUser: PropTypes.object,
  handleVerifySuccess: PropTypes.func,
};

export default SmsVerifyModal;
