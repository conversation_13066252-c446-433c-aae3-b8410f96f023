import React, { Fragment } from 'react';
import { Container } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { PROJECT_TYPES_DATA } from '../data/data';
import _ from 'lodash';

const ProjectTypes = () => {
  const {
    allFile: { nodes },
  } = useStaticQuery(query);

  return (
    <section className="project-types-wrapper">
      <Container>
        <h3 className="project-title">多种金融文档类型，一网打尽</h3>
        <div className="project-list">
          {PROJECT_TYPES_DATA.map((projectTypeList, projectTypeListIndex) => (
            <div className="project-group" key={projectTypeListIndex}>
              {projectTypeList.map((projectType, projectTypeIndex) => (
                <Fragment key={projectTypeIndex}>
                  {projectTypeIndex > 0 && (
                    <svg width="1" height="91">
                      <line
                        x1="0"
                        y1="0"
                        x2="1"
                        y2="91"
                        stroke="#7a8ba6"
                        strokeDasharray="4"
                      />
                    </svg>
                  )}
                  <div className="project-type">
                    {projectTypeListIndex < PROJECT_TYPES_DATA.length - 1 && (
                      <span className="type-name">{projectType.type}</span>
                    )}
                    {projectTypeListIndex === PROJECT_TYPES_DATA.length - 1 && (
                      <span className="type-name type-name-other">
                        {projectType.type}
                      </span>
                    )}
                    <div className="type-items">
                      {_.sortBy(
                        nodes.filter((node) =>
                          projectType.items.includes(node.name),
                        ),
                        [(node) => projectType.items.indexOf(node.name)],
                      ).map((node, index) => (
                        <Fragment key={index}>
                          <div className="type-item">
                            <div className="type-item-logo">
                              <img src={node.publicURL} alt={node.name} />
                            </div>
                            <span className="type-item-name">{node.name}</span>
                          </div>
                          {projectTypeListIndex ===
                            PROJECT_TYPES_DATA.length - 1 &&
                            index === 1 && (
                              <span className="type-name other-middle-name">
                                {projectType.type}
                              </span>
                            )}
                        </Fragment>
                      ))}
                    </div>
                  </div>
                </Fragment>
              ))}
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
};
const query = graphql`
  {
    allFile(
      filter: { relativePath: { regex: "/project/" } }
      sort: { name: ASC }
    ) {
      nodes {
        publicURL
        name
      }
    }
  }
`;

export default ProjectTypes;
