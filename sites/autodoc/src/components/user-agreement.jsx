import React, { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Form, Modal, OverlayTrigger, Tooltip } from 'react-bootstrap';
import '../styles/agreement.less';

const UserAgreement = ({
  title,
  isShowFooter,
  show,
  onSubmit,
  onHide,
  agreementList,
}) => {
  const checkbox = useRef();
  const [isChecked, setChecked] = useState(false);
  return (
    <Modal
      show={show}
      dialogClassName="agreement-modal"
      onHide={() => {
        if (isShowFooter) return;
        onHide();
      }}>
      <Modal.Header closeButton={!isShowFooter}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="service-content">
          <p className="content-desc">
            感谢您使用
            AutoDoc智能复核系统，为了保障您的使用权益，请您认真阅读以下内容：
          </p>
          {agreementList &&
            agreementList.map((item, index) => (
              <section key={index}>
                <p className="content-title">{item.title}</p>
                <p className="content-desc">{item.desc}</p>
              </section>
            ))}
        </div>
      </Modal.Body>
      {isShowFooter && (
        <Modal.Footer>
          <div className="checkbox-wrapper">
            <OverlayTrigger
              placement="bottom"
              overlay={
                <Tooltip className="agreement-tooltip" id="tooltip">
                  请勾选
                </Tooltip>
              }>
              <div>
                <Form.Control
                  ref={checkbox}
                  type="checkbox"
                  className="agreement-checkbox"
                  onChange={() => {
                    setChecked(checkbox.current.checked);
                  }}
                />
              </div>
            </OverlayTrigger>
            <span className="text">
              点击勾选即代表您已知悉并同意该产品的服务协议
            </span>
          </div>
          <div className="button-wrapper">
            <button
              className="default-btn agree-btn"
              disabled={!isChecked}
              onClick={() => {
                onSubmit();
                setChecked(false);
              }}>
              同意，立即使用系统
            </button>
          </div>
        </Modal.Footer>
      )}
    </Modal>
  );
};

UserAgreement.defaultProps = {
  title: '用户协议',
  isShowFooter: false,
  show: false,
};

UserAgreement.propTypes = {
  title: PropTypes.string,
  isShowFooter: PropTypes.bool,
  show: PropTypes.bool,
  onSubmit: PropTypes.func,
  onHide: PropTypes.func.isRequired,
  agreementList: PropTypes.arrayOf(PropTypes.object),
};

export default UserAgreement;
