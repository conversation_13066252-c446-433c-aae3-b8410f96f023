import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Container, Navbar } from 'react-bootstrap';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage } from 'gatsby-plugin-image';
import Navs from './navs';
import ScanLogin from './login/scan-login';
import VIPLogin from './login/vip-login';
import MobileLogin from './login/mobile-login';
import introBannerPng from '../assets/images/intro-banner.png';
import nextIntroBannerPng from '../assets/images/next-intro-banner.png';
import mobileIntroBannerPng from '../assets/images/mobile-intro-banner.png';
import mobileIntroBannerNextPng from '../assets/images/mobile-intro-banner-next.png';
import MenuMobileSvg from '../assets/icons/menu-mobile.svg';
import CloseSvg from '../assets/icons/close.svg';
import { getEnvVariables } from '../../../../common/utils/env';
import { useMobilePageContext } from '../../../../common/hooks/useMobilePageContext';
import classnames from 'classnames';

const Header = ({ handleLogin }) => {
  const [collapseOpen, setCollapseOpen] = useState(false);
  const { logo } = useStaticQuery(query);
  const isMobile = useMobilePageContext();
  const { isVipMode, isNextSite } = getEnvVariables();
  const handleNavClick = () => {
    setCollapseOpen(false);
  };

  return (
    <section
      className={classnames({
        'header-wrapper': true,
        'next-header-wrapper': isNextSite,
      })}>
      <Container>
        <Navbar className="header" expand="md">
          <Navbar.Brand>
            <GatsbyImage
              image={logo.childImageSharp.gatsbyImageData}
              alt="logo"
              className="header-logo"
            />
          </Navbar.Brand>
          <Navbar.Toggle
            aria-controls="basic-navbar-nav"
            onClick={() => setCollapseOpen(!collapseOpen)}>
            {collapseOpen ? <CloseSvg /> : <MenuMobileSvg />}
          </Navbar.Toggle>
          <Navbar.Collapse
            id="basic-navbar-nav"
            in={collapseOpen}
            onClick={handleNavClick}>
            <Navs handleNavClick={handleNavClick} />
          </Navbar.Collapse>
        </Navbar>
        {isMobile ? (
          <div className="banner-content-mobile">
            {isNextSite ? (
              <img
                src={mobileIntroBannerNextPng}
                className="banner-intro-mobile"
                alt="banner-intro-mobile"
              />
            ) : (
              <img
                src={mobileIntroBannerPng}
                className="banner-intro-mobile"
                alt="banner-intro-mobile"
              />
            )}
            <MobileLogin />
          </div>
        ) : (
          <div className="banner-content">
            {isNextSite ? (
              <img
                src={nextIntroBannerPng}
                className="banner-intro banner-intro-next"
                alt="banner-intro"
              />
            ) : (
              <img
                src={introBannerPng}
                className="banner-intro"
                alt="banner-intro"
              />
            )}
            {!isVipMode ? (
              <ScanLogin handleLogin={handleLogin} />
            ) : (
              <VIPLogin handleLogin={handleLogin} />
            )}
          </div>
        )}
      </Container>
    </section>
  );
};

const query = graphql`
  {
    logo: file(absolutePath: { regex: "/images/logo.png/" }) {
      childImageSharp {
        gatsbyImageData(layout: FIXED)
      }
      name
    }
  }
`;

Header.propTypes = {
  handleLogin: PropTypes.func,
};

export default Header;
