import axios from 'axios';
import qs from 'qs';
import Crypt from 'cryptjs';
import { bufferToUnit8Array } from './crypt-tool';

export const baseURL = '/api/v1';
/**
 * 创建axios实例
 */
const instance = axios.create({
  timeout: 10000,
  baseURL: baseURL,
});

let encryptData, binaryKey;
/**
 * 请求拦截器
 */
instance.interceptors.request.use(
  (config) => {
    if (
      config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
    ) {
      config.transformRequest = (data) => qs.stringify(data);
    }
    if (encryptData !== undefined) {
      if (encryptData) {
        let packageEncrypt = new Crypt(binaryKey);
        if (config.responseType === undefined) {
          config.responseType = 'arraybuffer';
          if (!(config.data instanceof FormData)) {
            config.data = packageEncrypt.encryptJson(config.data);
          }
        }
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

instance.interceptors.response.use(
  (response) => {
    let configs;
    if (response.config.url === '/config/feature') {
      configs = bufferToUnit8Array(response.data, true);
      encryptData = configs.data.binary_json;
      binaryKey = configs.data.binary_key;
      response.data = configs;
    } else if (encryptData) {
      response.data = bufferToUnit8Array(response.data, false, binaryKey);
    }
    if (response.data.status === 'error') {
      return Promise.reject(response.data);
    }
    return response.data;
  },
  (error) => {
    const { response } = error;
    if (!response) {
      return Promise.reject({ type: 'http', message: '未知错误' });
    }
    if (response.status >= 500) {
      return Promise.reject({ message: '服务器错误' });
    }
    if (response.status === 400) {
      if (encryptData) {
        response.data = bufferToUnit8Array(response.data, false, binaryKey);
      }
      return Promise.reject(response.data);
    }
    if (response.status === 418) {
      return Promise.reject(response);
    }
    return Promise.reject(response.data);
  },
);

export default instance;
