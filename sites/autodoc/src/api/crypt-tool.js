import Crypt from 'cryptjs';

const handshakeEncrypt = new Crypt('0b168d3bb0828b5f6242cb3a9f144a23');

export function bufferToUnit8Array(buffer, handshake = false, key) {
  let ints = new Uint8Array(buffer);
  if (handshake) {
    return handshakeEncrypt.decryptJson(ints);
  } else {
    let packageEncrypt = new Crypt(key);
    return packageEncrypt.decryptJson(ints);
  }
}

export function arrayBufferToUnit8Array(rawResp, handshake = false) {
  return new Promise((resolve, reject) => {
    rawResp
      .arrayBuffer()
      .then(function (buffer) {
        resolve(bufferToUnit8Array(buffer, handshake));
      })
      .catch((err) => {
        reject(err);
      });
  });
}
