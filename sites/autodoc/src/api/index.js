import http from './http';

export const AutodocSmsToken = () => http.get('/csrf_token');

export function getConfigFile() {
  return http.get('/config/feature', { responseType: 'arraybuffer' });
}

export const autoDocGetUserInfo = () => http.get('/user/me');

export const autoDocVipLogin = (data) => http.post('/user/login/vip', data);

export const queryVipUserLoginMode = (params) =>
  http.get('/user/vip-parent', { params });

export const updateUserServiceAgreement = (id, data) =>
  http.post(`/users/${id}/service-agreement`, data);

export const autoDocVipChildLogin = (data) =>
  http.post('/user/login/vip-child', data);

export const vipLoginBySmsCode = (data) =>
  http.post('/user/phone-login/vip', data);

export const vipChildLoginBySmsCode = (data) =>
  http.post('/user/phone-login/vip-child', data);

export const getSmsCode = (data) => http.post('/sms-code', data);

export const getInvalidEmails = () => http.get('/user/invalid-emails');

export const autoDocPostVerifyCode = (data) =>
  http.post('/user/email/verify', data);

export const autoDocResetPassword = (data) => http.put('/user/password', data);

export const autoDocPostVerifyEmail = (data) =>
  http.post('/user/email/verify-send', data);

export const getWechatLoginUserInfo = () => http.get('/user/info');

export const getWechatLoginCode = (params) =>
  http.get('/subscribe-login-qrcode', {
    params,
  });

export const getWechatLoginStatus = (params) =>
  http.get('/check-login', { params });

export const getFaultWordResult = (data) =>
  http.post('/adc/faulty-wording', data, { timeout: 30e3 });
