import { SCREEN_SIZE_WIDTH } from '../../../../common/data/constant';
const LEFT_ELEMENT_SHORT_LINE_DISTANCE = 5;
const LEFT_ELEMENT_SHORT_LINE_DISTANCE_MOBILE = 6.5;

const getLeftPoint = (el, elRect) => {
  let elWordWidth = parseInt(getComputedStyle(el)['fontSize']);
  const elLineHeight = parseInt(getComputedStyle(el)['lineHeight']);
  const elLetterSpacing = parseInt(getComputedStyle(el)['letterSpacing']);
  if (elLetterSpacing) {
    elWordWidth += elLetterSpacing;
  }

  const range = document.createRange();
  range.selectNodeContents(el.firstChild);
  const elFirstChildRect = range.getBoundingClientRect();

  const elFirstChildRows = Math.ceil(elFirstChildRect.height / elLineHeight);
  const elAllRows = Math.ceil(elRect.height / elLineHeight);
  const elMiddleRows = Math.ceil(elAllRows / 2);

  const textArray = el.textContent.split('\n');
  const firstTextWordsLength = textArray[0].length * elWordWidth;
  const middleOfFirstRowLastWord = elRect.left + elRect.width - elWordWidth / 2;
  const elWordHeight = elRect.height - (elAllRows - 1) * elLineHeight;

  let leftX = elRect.left + elRect.width / 2;
  let leftY = elRect.top + elWordHeight;
  // textArray.length为1时表示错别字未折行，不为1时表示错别字有折行，即通过"\n"折行。
  if (textArray.length === 1) {
    // elTextAllRows表示该错别字因span大小自动折行的行数，但该错别字中未出现"\n"。
    if (elAllRows === 2) {
      leftX = middleOfFirstRowLastWord;
    } else {
      leftY = elRect.top + (elMiddleRows - 1) * elLineHeight + elWordHeight;
    }
  } else {
    // firstChildRows为1并且第一个折行的位置小于元素位置时表示错别字在第一行折行，则连线位置在第一行错别字居中位置。
    // 其他情况折行时，则连线位置在第一行错别字末尾位置。
    if (
      elFirstChildRows === 1 &&
      elFirstChildRect.left + firstTextWordsLength <= elRect.left + elRect.width
    ) {
      leftX = elFirstChildRect.left + firstTextWordsLength / 2;
    } else {
      leftX = middleOfFirstRowLastWord;
    }
  }
  return [leftX, leftY, elAllRows, elMiddleRows];
};

const getRightPoint = (leftElementRect, rightElementRect) => {
  let rightX;
  let rightY;
  if (getIsMiniScreenSize()) {
    rightX = rightElementRect.left + rightElementRect.width / 2;
    if (leftElementRect.top > rightElementRect.top) {
      rightY = rightElementRect.top + rightElementRect.height;
    } else {
      rightY = rightElementRect.top;
    }
  } else {
    rightX = rightElementRect.left;
    rightY = rightElementRect.top + rightElementRect.height / 2;
  }
  return [rightX, rightY];
};

const getBreakPoint = (rightElementRect, leftX, rightX) => {
  if (getIsMiniScreenSize()) {
    let breakPoint;
    if (leftX > rightX) {
      breakPoint = rightX + 5;
    } else if (leftX < rightX) {
      breakPoint = rightX - 5;
    } else {
      breakPoint = rightX;
    }
    return [breakPoint];
  } else {
    const containerRight = document?.querySelector('.container-right');
    if (containerRight) {
      const containerRightPaddingLeft = parseInt(
        getComputedStyle(containerRight)['paddingLeft'],
      );
      return [rightElementRect.left - containerRightPaddingLeft];
    }
  }
};

export const getPointsOfConnectLine = (
  leftElement,
  rightElement,
  windowScrollDistance,
) => {
  const leftElementRect = leftElement.getBoundingClientRect();
  const rightElementRect = rightElement.getBoundingClientRect();
  const leftPoint = getLeftPoint(leftElement, leftElementRect);
  const rightPoint = getRightPoint(leftElementRect, rightElementRect);
  const leftX = leftPoint[0];
  const leftY = leftPoint[1];
  const rightX = rightPoint[0];
  const rightY = rightPoint[1];
  let shortLineY;
  const breakPoint = getBreakPoint(rightElementRect, leftX, rightX);
  if (getIsMiniScreenSize()) {
    shortLineY = leftPoint[1] + LEFT_ELEMENT_SHORT_LINE_DISTANCE_MOBILE;
  } else {
    shortLineY = leftPoint[1] + LEFT_ELEMENT_SHORT_LINE_DISTANCE;
  }
  let points;
  if (windowScrollDistance) {
    points = `
    ${leftX},${leftY + windowScrollDistance}
    ${leftX},${shortLineY + windowScrollDistance}
    ${breakPoint},${shortLineY + windowScrollDistance}
    ${rightX},${rightY + windowScrollDistance}`;
  } else {
    points = `
    ${leftX},${leftY}
    ${leftX},${shortLineY}
    ${breakPoint},${shortLineY}
    ${rightX},${rightY}`;
  }
  return points;
};

export const drawConnectLine = () => {
  const xmlns = 'http://www.w3.org/2000/svg';

  const svgElem = document.createElementNS(xmlns, 'svg');
  svgElem.classList.add('typo-connect-line');

  const polyline = document.createElementNS(xmlns, 'polyline');
  polyline.setAttribute('fill', 'transparent');
  polyline.setAttribute('stroke', '#FF5454');
  polyline.setAttribute('stroke-dasharray', '2 2');
  polyline.setAttribute('stroke-width', '1');
  polyline.classList.add('collection-polyline');
  svgElem.appendChild(polyline);

  const svgContainer = document.body;
  svgContainer.appendChild(svgElem);
};

const isElementInViewport = (element) => {
  if (element.parentNode) {
    const elementRect = element.getBoundingClientRect();
    const containerRect = element.parentNode.getBoundingClientRect();
    if (getIsMiniScreenSize()) {
      return (
        elementRect.top + elementRect.height >= containerRect.top &&
        elementRect.bottom <= containerRect.bottom
      );
    } else {
      return (
        elementRect.top + elementRect.height / 2 >= containerRect.top &&
        elementRect.bottom - elementRect.height / 2 <= containerRect.bottom
      );
    }
  } else {
    return false;
  }
};

export const getShowConnectLine = (leftElement, rightElement) => {
  if (leftElement && rightElement) {
    return (
      isElementInViewport(leftElement) && isElementInViewport(rightElement)
    );
  } else {
    return false;
  }
};

export const getPosition = (leftElement, rightElement) => {
  const leftElementLineHeight = parseInt(
    getComputedStyle(leftElement)['lineHeight'],
  );
  const leftElementRect = leftElement.getBoundingClientRect();
  const rightElementRect = rightElement.getBoundingClientRect();
  const containerRect = leftElement.parentNode.getBoundingClientRect();
  const leftPoint = getLeftPoint(leftElement, leftElementRect);
  const leftElementAllRows = leftPoint[2];
  const leftElementMiddleRows = leftPoint[3];
  let positionTop =
    leftPoint[1] -
    containerRect.top +
    (leftElementLineHeight / 2) * leftElementAllRows;
  let positionLeft =
    leftPoint[0] - leftElementRect.width / 2 - containerRect.left;
  let positionRight = 0;

  if (
    leftPoint[1] +
      rightElementRect.height +
      leftElementLineHeight * leftElementMiddleRows >=
    containerRect.height + containerRect.top
  ) {
    positionTop =
      leftPoint[1] -
      containerRect.top -
      leftElementLineHeight * leftElementMiddleRows -
      rightElementRect.height;
  }
  if (rightElementRect.width + positionLeft + 20 >= containerRect.width) {
    return [positionTop, , positionRight + 20];
  }
  return [positionTop, positionLeft, positionRight];
};

export const getIsMiniScreenSize = () => {
  if (window.innerWidth <= SCREEN_SIZE_WIDTH.MINI_SCREEN_SIZE) {
    return true;
  } else {
    return false;
  }
};
