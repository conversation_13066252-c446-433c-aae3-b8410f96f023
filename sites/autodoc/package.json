{"name": "autodoc", "version": "0.1.0", "scripts": {"develop": "cross-env SITE_ENV=test gatsby develop", "develop-vip": "cross-env SITE_ENV=test-vip gatsby develop", "develop-next": "cross-env SITE_ENV=test-next gatsby develop", "build": "gatsby clean && cross-env SITE_ENV=prod gatsby build", "build-vip": "gatsby clean && cross-env SITE_ENV=prod-vip gatsby build", "build-next": "gatsby clean && cross-env SITE_ENV=prod-next gatsby build", "build-test": "gatsby clean && cross-env SITE_ENV=test gatsby build", "build-vip-test": "gatsby clean && cross-env SITE_ENV=test-vip gatsby build", "build-next-test": "gatsby clean && cross-env SITE_ENV=test-next gatsby build", "clean": "gatsby clean", "lint": "eslint --fix --ext .js,.jsx src/"}, "dependencies": {"@emotion/react": "^11.10.6", "@reach/router": "^1.3.4", "aes-js": "^3.1.2", "bootstrap": "^5.2.3", "classnames": "^2.3.2", "formik": "^2.2.9", "gatsby": "^5.8.0", "gatsby-plugin-baidu-analytics": "^2.2.0", "gatsby-plugin-clarity": "^1.0.1", "gatsby-plugin-copy-files": "^1.1.0", "gatsby-plugin-google-analytics": "^5.8.0", "gatsby-plugin-google-gtag": "^4.25.0", "gatsby-plugin-image": "^3.8.0", "gatsby-plugin-less": "^7.8.0 ", "gatsby-plugin-manifest": "^5.8.0", "gatsby-plugin-no-sourcemaps": "^5.8.0", "gatsby-plugin-offline": "^6.8.0", "gatsby-plugin-page-creator": "^5.8.0", "gatsby-plugin-polyfill-io": "^1.1.0", "gatsby-plugin-postcss": "^6.8.0", "gatsby-plugin-react-svg": "^3.3.0", "gatsby-plugin-remove-serviceworker": "^1.0.0", "gatsby-plugin-robots-txt": "^1.8.0", "gatsby-plugin-sharp": "^5.8.0", "gatsby-plugin-sitemap": "^ 6.8.0 ", "gatsby-remark-external-links": "^0.0.4", "gatsby-remark-highlight-code": "^3.3.0", "gatsby-remark-images": "^7.8.0 ", "gatsby-source-filesystem": "^5.8.0", "gatsby-source-local-git": "^1.3.0", "gatsby-source-strapi": "^3.1.3", "gatsby-transformer-json": "^5.8.0", "gatsby-transformer-remark": "^6.8.0", "gatsby-transformer-sharp": "^5.8.0", "less": "^4.1.3", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-bootstrap": "^2.7.2"}, "devDependencies": {"@paoding/prettier-config": "^1.0.1", "cross-env": "^7.0.3", "vconsole": "^3.15.0"}}