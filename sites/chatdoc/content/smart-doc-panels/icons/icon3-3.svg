<svg width="89" height="89" viewBox="0 0 89 89" fill="none" xmlns="http://www.w3.org/2000/svg">
	<rect x="0.5" y="0.5" width="88" height="88" rx="44" fill="url(#paint0_linear_0_2241)" />
	<rect x="0.5" y="0.5" width="88" height="88" rx="44" stroke="white" stroke-opacity="0.1" />
	<rect x="0.5" y="0.5" width="88" height="88" rx="44" stroke="url(#paint1_linear_0_2241)" />
	<rect x="0.5" y="0.5" width="88" height="88" rx="44" stroke="url(#paint2_linear_0_2241)" />
	<rect x="9" y="9" width="71" height="71" rx="35.5" fill="url(#paint3_linear_0_2241)" />
	<rect x="9.5" y="9.5" width="70" height="70" rx="35" stroke="url(#paint4_linear_0_2241)" />
	<g filter="url(#filter0_di_0_2241)">
		<path d="M57.5 51H51V57.5H56.8623C57.2144 57.5 57.5 57.2144 57.5 56.8623V51ZM41.5 57.5H48V51H41.5V57.5ZM51 48H57.5V41.5H51V48ZM41.5 48H48V41.5H41.5V48ZM32 48H38.5V41.5H32V48ZM41.5 38.5H48V32H41.5V38.5ZM32 56.8623C32 57.2144 32.2856 57.5 32.6377 57.5H38.5V51H32V56.8623ZM57.5 32.6377C57.5 32.2856 57.2144 32 56.8623 32H51V38.5H57.5V32.6377ZM32 38.5H38.5V32H32.6377C32.2856 32 32 32.2856 32 32.6377V38.5ZM60.5 56.8623C60.5 58.8713 58.8713 60.5 56.8623 60.5H32.6377C30.6288 60.5 29 58.8713 29 56.8623V32.6377C29 30.6288 30.6288 29 32.6377 29H56.8623C58.8713 29 60.5 30.6288 60.5 32.6377V56.8623Z" fill="url(#paint5_linear_0_2241)" style="mix-blend-mode:soft-light" shape-rendering="crispEdges" />
	</g>
	<defs>
		<filter id="filter0_di_0_2241" x="25" y="27" width="47.5" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="4" dy="6" />
			<feGaussianBlur stdDeviation="4" />
			<feComposite in2="hardAlpha" operator="out" />
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" />
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_2241" />
			<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_2241" result="shape" />
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
			<feOffset dx="2" dy="2" />
			<feGaussianBlur stdDeviation="2" />
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0" />
			<feBlend mode="normal" in2="shape" result="effect2_innerShadow_0_2241" />
		</filter>
		<linearGradient id="paint0_linear_0_2241" x1="86.7563" y1="89" x2="-7.85294" y2="-23.5588" gradientUnits="userSpaceOnUse">
			<stop offset="0.475138" />
			<stop offset="1" stop-color="#242D53" />
		</linearGradient>
		<linearGradient id="paint1_linear_0_2241" x1="-3.42308" y1="65.4354" x2="-25.9976" y2="83.0473" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0" />
			<stop offset="1" stop-color="white" stop-opacity="0.5" />
		</linearGradient>
		<linearGradient id="paint2_linear_0_2241" x1="87.1173" y1="43.1308" x2="115.358" y2="43.1308" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0.05" />
			<stop offset="1" stop-color="white" />
		</linearGradient>
		<linearGradient id="paint3_linear_0_2241" x1="44.5" y1="9" x2="65.8689" y2="80" gradientUnits="userSpaceOnUse">
			<stop offset="0.15117" />
			<stop offset="1" stop-color="#242D53" />
		</linearGradient>
		<linearGradient id="paint4_linear_0_2241" x1="44.5" y1="9" x2="24.5097" y2="80" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0.45" />
			<stop offset="0.379808" stop-color="#627AE6" />
			<stop offset="1" stop-color="white" stop-opacity="0" />
		</linearGradient>
		<linearGradient id="paint5_linear_0_2241" x1="58.2341" y1="29.9213" x2="29.0358" y2="59.4767" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" />
			<stop offset="0.519231" stop-color="#98A7EB" />
			<stop offset="1" stop-color="#465BBE" />
		</linearGradient>
	</defs>
</svg>
