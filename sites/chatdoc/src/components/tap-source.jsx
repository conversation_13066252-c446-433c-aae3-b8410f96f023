import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import CommonSwiper from '../../../../common/components/swiper/common-swiper';
import classnames from 'classnames';

import '../styles/tap-source.less';

const TapSource = () => {
  const { allTapSourceData } = useGlobalContext();
  console.log(allTapSourceData);
  return (
    <div className="tap-source-wrapper">
      <Container className="tap-source-container">
        <Row>
          <Col lg={6} md={12} className="tap-source-left">
            <h1 className="module-title">
              With <span>TapSource™</span> Answers and Origins Hold Hands.
            </h1>
          </Col>
          <Col lg={6} md={12} className="tap-source-right">
            <div className="tap-source-swiper-content">
              <div className='tap-source-images'>
                <CommonSwiper
                  className="tap-source-images-swiper"
                  swiperData={allTapSourceData}
                  centeredSlides={true}
                  swiperSlideChildren={(item, index) => (
                    <div className="tap-source-item-box" key={index}>
                      <GatsbyImage
                        image={item.image.childImageSharp.gatsbyImageData}
                        alt=""
                        className={classnames({
                          'tap-source-image': true,
                          'reverse-gatsby-image': index === 2,
                        })}
                      />
                      <p className="tap-source-name">{item.name}</p>
                    </div>
                  )}
                />
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default TapSource;
