import React from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import AutoSwitchCards from './auto-switch-cards';

import '../styles/smart-doc-panels.less';

const SmartDocPanels = () => {
  const { allSmartDocPanelsData } = useGlobalContext();
  return (
    <div className="panel-wrapper">
      <Container className="panel-container">
        <div className="panel-row">
          {allSmartDocPanelsData.map((item, index) => (
            <div className="panel-item" key={index}>
              {item.image?.childImageSharp.gatsbyImageData ? (
                <GatsbyImage
                  image={item.image.childImageSharp.gatsbyImageData}
                  alt=""
                  className="panel-image"
                />
              ) : (
                <AutoSwitchCards itemList={item.imagesList} />
              )}
              <pre className="panel-desc">{item.desc}</pre>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default SmartDocPanels;
