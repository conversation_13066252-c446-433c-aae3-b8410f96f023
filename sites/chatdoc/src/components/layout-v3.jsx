import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import SuggestBrowserTip from '../../../../common/components/suggestBrowserTip/index';
import Header from '../../../../common/components/chatdoc/header/header';
import { useGetUrlQueryData } from '../../../../common/hooks/useUrlQueryHook';
import { setChatDOCUID } from '../../../../common/utils/chatdoc/chatdoc-uid';
import { handleUpdateActivityStatistic } from '../../../../common/utils/chatdoc/activity-utils';

const Layout = ({
  className,
  children,
  currentHideFeatureInHeader,
  isIndexPage,
}) => {
  const src = useGetUrlQueryData('src');

  useEffect(() => {
    setChatDOCUID();
  }, []);

  useEffect(() => {
    if (isIndexPage) {
      handleUpdateActivityStatistic(src);
    }
  }, [isIndexPage, src]);

  return (
    <div className={className}>
      <SuggestBrowserTip
        locale="en"
        headerClass={'header-wrapper'}
      />
      <Header
        isIndexPage={isIndexPage}
        isV3
        currentHideFeatureInHeader={currentHideFeatureInHeader}
      />
      {children}
    </div>
  );
};

Layout.defaultProps = {
  hideAnnouncement: true,
};

Layout.propTypes = {
  hideFooter: PropTypes.bool,
  hideAnnouncement: PropTypes.bool,
  currentHideFeatureInHeader: PropTypes.bool,
  isIndexPage: PropTypes.bool,
  className: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

export default Layout;
