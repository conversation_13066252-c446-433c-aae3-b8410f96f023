import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import SuggestBrowserTip from '../../../../common/components/suggestBrowserTip/index';
import Announcement from './announcement';
import Header from '../../../../common/components/chatdoc/header/header';
import Footer from '../../../../common/components/chatdoc/footer/footer';
import { useGetUrlQueryData } from '../../../../common/hooks/useUrlQueryHook';
import { setChatDOCUID } from '../../../../common/utils/chatdoc/chatdoc-uid';
import { handleUpdateActivityStatistic } from '../../../../common/utils/chatdoc/activity-utils';

const Layout = ({
  className,
  children,
  hideFooter,
  hideAnnouncement,
  currentHideFeatureInHeader,
  isIndexPage,
}) => {
  const src = useGetUrlQueryData('src');

  useEffect(() => {
    setChatDOCUID();
  }, []);

  useEffect(() => {
    if (isIndexPage) {
      handleUpdateActivityStatistic(src);
    }
  }, [isIndexPage, src]);

  return (
    <div className={className}>
      <SuggestBrowserTip
        locale="en"
        headerClass={
          hideAnnouncement ? 'header-wrapper' : 'announcement-wrapper'
        }
      />
      {!hideAnnouncement && <Announcement />}
      <Header
        isIndexPage={isIndexPage}
        currentHideFeatureInHeader={currentHideFeatureInHeader}
      />
      {children}
      {!hideFooter && <Footer />}
    </div>
  );
};

Layout.defaultProps = {
  hideAnnouncement: true,
};

Layout.propTypes = {
  hideFooter: PropTypes.bool,
  hideAnnouncement: PropTypes.bool,
  currentHideFeatureInHeader: PropTypes.bool,
  isIndexPage: PropTypes.bool,
  className: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

export default Layout;
