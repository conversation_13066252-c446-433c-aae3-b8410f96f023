import React from 'react';
import { Container } from 'react-bootstrap';
import { GatsbyImage } from 'gatsby-plugin-image';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import CommonSwiper from '../../../../common/components/swiper/common-swiper';

import '../styles/explore.less';

const Explore = () => {
  const { allExploreData } = useGlobalContext();

  return (
    <div className="explore-wrapper">
      <Container className="explore-container">
        <h1 className="module-title">Explore More Hidden Gems!</h1>
        <div className="explore-swiper-content">
          <CommonSwiper
            className="explore-images-swiper"
            swiperData={allExploreData}
            slidesPerView={3.7}
            spaceBetween={30}
            navigation
            pagination={false}
            swiperSlideChildren={(item, index) => (
              <div key={index} className="explore-item-box">
                <div className="explore-image-box">
                  <GatsbyImage
                    image={item.image.childImageSharp.gatsbyImageData}
                    alt=""
                    className="explore-image"
                  />
                </div>
                <p className="explore-name">{item.name}</p>
              </div>
            )}
          />
        </div>
      </Container>
    </div>
  );
};

export default Explore;
