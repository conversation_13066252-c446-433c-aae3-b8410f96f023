import React, { useState } from 'react';
import { Container } from 'react-bootstrap';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import CommonSwiper from '../../../../common/components/swiper/common-swiper';
import ProjectButton from '../../../../common/components/chatdoc/button/project-button';
import { getUrlWithProduct } from '../../../../common/urls';
import '../styles/workflow.less';

const Workflow = () => {
  const { allWorkflowData } = useGlobalContext();

  const [activeIndex, setActiveIndex] = useState(0);
  const chatpaperProjectHref = getUrlWithProduct('chatpaper', 'project');

  const handleSlideChange = (swiper) => {
    const index = swiper.realIndex;
    setActiveIndex(index);
  };

  return (
    <div className="workflow-wrapper">
      <Container className="workflow-container">
        <h1 className="module-title">
          Your Workflow, <span>Supercharged</span>
        </h1>
        <div className="workflow-swiper-content">
          <CommonSwiper
            className="workflow-images-swiper"
            swiperData={allWorkflowData}
            centeredSlides={true}
            slidesPerView={3}
            onSlideChange={handleSlideChange}
            swiperSlideChildren={(item, index) => (
              <>
                {activeIndex === index && (
                  <div className="item-bottom-blue"></div>
                )}
                <div key={index} className="workflow-item">
                  <div className="workflow-item-box">
                    {activeIndex === index && (
                      <div className="item-number">{index + 1}</div>
                    )}

                    <div className="workflow-image-box">
                      <img
                        className="workflow-image"
                        src={item.image.publicURL}
                        alt=""
                      />
                    </div>
                    <p className="workflow-name">{item.name}</p>
                  </div>
                </div>
              </>
            )}
          />
        </div>
        <ProjectButton text="Try ChatPaper" isV3 href={chatpaperProjectHref} />
      </Container>
    </div>
  );
};

export default Workflow;
