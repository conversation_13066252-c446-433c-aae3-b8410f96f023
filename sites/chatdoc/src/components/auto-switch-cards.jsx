import React, { useState } from 'react';
import { GatsbyImage } from 'gatsby-plugin-image';
import CommonSwiper from '../../../../common/components/swiper/common-swiper';
import RightCircleIcon from '../assets/icons/right-circle.svg';
import TaperLine from '../assets/images/taper-line.png';
import '../styles/auto-switch-cards.less';

const AutoSwitchVerticalCards = ({ itemList }) => {
  const [activeItem, setActiveItem] = useState(null);

  const handleSlideChange = (swiper) => {
    const index = swiper.realIndex;
    setActiveItem(itemList[index] || itemList[0]);
  };

  return (
    <div className="vertical-cards-container">
      <CommonSwiper
        className="comments-images-swiper"
        swiperData={itemList}
        centeredSlides={true}
        direction="vertical"
        slidesPerView={3}
        onSlideChange={handleSlideChange}
        swiperSlideChildren={(item) => (
          <div className="card-name" key={item.name}>
            {item.name}
            <div className="name-right-line"></div>
          </div>
        )}
      />
      <img className="taper-line" src={TaperLine} alt="" />
      {activeItem && (
        <div className="vertical-card-right">
          <img
            className="card-type-icon"
            src={activeItem.icon.publicURL}
            alt=""
          />
          <div className="active-content">
            <p>
              <RightCircleIcon className="right-circle-icon" />
              {activeItem.name}
            </p>
            <GatsbyImage
              image={activeItem.image.childImageSharp.gatsbyImageData}
              alt=""
              className="panel-image"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AutoSwitchVerticalCards;
