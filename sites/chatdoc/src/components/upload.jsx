import React from 'react';
import { Container } from 'react-bootstrap';
import ProjectButton from '../../../../common/components/chatdoc/button/project-button';

import '../styles/upload.less';

const SmartDocPanels = () => {
  return (
    <div className="upload-wrapper">
      <Container className="upload-container">
        <div className="upload-row">
          <h1 className="module-title">
            Doubt it? <br />
            Try Yourself!
          </h1>
          <div className="upload-box">
            <div className="iframe-box"></div>
          </div>
          <p className="upload-desc">
            100 questions free · No card needed · Cancel anytime
          </p>
          <ProjectButton isV3 text="Sign Up for Free" />
        </div>
      </Container>
    </div>
  );
};

export default SmartDocPanels;
