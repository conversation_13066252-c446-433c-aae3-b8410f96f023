.tap-source-wrapper {
  margin-top: 180px;

  .row {
    position: relative;
  }

  .module-title {
    margin-top: 69px;

    span {
      background: linear-gradient(86deg, #627ae6 40.34%, #d6b0bc 71.07%);
      background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tap-source-right {
    position: relative;
    &::before {
      position: absolute;
      top: -30px;
      left: -30px;
      width: 171px;
      height: 185px;
      background: radial-gradient(
        ellipse at center,
        rgba(75, 183, 90, 30%) 60%,
        transparent 100%
      );
      filter: blur(100px);
      content: '';
      z-index: 0;
    }
    &::after {
      position: absolute;
      right: 0px;
      bottom: 0px;
      width: 89px;
      height: 300px;
      background: radial-gradient(
        ellipse at center,
        rgba(98, 122, 230, 40%) 30%,
        transparent 100%
      );
      filter: blur(50px);
      content: '';
      z-index: 0;
    }
  }

  .tap-source-swiper-content {
    position: relative;
    background: linear-gradient(130deg, #000 1%, #222 114.3%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 16px;
    z-index: 1;
    &::before {
      position: absolute;
      top: -1px;
      left: -1px;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #4bb75a 0%, transparent 20%) top left /
        200px 200px no-repeat;
      border-radius: 18px;
      content: '';
      z-index: 0;
    }
    &::after {
      position: absolute;
      right: -1px;
      bottom: 80px;
      width: 1px;
      height: 117px;
      background: linear-gradient(
        to bottom,
        rgba(98, 122, 230, 0%) 0%,
        #627ae6 50%,
        rgba(98, 122, 230, 0%) 100%
      );
      content: '';
      z-index: 1;
    }
    .tap-source-images {
      position: relative;
      padding: 73px 35px 65px;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border-radius: 16px;
      z-index: 1;
    }

    .tap-source-name {
      margin-top: 28px;
      font-weight: 600;
      font-size: 32px;
      font-family: Poppins-blod;
      line-height: 38px;
      text-align: center;
      background: linear-gradient(
        88deg,
        #fff 2.84%,
        rgba(255, 255, 255, 80%) 76.02%
      );
      background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .tap-source-item-box {
      perspective: 1000px;
      perspective-origin: center center;
    }

    .tap-source-image {
      transform: rotateX(0deg) rotateY(0deg) translateZ(0);
      transform-style: preserve-3d;
      transition: all 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        transform: rotateX(10deg) rotateY(15deg) translateZ(-20px) scale(0.98);
      }
    }

    .reverse-gatsby-image {
      &:hover {
        transform: rotateX(8deg) rotateY(-26deg) translateZ(20px) scale(0.98);
      }
    }
  }

  .tap-source-images-swiper {
    position: static;
  }

  .swiper-pagination {
    position: absolute;
    bottom: 50px;
    left: -104%;
    width: 400px;
    height: 4px;
    padding: 0 30px;
    line-height: 0;
    border-radius: 100px;

    &::before {
      position: absolute;
      top: 3px;
      left: 0;
      color: #fff;
      font-size: 18px;
      content: '01';
    }

    &::after {
      position: absolute;
      top: 3px;
      right: 0;
      color: #fff;
      font-size: 18px;
      content: '03';
    }

    .swiper-pagination-bullet {
      width: 33.3%;
      height: 4px;
      margin: 0 !important;
      background: rgba(255, 255, 255, 50%);
      border-radius: 0;
    }

    .swiper-pagination-bullet-active {
      background: #627ae6;
      border-radius: 100px;
    }
  }
}
