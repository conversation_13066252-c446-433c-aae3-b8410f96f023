.panel-wrapper {
  position: relative;
  padding: 134px 0;

  .panel-container {
    &::before {
      position: absolute;
      top: 60px;
      left: 50px;
      width: 171px;
      height: 185px;
      background: radial-gradient(
        ellipse at center,
        rgba(75, 183, 90, 30%) 60%,
        transparent 100%
      );
      filter: blur(100px);
      content: '';
    }

    &::after {
      position: absolute;
      right: 10px;
      bottom: 350px;
      z-index: 0;
      width: 89px;
      height: 300px;
      background: radial-gradient(
        ellipse at center,
        rgba(98, 122, 230, 30%) 30%,
        transparent 100%
      );
      filter: blur(50px);
      content: '';
    }
  }

  .panel-row {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    gap: 30px;

    &::before {
      position: absolute;
      top: 30px;
      left: 0;
      z-index: 2;
      width: 1px;
      height: 117px;
      background: linear-gradient(
        to bottom,
        rgba(75, 183, 90, 0%) 0%,
        rgba(75, 183, 90, 100%) 50%,
        rgba(75, 183, 90, 0%) 100%
      );
      content: '';
    }

    &::after {
      position: absolute;
      right: 0;
      bottom: 245px;
      z-index: 2;
      width: 1px;
      height: 117px;
      background: linear-gradient(
        to bottom,
        rgba(98, 122, 230, 0%) 0%,
        #627ae6 50%,
        rgba(98, 122, 230, 0%) 100%
      );
      content: '';
    }
  }

  .panel-item {
    position: relative;
    z-index: 1;
    width: calc(57% - 30px);
    height: 510px;
    padding: 45px 40px;
    background: linear-gradient(129deg, #000 0.67%, #222 116.37%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 16px;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 25%);

    .panel-desc {
      position: absolute;
      bottom: 40px;
      left: 40px;
      color: rgba(255, 255, 255, 80%);
      font-weight: 600;
      font-size: 32px;
      font-family: Poppins-Regular;
      line-height: 1.4;
    }

    &:first-child,
    &:last-child {
      width: 43%;
      padding-bottom: 165px;

      .panel-image {
        width: 82%;
        height: auto;
        margin-left: 9%;
      }
    }

    &:first-child {
      padding: 0 0 45px;

      .panel-image {
        width: auto;
        height: 100%;
      }
    }

    &:nth-child(2) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background: linear-gradient(123deg, #000 1.15%, #222 105.64%);

      .panel-image {
        position: relative;
        top: -40px;
        width: 75%;
      }
    }

    &:nth-child(3) {
      .panel-desc {
        bottom: 35px;
      }
    }
  }
}
