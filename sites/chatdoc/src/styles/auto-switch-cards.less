.vertical-cards-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;

  .taper-line {
    position: absolute;
    left: 2.5%;
    z-index: 0;
    width: auto;
    height: 90%;
    margin-top: 10px;
  }

  .common-swiper {
    width: 245px;
    height: 80%;
    margin: 0;
    color: rgba(255, 255, 255, 60%);
    font-size: 12px;
  }

  .swiper-pagination {
    display: none;
  }

  .swiper-slide-active {
    padding-right: 40px;
  }

  .card-name {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .name-right-line {
      display: block;
      width: 38px;
      height: 1px;
      margin-left: 24px;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 40%) 0%,
        rgba(255, 255, 255, 0%) 100%
      );
      content: '';
    }
  }

  .vertical-card-right {
    z-index: 1;
    display: flex;
    flex: 1;
    margin-left: 2%;

    .card-type-icon {
      width: 89px;
      height: auto;
      margin-right: 22px;
    }

    .active-content {
      flex: 1;

      p {
        margin-bottom: 20px;
        color: rgba(255, 255, 255, 60%);
        font-size: 14px;
      }

      .right-circle-icon {
        margin-right: 5px;
      }

      .panel-image {
        width: auto;
        max-height: 150px;
      }
    }
  }
}
