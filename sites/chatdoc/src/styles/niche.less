.niche-wrapper {
  .niche-content {
    position: relative;
    display: flex;
    overflow: hidden;
  }

  .niche-title {
    width: 400px;
    margin-right: 100px;

    h1 {
      font-size: 55px;
      line-height: 57px;
    }

    p {
      margin-top: 48px;
      color: rgba(255, 255, 255, 60%);
      font-size: 20px;
      line-height: 28px;
    }
  }

  .niche-list-content {
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  .niche-list {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    width: 49%;
    height: 530px;

    .niche-item {
      width: 100%;
      height: 100px;
      margin-bottom: 30px;
      color: #fff;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 16px;
      transition: all 0.2s;

      &:last-child {
        margin-top: auto;
      }
    }

    .niche-item--lefted {
      position: absolute;
      bottom: 10px;
      left: calc(-100% - 30px);
    }

    &#niche-list-1 {
      .niche-item--lefted {
        left: calc(-200% - 30px);
      }
    }

    .niche-item--expanded {
      position: relative;
      height: 231px;
      &::before {
        position: absolute;
        bottom: -1px;
        left: -1px;
        width: 100%;
        height: 100%;
        background: linear-gradient(40deg, #4bb75a 0%, transparent 30%) bottom
          left / 100px 100px no-repeat;
        border-radius: 18px;
        content: '';
        z-index: 0;
      }
      &::after {
        position: absolute;
        right: -1px;
        top: 26px;
        width: 1px;
        height: 80px;
        background: linear-gradient(
          to bottom,
          rgba(98, 122, 230, 0%) 0%,
          #627ae6 50%,
          rgba(98, 122, 230, 0%) 100%
        );
        box-shadow: 0px 0px 80px 20px rgba(98, 122, 230, 0.3);
        content: '';
        z-index: 0;
      }
      .niche-item-box {
        align-items: flex-start;
      }
      .niche-info {
        margin-top: auto;

        p {
          display: block;
        }
      }
    }
    .niche-item-box {
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26px 21px;
      background: linear-gradient(130deg, #000 1%, #222 114.3%);
      border-radius: 16px;
    }

    .niche-image {
      width: 48px;
    }

    .niche-info {
      p {
        display: none;
      }
    }
  }
}
