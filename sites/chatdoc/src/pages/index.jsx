import React from 'react';
import ChatDOCSEO from '../components/seo';
import Layout from '../components/layout-v3';
import Banner from '../../../../common/components/chatdoc/v3banner/banner';
import SmartDocPanels from '../components/smart-doc-panels';
import Upload from '../components/upload';
import TapSource from '../components/tap-source';
import Workflow from '../components/workflow';
import Explore from '../components/explore';
import Niche from '../components/niche';
import Pricing from '../../../../common/components/chatdoc/pricing/pricing';
import GitInfo from '../../../../common/components/gitInfo/git-info';
import { useGlobalContext } from '../../../../common/hooks/useGlobalHook';
import { useChatDOCIdle } from '../../../../common/hooks/chatdoc/useChatDOCIdle';
import { useGoogleLogin } from '../../../../common/hooks/chatdoc/useGoogleLogin';
import '../styles/index.less';

export const Head = () => <ChatDOCSEO />;

const IndexPage = () => {
  useChatDOCIdle();
  useGoogleLogin();

  const { gitCommitData } = useGlobalContext();

  return (
    <>
      <Layout
        className="chatdoc-index-page"
        isIndexPage
        hideFooter>
        <main>
          <Banner />
          <SmartDocPanels />
          <Upload />
          <TapSource />
          <Workflow />
          <Explore />
          <Niche />
          <Pricing classNames="chatdoc-v3-pricing" />
        </main>
      </Layout>
      <GitInfo data={gitCommitData} />
    </>
  );
};

export default IndexPage;
