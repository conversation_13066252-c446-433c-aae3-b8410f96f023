<svg xmlns="http://www.w3.org/2000/svg" width="771" height="785" viewBox="0 0 771 785" fill="none">
  <g filter="url(#filter0_f_0_1245)">
    <ellipse cx="385.5" cy="392.5" rx="85.5" ry="92.5" fill="#4BB75A" fill-opacity="0.3"/>
  </g>
  <g filter="url(#filter1_f_0_1245)">
    <ellipse cx="384.205" cy="392.5" rx="42.75" ry="45.9731" fill="#4BB75A" fill-opacity="0.3"/>
  </g>
  <g style="mix-blend-mode:hard-light" filter="url(#filter2_f_0_1245)">
    <ellipse cx="384.205" cy="392.5" rx="42.75" ry="45.9731" fill="#4BB75A" fill-opacity="0.3"/>
  </g>
  <defs>
    <filter id="filter0_f_0_1245" x="0" y="0" width="771" height="785" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_0_1245"/>
    </filter>
    <filter id="filter1_f_0_1245" x="241.455" y="246.527" width="285.5" height="291.946" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1245"/>
    </filter>
    <filter id="filter2_f_0_1245" x="241.455" y="246.527" width="285.5" height="291.946" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_0_1245"/>
    </filter>
  </defs>
</svg>