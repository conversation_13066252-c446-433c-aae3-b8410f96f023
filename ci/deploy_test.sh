#!/usr/bin/env bash

set -ex

# chatdoc
if [[ -n "${ENABLE_CHATDOC_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-chatdoc-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/
  rsync -av --delete public-chatdoc-cdn/ docker_test@bohr:/data/chatdoc_officialweb/public/
else
  rsync -av --delete public-chatdoc/ docker_test@bohr:/data/chatdoc_officialweb/public/
fi

# pdfparser
rsync -av --delete public-pdfparser/ docker_test@bohr:/data/pdfparser_officialweb/public/

# paodingjiewen
rsync -av --delete public-paodingjiewen/ docker_test@bohr:/data/chatdoc_cn/public/

# pdfchat
if [[ -n "${ENABLE_PDFCHAT_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-pdfchat-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/
  rsync -av --delete public-pdfchat-cdn/ docker_test@bohr:/data/pdfchat_officialweb/public/
else
  rsync -av --delete public-pdfchat/ docker_test@bohr:/data/pdfchat_officialweb/public/
fi

# chatdb
rsync -av --delete public-chatdb/ docker_test@bohr:/data/chatdb_officialweb/public/

# pdflux
if [[ -n "${ENABLE_PDFLUX_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html --exclude=/**/*.zst --exclude=/*.zst --exclude=/**/*.gz --exclude=/*.gz \
      public-pdflux-cdn/ aliyun-cdn-paodingai-com-oss:/cdn-paodingai/pdflux/
  rsync -av --delete public-pdflux-cdn/ docker_test@bohr:/data/pdflux_officialweb/public/
else
  rsync -av --delete public-pdflux/ docker_test@bohr:/data/pdflux_officialweb/public/
fi

# paodingai
rsync -av --delete public-paodingai/ docker_test@bohr:/data/OfficialWeb_test/

# calliper
rsync -av --delete public-calliper/ docker_test@bohr:/data/calliper_officialweb/public/

# calliper_vip
rsync -av --exclude=/logo.png --delete public-calliper-vip/ docker_test@bohr:/data/calliper_enterpriseweb/

# pdflux sdk
rsync -av --delete public-pdflux-sdk/ docker_test@bohr:/data/pdflux_sdk/public/

# grater vip
rsync -av --delete public-grater-vip/ docker_test@bohr:/data/grater_officialweb/

# grater
rsync -av --delete public-grater/ docker_test@bohr:/data/grater_personal_officialweb/

# semanmeter
rsync -av --delete public-semanmeter/ docker_test@bohr:/data/semanmeter_officialweb/

# scriber
rsync -av --delete public-scriber/ docker_test@bohr:/data/scriber_officialweb/public/

# autodoc
rsync -av --delete public-autodoc/ docker_test@bohr:/data/autodoc_master/official_front/

# autodoc mvip
rsync -av --delete public-autodoc-vip/ ci@c122:/data/autodoc_py3/autodoc-vip-front/
rsync -av --delete public-autodoc-vip/ farm@c11:/data/autodoc_overall_review_2/autodoc-vip-front/

# autodoc next
rsync -av --delete public-autodoc-next/ docker_test@bohr:/data/autodoc_master/autodoc-next_official_front/

# glazer
rsync -av --delete public-glazer/ ci@c2:/data/glazer_test/official_front

# hunter
rsync -av --delete public-hunter/ docker_test@bohr:/data//hunter_officialweb/public/

# metalmesh
rsync -av --delete public-metalmesh/ docker_test@bohr:/data/metalmesh_officialweb/public/

curl http://mm.paodingai.com/hooks/zxg3ncokc3yuxfymyrco7zctta \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d @- <<EOF
{
  "text": "官网测试环境前端已更新:\`${GO_REVISION_PAODING_OFFICIALWEB:0:8}(${GO_MATERIAL_BRANCH_PAODING_OFFICIALWEB})\`",
  "props": {
  "card": "
| [AutoDoc官网](http://**********:8076/) |\n
| --- |\n
| [AutoDoc-next官网](https://autodoc-next.test.paodingai.com/) |\n
| [AutoDoc-vip官网](http://**********:65176/) |\n
| [Calliper官网](https://bohr.cheftin.cn:8073/) |\n
| [Calliper-VIP官网](https://bohr.cheftin.cn:8083/) |\n
| [ChatDB官网](https://**********:8082/) |\n
| [ChatDOC官网](http://bohr.cheftin.cn:8081/) |\n
| [Glazer官网](https://bohr.cheftin.cn:8097/) |\n
| [Grater官网](https://bohr.cheftin.cn:8077/) |\n
| [Grater个人版官网](https://**********:8078/) |\n
| [Hunter官网](https://bohr.cheftin.cn:8074/) |\n
| [MetalMesh官网](https://bohr.cheftin.cn:8102/) |\n
| [Paoding官网](https://bohr.cheftin.cn:5081/) |\n
| [Paodingjiewen官网](https://bohr.cheftin.cn:8801/) |\n
| [PDFChat官网](http://bohr.cheftin.cn:8099/) |\n
| [PDFlux官网](https://bohr.cheftin.cn:8071/) |\n
| [PDFlux SDK官网](https://bohr.cheftin.cn:8075/) |\n
| [PDFParser官网](https://bohr.cheftin.cn:8072/) |\n
| [Semanmeter官网](https://bohr.cheftin.cn:8079/) |\n
| [Scriber官网](https://bohr.cheftin.cn:8070/) | \n"
  },
"channel": "official-website",
"icon_url": "http://res.cloudinary.com/kdr2/image/upload/c_crop,g_faces,h_240,w_240/v1454772214/misc/c3p0-001.jpg",
"username": "CI"
}
EOF
