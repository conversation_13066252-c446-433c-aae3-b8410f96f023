#!/usr/bin/env bash

set -ex

if [[ -n "${ENABLE_PDFCHAT_CDN}" ]]; then
  # deploy pdfchat.com
    rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-pdfchat-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/

#  rsync -rlpgocDv --exclude=/index.html public-pdfchat-cdn/ chatdoc@100.64.0.203:/data/cdn/chatdoc/
  rsync -av --delete public-pdfchat-cdn/ chatdoc@100.64.0.203:/data/pdfchat_official/

else
  # deploy pdfchat.com
  rsync -av --delete public-pdfchat/ chatdoc@100.64.0.203:/data/pdfchat_official/

fi

if [ -f "/data/ci/fitout/autodoc/send_mm_msg.sh" ]; then
  bash /data/ci/fitout/autodoc/send_mm_msg.sh http://mm.paodingai.com/hooks/xffd4wkndpnjubqd9z9puzoxaa official-website \[PDFChat官网生产环境\]\(https://pdfchat.com/\)前端已更新至版本\:\`${GO_REVISION_PAODING_OFFICIALWEB:0:8}\(${GO_MATERIAL_BRANCH_PAODING_OFFICIALWEB}\)\`
fi
