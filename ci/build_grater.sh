#!/usr/bin/env bash

set -ex

rm -rf sites/grater/public* public*

yarn install

# BUILD_POSTFIX set by gocd, default build for prod. if build for test, add BUILD_POSTFIX=-test on gocd.
yarn build:grater-vip"${BUILD_POSTFIX}"

gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/grater/public

mv sites/grater/public public-grater-vip

yarn build:grater"${BUILD_POSTFIX}"

gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/grater/public

mv sites/grater/public public-grater
