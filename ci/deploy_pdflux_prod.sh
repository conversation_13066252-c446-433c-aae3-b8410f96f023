#!/usr/bin/env bash

set -ex

if [[ -n "${ENABLE_PDFLUX_CDN}" ]]; then
  rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html --exclude=/**/*.zst --exclude=/*.zst --exclude=/**/*.gz --exclude=/*.gz \
      public-pdflux-cdn/ aliyun-cdn-paodingai-com-oss:/cdn-paodingai/pdflux/
  rsync -av --delete public-pdflux-cdn/ pdflux@adams:/home/<USER>/PDFlux_official/
else
  rsync -av --exclude=/logo.png --delete public-pdflux/ pdflux@adams:/home/<USER>/PDFlux_official/
fi

if [ -f "/data/ci/fitout/autodoc/send_mm_msg.sh" ]; then
  bash /data/ci/fitout/autodoc/send_mm_msg.sh http://mm.paodingai.com/hooks/xffd4wkndpnjubqd9z9puzoxaa official-website \
    "[PDFlux官网生产环境](https://pdflux.com/)前端已更新至版本:\`${GO_REVISION_PAODING_OFFICIALWEB:0:8}(${GO_MATERIAL_BRANCH_PAODING_OFFICIALWEB})\` @humingchi"
fi
