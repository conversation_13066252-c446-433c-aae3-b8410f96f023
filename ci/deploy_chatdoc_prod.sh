#!/usr/bin/env bash

set -ex

if [[ -n "${ENABLE_CHATDOC_CDN}" ]]; then
    rclone copy --ignore-existing -P --timeout 600s \
      --exclude=/index.html public-chatdoc-cdn/ aliyun-cdn-pdppt-com-oss:/cdn-pdppt/chatdoc/
#  rsync -rlpgocDv --exclude=/index.html public-chatdoc-cdn/ chatdoc@100.64.0.203:/data/cdn/chatdoc/
  rsync -av --delete public-chatdoc-cdn/ chatdoc@100.64.0.203:/data/chatdoc_official/

  # deploy marvin chatdoc.site
  rsync -av --delete public-chatdoc-cdn/ chatdoc@100.64.0.221:/data/chatdoc_official/

else
  rsync -av --delete public-chatdoc/ chatdoc@100.64.0.203:/data/chatdoc_official/

  # deploy marvin chatdoc.site
  rsync -av --delete public-chatdoc/ chatdoc@100.64.0.221:/data/chatdoc_official/

fi

if [ -f "/data/ci/fitout/autodoc/send_mm_msg.sh" ]; then
  bash /data/ci/fitout/autodoc/send_mm_msg.sh http://mm.paodingai.com/hooks/xffd4wkndpnjubqd9z9puzoxaa official-website \[ChatDOC官网生产环境\]\(https://chatdoc.com/\)前端已更新至版本\:\`${GO_REVISION_PAODING_OFFICIALWEB:0:8}\(${GO_MATERIAL_BRANCH_PAODING_OFFICIALWEB}\)\`
fi
