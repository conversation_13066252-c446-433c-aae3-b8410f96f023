#!/usr/bin/env bash

set -ex
set -o pipefail

rm -rf sites/pdflux/public*

yarn install

if [[ -n "${ENABLE_PDFLUX_CDN}" ]]; then
  yarn build:pdflux-cdn"${BUILD_POSTFIX}"
  # replace https://cdn.paodingai.com/pdflux/page-data/app-data.json to /page-data/app-data.json
  ls -1 sites/pdflux/public/app-*.js | xargs -I {} sed -i "s@https://cdn.paodingai.com/pdflux/page-data@/page-data@g" {}

  # add args for app.js
  ts=$(date +%s)
  find sites/pdflux/public -name '*.html' -type f | xargs -I {} sed -Ei \
    's@https://cdn.paodingai.com/pdflux/app-(.*?).js@https://cdn.paodingai.com/pdflux/app-\1.js?_t='"${ts}"'@g' {}
  gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/pdflux/public
  mv sites/pdflux/public public-pdflux-cdn
fi

# BUILD_POSTFIX set by gocd, default build for prod. if build for test, add BUILD_POSTFIX=-test on gocd.
yarn build:pdflux"${BUILD_POSTFIX}"
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/pdflux/public
mv sites/pdflux/public public-pdflux
