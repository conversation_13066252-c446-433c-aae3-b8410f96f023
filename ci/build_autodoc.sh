#!/usr/bin/env bash

set -ex

rm -rf public* sites/autodoc/public

yarn install

yarn build:autodoc-vip"${BUILD_POSTFIX}"
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/autodoc/public
mv sites/autodoc/public public-autodoc-vip

# BUILD_POSTFIX set by gocd, default build for prod. if build for test, add BUILD_POSTFIX=-test on gocd.
yarn build:autodoc"${BUILD_POSTFIX}"
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/autodoc/public
mv sites/autodoc/public public-autodoc

yarn build:autodoc-next"${BUILD_POSTFIX}"
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/autodoc/public
mv sites/autodoc/public public-autodoc-next
