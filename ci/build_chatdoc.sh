#!/usr/bin/env bash

set -ex
set -o pipefail

rm -rf sites/chatdoc/public* public*

yarn install

remove_script_src_in_notfound() {
  sed -i 's@<script src=".*" async></script>@@g' sites/chatdoc/public/notFound/index.html
  sed -i 's@<script src=".*" async></script>@@g' sites/chatdoc/public/notFound/m/index.html
}

# BUILD_POSTFIX set by gocd, default build for prod. if build for test, add BUILD_POSTFIX=-test on gocd.
yarn build:chatdoc"${BUILD_POSTFIX}"
remove_script_src_in_notfound
gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/chatdoc/public
mv sites/chatdoc/public public-chatdoc

if [[ -n "${ENABLE_CHATDOC_CDN}" ]]; then
  yarn build:chatdoc-cdn"${BUILD_POSTFIX}"
  # replace https://cdn.pdppt.com/chatdoc/page-data/app-data.json to /page-data/app-data.json
  ls -1 sites/chatdoc/public/app-*.js | xargs -I {} sed -i "s@https://cdn.pdppt.com/chatdoc/page-data@/page-data@g" {}

  # add args for app.js
  ts=$(date +%s)
  find sites/chatdoc/public -name '*.html' -type f | xargs -I {} sed -Ei \
    's@https://cdn.pdppt.com/chatdoc/app-(.*?).js@https://cdn.pdppt.com/chatdoc/app-\1.js?_t='"${ts}"'@g' {}

  remove_script_src_in_notfound

  gzipper compress --gzip --gzip-level=5 --zstd --zstd-level=3 sites/chatdoc/public

  mv sites/chatdoc/public public-chatdoc-cdn
fi
